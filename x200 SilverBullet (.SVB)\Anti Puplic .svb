[SETTINGS]
{
  "Name": "Anti Puplic ",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T01:51:17.4902729+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@nibirupws",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Anti Puplic ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#POST_API REQUEST POST "https://digibody.avast.com/v1/web/leaks" 
  CONTENT "{\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: digibody.avast.com" 
  HEADER "Origin: https://www.avast.com" 
  HEADER "Referer: https://www.avast.com/hackcheck/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.89 Safari/537.36" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"status\":\"ok\",\"value\":[]}" 
  KEYCHAIN Failure OR 
    KEY "leak_id" 

#DATA FUNCTION Constant "<USER>:<PASS>" -> CAP "CAP" 

#STATUS PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "Status:" 

UTILITY File "ANTIPUBLIC COMBO.txt" Append "<USER>:<PASS>\\n" -> VAR "REG" 

